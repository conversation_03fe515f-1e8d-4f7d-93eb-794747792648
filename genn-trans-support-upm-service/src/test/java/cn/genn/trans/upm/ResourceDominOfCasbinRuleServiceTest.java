package cn.genn.trans.upm;

import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.domain.upm.service.ResourceDominOfCasbinRuleService;
import org.casbin.adapter.RedisAdapter;
import org.casbin.jcasbin.main.Enforcer;
import org.casbin.jcasbin.model.Model;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@SpringBootTest(classes = Application.class)
class ResourceDominOfCasbinRuleServiceTest {

    @InjectMocks
    private ResourceDominOfCasbinRuleService resourceDominOfCasbinRuleService;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private Enforcer enforcer;

    @Mock
    private RedisAdapter redisAdapter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void convertUrlOfResourceToCasbinRule() {
        // Arrange
        List<UpmRole> upmRoleList = Collections.singletonList(
            UpmRole.builder()
                .id(1L)
                .name("admin")
                .resourceList(Collections.singletonList(
                    new UpmResource() // Assuming UpmResource has appropriate constructor or setter methods
                ))
                .build()
        );

        when(roleRepository.queryAllRoleAndResource()).thenReturn(upmRoleList);

        // Act
        resourceDominOfCasbinRuleService.convertUrlOfResourceToCasbinRule();

        // Assert
        verify(enforcer, times(1)).addPolicy(anyString(), anyString(), anyString());
        verify(enforcer, times(1)).addRoleForUser(anyString(), anyString());
        verify(redisAdapter, times(1)).savePolicy(any(Model.class));
    }
}
