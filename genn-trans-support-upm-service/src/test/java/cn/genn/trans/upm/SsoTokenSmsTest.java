package cn.genn.trans.upm;

import cn.genn.trans.upm.application.dto.UpmAuthChangeEventDimensionEnum;
import cn.genn.trans.upm.application.service.action.SpringEventPublishService;
import cn.genn.trans.upm.application.service.action.SsoTokenActionService;
import cn.genn.trans.upm.infrastructure.utils.SsoTokenUtil;
import cn.genn.trans.upm.interfaces.api.ISsoAuthClient;
import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.query.SsoAccountLoginQuery;
import cn.genn.trans.upm.interfaces.query.UpmCheckUriPermissionQuery;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @Date: 2024/6/6
 * @Author: kangjian
 */
@SpringBootTest(classes = Application.class)
@Slf4j
public class SsoTokenSmsTest {

    @Resource
    private SsoTokenActionService ssoTokenActionService;

    @Test
    void sendSms() throws InterruptedException {
        ssoTokenActionService.sendSms("***********", "login");
    }

    @Test
    void genarateToken() {
        String token = ssoTokenActionService.generateTokenByUserId(13212346877L);
        log.info("token:{}", token);
    }

    @Test
    void getUser() {
        String token = "ea72666a-7f22-45f6-9b6d-4ee8dd54c5ab";
        LoginUserAuthInfoDTO authInfoDTO = SsoTokenUtil.getLoginUserAuthInfoFormToken(token);
        System.out.println(authInfoDTO);
    }


}
