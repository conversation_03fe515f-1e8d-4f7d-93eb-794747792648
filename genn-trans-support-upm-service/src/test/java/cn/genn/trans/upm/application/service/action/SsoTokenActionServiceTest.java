//package cn.genn.trans.upm.application.service.action;
//
//import cn.genn.core.exception.CheckException;
//import cn.genn.trans.upm.Application;
//import cn.genn.trans.upm.application.assembler.UpmUserLoginAssembler;
//import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
//import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
//import cn.genn.trans.upm.domain.upm.repository.UserRepository;
//import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
//import cn.genn.trans.upm.interfaces.dto.UserAccountInfoDTO;
//import cn.genn.trans.upm.domain.upm.service.ResourceDominOfCasbinRuleService;
//import cn.hutool.core.util.RandomUtil;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//import java.util.List;
//
//import static org.mockito.Mockito.*;
//import static org.junit.jupiter.api.Assertions.*;
//
//@SpringBootTest(classes = Application.class)
//public class SsoTokenActionServiceTest {
//
////    @InjectMocks
//    private SsoTokenActionService ssoTokenActionService;
//
////    @Mock
//    private UserRepository userRepository;
//
////    @Mock
//    private SsoAccountActionService upmUserService;
//
//    @BeforeEach
//    void setUp() {
////        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void ssoAccountdoLogin_Success() {
//        // Arrange
//        String username = "kangjian";
//        String password = "password123";
//        String productCode = "productA";
//        Long accountId = 1L;
//
//        UserAccountInfoDTO accountInfo = UserAccountInfoDTO.builder()
//            .accountId(accountId)
//            .username(username)
//            .password("hashedPassword")
//            .salt("salt")
//            .telephone("**********")
//            .build();
//
//        UpmUser upmUser = UpmUser.builder()
//            .accountId(accountId)
//            .build();
//
//        List<UpmUser> upmUserList = Arrays.asList(upmUser);
//
//        when(upmUserService.queryAccountInfoByUserName(username)).thenReturn(accountInfo);
//        when(upmUserService.checkPassword(password, accountInfo)).thenReturn(true);
//        when(userRepository.queryListByAccountId(accountId)).thenReturn(upmUserList);
//
//        // Act
//        LoginUserAccountDTO result = ssoTokenActionService.ssoAccountdoLogin(username, password, productCode);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(username, result.getUsername());
//        assertEquals(accountId.toString(), result.getAccountId());
//        assertNotNull(result.getTicket());
//        assertNotNull(result.getUserDeviceIdentify());
//        assertEquals(upmUserList.size(), result.getBindUserList().size());
//
//        // Verify that the methods were called with expected parameters
//        verify(upmUserService).queryAccountInfoByUserName(username);
//        verify(upmUserService).checkPassword(password, accountInfo);
//        verify(userRepository).queryListByAccountId(accountId);
//    }
//
//    @Test
//    void ssoAccountdoLogin_InvalidUsername() {
//        // Arrange
//        String username = "invalidUser";
//        String password = "password123";
//        String productCode = "productA";
//
//        when(upmUserService.queryAccountInfoByUserName(username)).thenReturn(null);
//
//        // Act & Assert
//        assertThrows(CheckException.class, () -> {
//            ssoTokenActionService.ssoAccountdoLogin(username, password, productCode);
//        });
//
//        // Verify
//        verify(upmUserService).queryAccountInfoByUserName(username);
//        verify(upmUserService, never()).checkPassword(anyString(), any());
//        verify(userRepository, never()).queryListByAccountId(anyLong());
//    }
//
//    @Test
//    void ssoAccountdoLogin_InvalidPassword() {
//        // Arrange
//        String username = "testUser";
//        String password = "wrongPassword";
//        String productCode = "productA";
//
//        UserAccountInfoDTO accountInfo = UserAccountInfoDTO.builder()
//            .accountId(1L)
//            .username(username)
//            .password("hashedPassword")
//            .salt("salt")
//            .telephone("**********")
//            .build();
//
//        when(upmUserService.queryAccountInfoByUserName(username)).thenReturn(accountInfo);
//        when(upmUserService.checkPassword(password, accountInfo)).thenReturn(false);
//
//        // Act & Assert
//        assertThrows(CheckException.class, () -> {
//            ssoTokenActionService.ssoAccountdoLogin(username, password, productCode);
//        });
//
//        // Verify
//        verify(upmUserService).queryAccountInfoByUserName(username);
//        verify(upmUserService).checkPassword(password, accountInfo);
//        verify(userRepository, never()).queryListByAccountId(anyLong());
//    }
//
//    @Resource
//    private SsoAccountActionService ssoAccountActionService;
//
//    private final String username = "kangjian";
//    private UpmAccount upmAccount;
//
//    @Test
//    public void whenQueryAccountInfoByUserNameExists_thenShouldReturnUserAccountInfoDTO() {
//        UserAccountInfoDTO result = ssoAccountActionService.queryAccountInfoByUserName(username);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(username, result.getUsername());
//        assertEquals(upmAccount.getId(), result.getAccountId());
//        assertEquals(upmAccount.getPassword(), result.getPassword());
//        assertEquals(upmAccount.getSalt(), result.getSalt());
//        assertEquals(upmAccount.getTelephone(), result.getTelephone());
//
//        ssoAccountActionService.checkPassword("asdas", result);
//    }
//
//    @Test
//    public void whenQueryAccountInfoByUserNameNotExists_thenShouldReturnNull() {
//        // Arrange
//        when(userRepository.queryAccountByUserName(username)).thenReturn(null);
//
//        // Act
//        UserAccountInfoDTO result = ssoAccountActionService.queryAccountInfoByUserName(username);
//
//        // Assert
//        assertNull(result);
//    }
//}
