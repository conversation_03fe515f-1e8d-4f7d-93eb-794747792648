package cn.genn.trans.upm;

import cn.genn.trans.upm.application.service.action.SsoTokenActionService;
import cn.genn.trans.upm.interfaces.api.ISsoAuthClient;
import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
import cn.genn.trans.upm.interfaces.query.SsoAccountLoginQuery;
import cn.genn.trans.upm.interfaces.query.UpmCheckUriPermissionQuery;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @Date: 2024/4/23
 * @Author: kangjian
 */
@SpringBootTest(classes = Application.class)
@Slf4j
public class CasbinRuleTest {

    @Resource
    private ISsoAuthClient ssoAuthClient;
    @Resource
    private SsoTokenActionService ssoTokenActionService;

    @Test
    void test() {
        SsoAccountLoginQuery query = SsoAccountLoginQuery.builder()
            .username("kangjian")
            .password("kangjasdas")
            .systemCode("WEB")
            .build();
        try {
            LoginUserAccountDTO result = ssoAuthClient.auth(query,"default");
            System.out.println(JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void testCasbinRule() {
        UpmCheckUriPermissionQuery query = UpmCheckUriPermissionQuery.builder()
            .userId(1L)
            .uri("/")
            .build();
        try {
            Boolean result = ssoAuthClient.checkUriPermission(query);
            log.info("result: {}",  result);
        } catch (Exception e) {
            log.error("error: ",  e);
        }
         query = UpmCheckUriPermissionQuery.builder()
            .userId(1L)
            .uri("/platform")
            .build();
        try {
            Boolean result = ssoAuthClient.checkUriPermission(query);
            log.info("result: {}",  result);
        } catch (Exception e) {
            log.error("error: ",  e);
        }
    }

    @Test
    void testCasbinRuleService() {
        UpmCheckUriPermissionQuery query = UpmCheckUriPermissionQuery.builder()
            .userId(1L)
            .uri("/tgg/0001")
            .build();
        boolean result = ssoTokenActionService.checkUriPermission(query);
        log.info("result1: {}",  result);
        query.setUri("/sso/user");
        result = ssoTokenActionService.checkUriPermission(query);
        log.info("result2: {}",  result);
        query.setUri("/sso");
        result = ssoTokenActionService.checkUriPermission(query);
        log.info("result3: {}",  result);
        query.setUri("/tgg/0009");
        result = ssoTokenActionService.checkUriPermission(query);
        log.info("result4: {}",  result);
//        result = ssoTokenActionService.checkUriPermission(query);
//        log.info("result2: {}",  result);
//        result = ssoTokenActionService.checkUriPermission(query);
//        log.info("result3: {}",  result);
    }
}
