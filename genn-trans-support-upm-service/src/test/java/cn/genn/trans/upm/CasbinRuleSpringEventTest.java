package cn.genn.trans.upm;

import cn.genn.trans.upm.application.dto.UpmAuthChangeEventDimensionEnum;
import cn.genn.trans.upm.application.service.action.SpringEventPublishService;
import cn.genn.trans.upm.interfaces.api.ISsoAuthClient;
import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
import cn.genn.trans.upm.interfaces.query.SsoAccountLoginQuery;
import cn.genn.trans.upm.interfaces.query.UpmCheckUriPermissionQuery;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @Date: 2024/4/23
 * @Author: kangjian
 */
@SpringBootTest(classes = Application.class)
@Slf4j
public class CasbinRuleSpringEventTest {

    @Resource
    private SpringEventPublishService eventPublishService;

    @Test
    void testResource() throws InterruptedException {
        eventPublishService.publishUpmAuthChangeEvent(UpmAuthChangeEventDimensionEnum.RESOURCE, 3L);
        Thread.sleep(10000000000L);
    }


}
