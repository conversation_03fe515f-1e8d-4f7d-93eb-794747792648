package cn.genn.trans.upm;

import cn.hutool.core.io.resource.ResourceUtil;
import org.casbin.jcasbin.main.Enforcer;
import org.casbin.jcasbin.model.Model;
import org.casbin.jcasbin.util.Util;

import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class JCasbinExample {
    public static void main(String[] args) {
        // 假设你的模型文件名为 model.conf，策略文件名为 policy.csv
        String modelResourceName = "casbin_model/rbac_model.conf";
        String policyResourceName = "casbin_model/rbac_policy_definition.csv";
        String str = ResourceUtil.readUtf8Str(modelResourceName);
        System.out.println(str);
    }
}
