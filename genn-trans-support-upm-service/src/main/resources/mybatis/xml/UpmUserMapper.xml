<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper">

    <resultMap id="userResultMap" type="cn.genn.trans.upm.domain.upm.model.entity.UpmUser">
        <id property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="systemId" column="system_id"/>
        <result property="status" column="status"/>
        <result property="nick" column="nick"/>
        <result property="avatar" column="avatar"/>
        <result property="remark" column="remark"/>
        <result property="telephone" column="telephone"/>
        <result property="email" column="email"/>
        <result property="authKey" column="auth_key"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="effectiveTime" column="effective_time"/>
        <collection property="roleList" ofType="cn.genn.trans.upm.domain.upm.model.entity.UpmRole">
            <id property="id" column="role_id"/>
            <result property="code" column="role_code"/>
            <result property="name" column="role_name"/>
            <result property="systemId" column="role_system_id"/>
            <result property="tenantId" column="role_tenant_id"/>
            <result property="type" column="role_type"/>
            <result property="authKey" column="auth_key"/>
            <result property="status" column="role_status"/>
            <result property="remark" column="role_remark"/>
            <collection property="resourceList" ofType="cn.genn.trans.upm.domain.upm.model.entity.UpmResource">
                <id property="id" column="resource_id"/>
                <result property="type" column="resource_type"/>
                <result property="name" column="resource_name"/>
                <result property="resourceSort" column="resource_sort"/>
                <result property="pid" column="resource_pid"/>
                <result property="icon" column="resource_icon"/>
                <result property="code" column="resource_code"/>
                <result property="url" column="resource_url"/>
                <result property="title" column="resource_title"/>
                <result property="path" column="resource_path"/>
                <result property="status" column="resource_status"/>
                <result property="component" column="resource_component"/>
                <result property="redirect" column="resource_redirect"/>
                <result property="activePath" column="resource_activePath"/>
                <result property="auths" column="resource_auths"/>
                <result property="frameSrc" column="resource_frameSrc"/>
                <result property="showLink" column="resource_showLink"/>
                <result property="showParent" column="resource_showParent"/>
                <result property="keepAlive" column="resource_keepAlive"/>
                <result property="fixedTag" column="resource_fixedTag"/>
                <result property="hiddenTag" column="resource_hiddenTag"/>
                <result property="remark" column="resource_remark"/>
                <result property="deleted" column="resource_deleted"/>
                <result property="createTime" column="resource_createTime"/>
                <result property="createUserId" column="resource_createUserId"/>
                <result property="createUserName" column="resource_createUserName"/>
                <result property="updateTime" column="resource_updateTime"/>
                <result property="updateUserId" column="resource_updateUserId"/>
                <result property="updateUserName" column="resource_updateUserName"/>
            </collection>
        </collection>
    </resultMap>

    <select id="findUserRoleResource" resultMap="userResultMap">
        SELECT u.id,
               u.account_id,
               u.tenant_id,
               u.system_id,
               u.status,
               u.nick,
               u.avatar,
               u.remark,
               u.telephone,
               u.email,
               u.auth_key,
               u.deleted,
               u.create_time,
               u.create_user_id,
               u.create_user_name,
               u.update_time,
               u.update_user_id,
               u.update_user_name,
               u.effective_time,
               r.id             as role_id,
               r.code           as role_code,
               r.name           as role_name,
               r.system_id      as role_system_id,
               r.tenant_id      as role_tenant_id,
               r.auth_key,
               r.type           as role_type,
               r.status         as role_status,
               r.remark         as role_remark,
               re.id            as resource_id,
               re.type          as resource_type,
               re.name          as resource_name,
               re.resource_sort as resource_sort,
               re.pid           as resource_pid,
               re.icon          as resource_icon,
               re.code          as resource_code,
               re.url           as resource_url,
               re.title         as resource_title,
               re.path          as resource_path,
               re.status        as resource_status,
               re.component     as resource_component,
               re.redirect      as resource_redirect,
               re.active_path    as resource_activePath,
               re.auths         as resource_auths,
               re.frame_src      as resource_frameSrc,
               re.show_link      as resource_showLink,
               re.show_parent    as resource_showParent,
               re.keep_alive     as resource_keepAlive,
               re.fixed_tag      as resource_fixedTag,
               re.hidden_tag     as resource_hiddenTag,
               re.remark        as resource_remark
        FROM upm_user u
                 LEFT JOIN upm_user_role ur ON u.id = ur.user_id
                 LEFT JOIN upm_role r ON ur.role_id = r.id and r.status = 1
                 LEFT JOIN upm_role_auth_resource_rel rr ON r.id = rr.role_id
                 LEFT JOIN upm_auth_resource_rel tsr ON tsr.id = rr.auth_resource_id
                 LEFT JOIN upm_resource re ON tsr.resource_id = re.id
        WHERE u.deleted = 0
          and u.status = 1
          and u.id = #{userId}
    </select>

    <select id="findAllUserAndRole" resultMap="userResultMap">
        SELECT u.id,
               u.account_id,
               u.tenant_id,
               u.system_id,
               u.status,
               u.nick,
               u.telephone,
               u.email,
               u.deleted,
               u.create_time,
               u.create_user_id,
               u.create_user_name,
               u.update_time,
               u.update_user_id,
               u.update_user_name,
               r.id        as role_id,
               r.name      as role_name,
               r.system_id as role_system_id,
               r.tenant_id as role_tenant_id,
               r.type      as role_type,
               r.status    as role_status,
               r.remark    as role_remark
        FROM upm_user u
                 LEFT JOIN upm_user_role ur ON u.id = ur.user_id
                 LEFT JOIN upm_role r ON ur.role_id = r.id
        WHERE u.deleted = 0
          and u.status = 1
    </select>

    <select id="selectByUsernameAndSystemId" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO">
        select uu.* from upm_user uu inner join upm_account ua on uu.account_id = ua.id
        <where>
            ua.username = #{username}
            AND ua.system_id = #{systemId}
            AND uu.deleted = 0
            AND ua.deleted =0
        </where>

    </select>

    <select id="selectByTelephoneAndSystemId" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO">
        select uu.* from upm_user uu inner join upm_account ua on uu.account_id = ua.id
        <where>
            ua.telephone = #{telephone}
            AND ua.system_id = #{systemId}
            AND uu.deleted = 0
            AND ua.deleted =0
        </where>

    </select>

    <select id="selectUserList" parameterType="list" resultType="cn.genn.trans.upm.application.dto.UserAccountDTO">
        select uu.*,ua.username,ua.type from upm_user uu inner join upm_account ua on uu.account_id = ua.id
        <where>
            uu.id in
            <foreach collection="list" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
            AND uu.deleted = 0
            AND ua.deleted =0
        </where>
    </select>

    <select id="selectByAccountIdAndVpcGroup" parameterType="list" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO">
        select uu.* from upm_user uu inner join upm_tenant ut on uu.tenant_id = ut.id
        <where>
            uu.account_id = #{accountId}
            and uu.status = 1
            <if test="vpcGroup !=null and vpcGroup !=''">
                and (ut.vpc_group = #{vpcGroup} or uu.tenant_id=1)
            </if>
        </where>
    </select>

    <select id="selectByPage" resultType="cn.genn.trans.upm.interfaces.dto.UpmUserDTO">
        select uu.*,ua.username,ua.type,ua.company_name,ua.sale_name from upm_user uu inner join upm_account ua on uu.account_id= ua.id
        <where>
            uu.auth_key=#{authKey}
            <if test="query.username != null and query.username !=''">
                and ua.username like CONCAT('%',#{query.username},'%')
            </if>
            <if test="query.status != null">
                and uu.`status`= #{query.status.code}
            </if>
            <if test="query.telephone != null and query.telephone !=''">
                and ua.telephone like CONCAT('%',#{query.telephone},'%')
            </if>
            <if test="query.nick != null and query.nick !=''">
                and uu.nick like CONCAT('%',#{query.nick},'%')
            </if>
            <if test="query.companyName != null and query.companyName !=''">
                and ua.company_name like CONCAT('%',#{query.companyName},'%')
            </if>
            <if test="query.saleName != null and query.saleName !=''">
                and ua.sale_name like CONCAT('%',#{query.saleName},'%')
            </if>
            <if test="query.getUserIds != null and query.getUserIds.size() > 0">
                and  uu.id in
                <foreach collection="query.getUserIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and uu.type="tenant"
            and uu.deleted=0 and ua.deleted=0
            order by uu.id desc
        </where>
    </select>

    <select id="selectUserRoleByPage" parameterType="cn.genn.trans.upm.interfaces.query.UpmUserRolePageQuery"
            resultType="cn.genn.trans.upm.interfaces.dto.UpmUserDTO">
        select uu.*, ua.username, ua.type
        from upm_user uu inner join upm_account ua on uu.account_id= ua.id
        <where>
            uu.auth_key=#{authKey}
            <if test="query.username != null and query.username !=''">
                and ua.username like CONCAT('%',#{query.username},'%')
            </if>
            <if test="query.remark != null and query.remark !=''">
                and uu.remark like CONCAT('%',#{query.remark},'%')
            </if>
            <if test="query.roleId != null">
                and uu.id in (select distinct uur.user_id
                from upm_user_role uur
                left join upm_role ur on uur.role_id = ur.id
                where ur.id =#{query.roleId} and ur.status = 1 and ur.deleted=0)
            </if>
            <if test="query.status != null">
                and uu.`status`= #{query.status.code}
            </if>
            <if test="query.telephone != null and query.telephone !=''">
                and ua.telephone like CONCAT('%',#{query.telephone},'%')
            </if>
            <if test="query.nick != null and query.nick !=''">
                and uu.nick like CONCAT('%',#{query.nick},'%')
            </if>
            <if test="query.updateTimeBefore != null">
                AND uu.update_time <![CDATA[ >= ]]> #{query.updateTimeBefore}
            </if>
            <if test="query.updateTimeAfter != null">
                AND uu.update_time <![CDATA[ <= ]]> #{query.updateTimeAfter}
            </if>
            and uu.type= "tenant"
            and uu.deleted=0 and ua.deleted=0
            order by uu.id desc
        </where>
    </select>

    <select id="selectByQuery" resultType="cn.genn.trans.upm.interfaces.dto.UpmUserDTO">
        select uu.id,
        uu.account_id,
        uu.tenant_id,
        uu.system_id,
        uu.status,
        uu.auth_key,
        uu.nick,
        uu.type,
        uu.email,
        uu.deleted,
        uu.create_time,
        uu.create_user_id,
        uu.create_user_name,
        uu.update_time,
        uu.update_user_id,
        uu.update_user_name,
        uu.avatar,
        ua.telephone,
        ua.username,ua.type
        from upm_user uu inner join upm_account ua on uu.account_id= ua.id
        <where>
            <if test="query.tenantId != null and query.tenantId >0">
                and uu.tenant_id= #{query.tenantId}
            </if>
            <if test="query.systemId != null and query.systemId >0">
                and uu.system_id= #{query.systemId}
            </if>
            <if test="query.authKey != null and query.authKey !=''">
                and uu.auth_key= #{query.authKey}
            </if>
            <if test="query.userIdList != null and query.userIdList.size() >0">
                and uu.id in
                <foreach collection="query.userIdList" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.username != null and query.username !=''">
                and ua.username like CONCAT('%',#{query.username},'%')
            </if>
            <if test="query.status != null">
                and uu.`status`= #{query.status.code}
            </if>
            <if test="query.telephone != null and query.telephone !=''">
                and ua.telephone =#{query.telephone}
            </if>
            <if test="query.nick != null and query.nick !=''">
                and uu.nick like CONCAT('%',#{query.nick},'%')
            </if>
            <if test="query.deleted != null">
                and uu.deleted=#{query.deleted.code}
                and ua.deleted=#{query.deleted.code}
            </if>
            order by uu.id desc
        </where>
    </select>

    <select id="queryUserByUsernamesAndAuthKey" resultType="cn.genn.trans.upm.interfaces.dto.UpmUserDTO">
        select uu.id,
               uu.account_id,
               uu.tenant_id,
               uu.system_id,
               uu.status,
               uu.auth_key,
               uu.nick,
               uu.type,
               uu.email,
               uu.deleted,
               uu.create_time,
               uu.create_user_id,
               uu.create_user_name,
               uu.update_time,
               uu.update_user_id,
               uu.update_user_name,
               uu.avatar,
               ua.telephone,
               ua.username,ua.type
        from upm_user uu inner join upm_account ua on uu.account_id= ua.id
        <where>
            and ua.username in
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            and ua.system_id = #{systemId}
            and uu.auth_key = #{authKey}
        </where>
    </select>

    <select id="searchName" resultType="cn.genn.trans.upm.interfaces.dto.UpmUserDTO">
        select uu.id,
               uu.account_id,
               uu.tenant_id,
               uu.system_id,
               uu.status,
               uu.auth_key,
               uu.nick,
               uu.type,
               uu.email,
               uu.deleted,
               uu.create_time,
               uu.create_user_id,
               uu.create_user_name,
               uu.update_time,
               uu.update_user_id,
               uu.update_user_name,
               uu.avatar,
               ua.telephone,
               ua.username,ua.type
        from upm_user uu inner join upm_account ua on uu.account_id= ua.id
        <where>
            and ua.system_id = #{systemId}
            and uu.auth_key = #{authKey}
            and (
                ua.username like CONCAT('%',#{search},'%')
                or uu.nick like CONCAT('%',#{search},'%')
            )
        </where>
    </select>

    <select id="queryByRoleCode" resultType="cn.genn.trans.upm.interfaces.dto.UpmUserDTO">
        select uu.id,
               uu.account_id,
               uu.tenant_id,
               uu.system_id,
               uu.status,
               uu.auth_key,
               uu.nick,
               uu.type,
               uu.email,
               uu.deleted,
               uu.create_time,
               uu.create_user_id,
               uu.create_user_name,
               uu.update_time,
               uu.update_user_id,
               uu.update_user_name,
               uu.avatar,
               ua.telephone,
               ua.username,ua.type
        from upm_user uu
            inner join upm_account ua on uu.account_id= ua.id
            inner join upm_user_role uur on uu.id = uur.user_id
            inner join upm_role ur on ur.id = uur.role_id
        <where>
            ur.code = #{query.roleCode}
            and ur.auth_key= #{query.authKey}
            and uu.auth_key= #{query.authKey}
            and ur.deleted =0 and ur.status=1
            and uu.deleted = 0 and uu.status=1
        </where>
    </select>

    <select id="selectByUserId" parameterType="java.lang.Long" resultType="cn.genn.trans.upm.interfaces.dto.UpmUserDTO">
        select uu.*,ua.username,ua.type from upm_user uu inner join upm_account ua on uu.account_id= ua.id
        <where>
            uu.id = #{id}
        </where>
    </select>

    <insert id="saveBatch" parameterType="list" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into upm_user(account_id,tenant_id,system_id,auth_key,nick,create_time,create_user_id,create_user_name,
                             update_time,update_user_id,update_user_name)
        values
            <foreach collection="list" item="item" separator=",">
                (
                 #{item.accountId},
                 #{item.tenantId},
                 #{item.systemId},
                 #{item.authKey},
                 #{item.nick},
                 now(),
                 #{item.createUserId},
                 #{item.createUserName},
                 now(),
                 #{item.updateUserId},
                 #{item.updateUserName}
                )
            </foreach>
    </insert>

    <select id="selectSuperPage" resultType="cn.genn.trans.upm.interfaces.dto.fsp.SuperUserDTO">
        select uu.id,
        ua.username,
        ua.telephone,
        uu.email,
        uu.nick,
        uu.auth_key,
        SUBSTRING_INDEX(uu.auth_key, '-', 1) as auth_group,
        uu.`status`,
        uu.create_time,
        uu.update_time,
        uu.update_user_name
        from upm_user uu
        left join upm_account ua on ua.id=uu.account_id
        <where>
            ua.deleted=0 and uu.deleted=0
            and ua.system_id =#{query.systemId} and uu.system_id=#{query.systemId}
            and ua.type='system' and uu.type='system'
            and uu.auth_key not like "pl%"
            <if test="query.username != null and query.username !=''">
                and ua.username like CONCAT('%',#{query.username},'%')
            </if>
            <if test="query.nick != null and query.nick !=''">
                and uu.nick like CONCAT('%',#{query.nick},'%')
            </if>
            <if test="query.telephone != null and query.telephone !=''">
                and ua.telephone =#{query.telephone}
            </if>
            <if test="query.status != null">
                and uu.`status` = #{query.status.code}
            </if>
        </where>
        order by uu.id desc
    </select>

    <select id="selectByPhone" resultType="cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO">
        select ua.*,uu.id as user_id,uu.auth_key,uu.system_id,uu.tenant_id
        from upm_user uu
        inner join upm_account ua on uu.account_id=ua.id
        <where>
            and ua.telephone=#{telephone}
            and uu.telephone=#{telephone}
            and ua.system_id=#{systemId}
            and uu.system_id=#{systemId}
            and ua.deleted=0
        </where>
        limit 1
    </select>

    <select id="selectByUsername" resultType="cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO">
        select ua.*,uu.id as user_id,uu.auth_key,uu.system_id,uu.tenant_id
        from upm_user uu
        inner join upm_account ua on uu.account_id=ua.id
        <where>
            and ua.username=#{username}
            and ua.system_id=#{systemId}
            and uu.system_id=#{systemId}
            and ua.deleted=0
        </where>
        limit 1
    </select>

</mapper>
