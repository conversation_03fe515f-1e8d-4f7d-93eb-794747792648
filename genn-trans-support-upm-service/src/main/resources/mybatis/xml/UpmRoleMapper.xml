<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, code, name, auth_key, tenant_id, system_id, `status`, `type`, remark,
        deleted, create_time, create_user_id, create_user_name, update_time, update_user_id,
        update_user_name
    </sql>
    <resultMap id="rolePoResultMap" type="cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="authKey" column="auth_key"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="systemId" column="system_id"/>
        <result property="status" column="status"/>
        <result property="type" column="type"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateUserName" column="update_user_name"/>
    </resultMap>

    <resultMap id="roleResultMap" type="cn.genn.trans.upm.domain.upm.model.entity.UpmRole">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="systemId" column="system_id"/>
        <result property="status" column="status"/>
        <result property="type" column="type"/>
        <result property="authKey" column="auth_key"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateUserName" column="update_user_name"/>
        <!--        <collection property="userList" ofType="cn.genn.trans.upm.domain.upm.model.entity.UpmUser">-->
        <!--            <id property="id" column="user_id"/>-->
        <!--            <result property="accountId" column="user_account_id"/>-->
        <!--            <result property="systemId" column="user_system_id"/>-->
        <!--            <result property="tenantId" column="user_tenant_id"/>-->
        <!--            <result property="status" column="user_status"/>-->
        <!--            <result property="nick" column="user_nick"/>-->
        <!--            <result property="telephone" column="user_telephone"/>-->
        <!--        </collection>-->
        <collection property="resourceList" ofType="cn.genn.trans.upm.domain.upm.model.entity.UpmResource">
            <id property="id" column="resource_id"/>
            <result property="type" column="resource_type"/>
            <result property="name" column="resource_name"/>
            <result property="resourceSort" column="resource_sort"/>
            <result property="pid" column="resource_pid"/>
            <result property="icon" column="resource_icon"/>
            <result property="code" column="resource_code"/>
            <result property="url" column="resource_url"/>
            <result property="title" column="resource_title"/>
            <result property="path" column="resource_path"/>
            <result property="status" column="resource_status"/>
            <result property="component" column="resource_component"/>
            <result property="redirect" column="resource_redirect"/>
            <result property="activePath" column="resource_activePath"/>
            <result property="auths" column="resource_auths"/>
            <result property="frameSrc" column="resource_frameSrc"/>
            <result property="showLink" column="resource_showLink"/>
            <result property="showParent" column="resource_showParent"/>
            <result property="keepAlive" column="resource_keepAlive"/>
            <result property="fixedTag" column="resource_fixedTag"/>
            <result property="hiddenTag" column="resource_hiddenTag"/>
            <result property="remark" column="resource_remark"/>
            <result property="deleted" column="resource_deleted"/>
            <result property="createTime" column="resource_createTime"/>
            <result property="createUserId" column="resource_createUserId"/>
            <result property="createUserName" column="resource_createUserName"/>
            <result property="updateTime" column="resource_updateTime"/>
            <result property="updateUserId" column="resource_updateUserId"/>
            <result property="updateUserName" column="resource_updateUserName"/>
        </collection>
    </resultMap>

    <resultMap id="roleUserResultMap" type="cn.genn.trans.upm.infrastructure.dto.RoleUserRelDTO">
        <result property="roleId" column="role_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="systemId" column="system_id"/>
        <result property="userId" column="user_id"/>
        <result property="authKey" column="auth_key"/>
    </resultMap>


    <select id="queryAllRoleAndResource" resultMap="roleResultMap">
        SELECT r.id,
               r.name,
               r.tenant_id,
               r.system_id,
               r.status,
               r.type,
               r.auth_key,
               r.remark,
               r.deleted,
               r.create_time,
               r.create_user_id,
               r.create_user_name,
               r.update_time,
               r.update_user_id,
               r.update_user_name,
               re.id            as resource_id,
               re.type          as resource_type,
               re.name          as resource_name,
               re.resource_sort as resource_sort,
               re.pid           as resource_pid,
               re.icon          as resource_icon,
               re.code          as resource_code,
               re.url           as resource_url,
               re.title         as resource_title,
               re.path          as resource_path,
               re.status        as resource_status,
               re.component     as resource_component,
               re.redirect      as resource_redirect,
               re.active_path    as resource_activePath,
               re.auths         as resource_auths,
               re.frame_src      as resource_frameSrc,
               re.show_link      as resource_showLink,
               re.show_parent    as resource_showParent,
               re.keep_alive     as resource_keepAlive,
               re.fixed_tag      as resource_fixedTag,
               re.hidden_tag     as resource_hiddenTag,
               re.remark        as resource_remark
        FROM upm_role r
                 LEFT JOIN upm_role_auth_resource_rel rr ON r.id = rr.role_id
                 LEFT JOIN upm_auth_resource_rel tsr ON tsr.id = rr.auth_resource_id
                 LEFT JOIN upm_resource re ON tsr.resource_id = re.id
        WHERE r.deleted = 0
          and r.status = 1
    </select>

    <select id="getAllUserAndRoleRel" resultMap="roleUserResultMap">
        SELECT r.id    as role_id,
               r.tenant_id,
               r.system_id,
               user.id as user_id,
               user.auth_key as auth_key
        FROM upm_user_role ur
                 LEFT JOIN upm_user user on ur.user_id = user.id
                 LEFT JOIN upm_role r on ur.role_id = r.id and ur.user_id = user.id
        WHERE r.deleted = 0
          and r.status = 1
    </select>

    <select id="queryByUserIds" resultType="cn.genn.trans.upm.interfaces.dto.UpmRoleUserDTO">
        select ur.*,uur.user_id from upm_role ur inner join upm_user_role uur on ur.id = uur.role_id
        <where>
            uur.user_id in
            <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
            and ur.status =1
            and ur.deleted =0
        </where>

    </select>

    <select id="selectByPage" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO">
        select * from upm_role
        <where>
            and auth_key = #{authKey}
            <if test="query.id != null">
                and id = #{query.id}
            </if>
            <if test="query.code != null and query.code !=''">
                and code like CONCAT('%',#{query.code},'%')
            </if>
            <if test="query.remark != null and query.remark !=''">
                and remark like CONCAT('%',#{query.remark},'%')
            </if>
            <if test="query.name != null and query.name !=''">
                and `name` like CONCAT('%',#{query.name},'%')
            </if>
            <if test="query.status != null">
                and `status` = #{query.status.code}
            </if>
            <if test="query.updateTimeBefore != null">
                AND update_time <![CDATA[ >= ]]> #{query.updateTimeBefore}
            </if>
            <if test="query.updateTimeAfter != null">
                AND update_time <![CDATA[ <= ]]> #{query.updateTimeAfter}
            </if>
            <if test="query.getRoleIds != null and query.getRoleIds.size() > 0">
                and id in
                <foreach collection="query.getRoleIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and `type` = "tenant"
            and deleted=0 order by id desc
        </where>
    </select>

    <select id="selectByList" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO">
        select * from upm_role
        <where>
            and auth_key = #{authKey}
            <if test="query.name != null and query.name !=''">
                and `name` like CONCAT('%',#{query.name},'%')
            </if>
            <if test="query.type != null">
                and `type` = #{query.type.code}
            </if>
            <if test="query.status != null">
                and `status` = #{query.status.code}
            </if>
            and deleted=0 order by id desc
        </where>
    </select>

    <select id="selectByAuthKeyAndMainRoleNames" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO">
        select
        ur.id, ur.code, ur.name, ur.auth_key, ur.tenant_id, ur.system_id, ur.`status`, ur.`type`, ur.remark,
        ur.deleted, ur.create_time, ur.create_user_id, ur.create_user_name, ur.update_time, ur.update_user_id,
        ur.update_user_name
        FROM upm_role ur
        LEFT JOIN upm_role ur2 ON ur.id = ur2.id
        WHERE ur.name IN
        <foreach collection="roleNameList" item="name" separator="," open="(" close=")">
            #{name}
        </foreach>
        AND ur2.auth_key IN
        <foreach collection="authKeyList" item="authKey" separator="," open="(" close=")">
            #{authKey}
        </foreach>
        and ur.deleted=0 and ur2.deleted=0

    </select>

    <insert id="saveBatch" parameterType="list" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into upm_role ( `code`,`name`,system_id,tenant_id,type,auth_key,remark,create_time,create_user_id,create_user_name,
            update_time,update_user_id,update_user_name)
        values
        <foreach collection="list" item="item" separator=",">
        (
        #{item.code},
        #{item.name},
        #{item.systemId},
        #{item.tenantId},
        #{item.type.code},
        #{item.authKey},
        #{item.remark},
        now(),
        #{item.createUserId},
        #{item.createUserName},
        now(),
        #{item.updateUserId},
        #{item.updateUserName}
         )
        </foreach>
    </insert>

    <select id="selectAllByTenantId" parameterType="long" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO">
        select * from upm_role
        where tenant_id = #{tenantId}
    </select>

    <select id="queryByCodeAndAuthKey" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO">
        select * from upm_role
        where auth_key=#{authKey} and code=#{code} and deleted=0
    </select>
</mapper>
