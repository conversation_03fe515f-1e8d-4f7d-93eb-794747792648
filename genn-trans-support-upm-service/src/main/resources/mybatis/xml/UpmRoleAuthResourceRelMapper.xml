<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleAuthResourceRelMapper">


        <delete id="deleteByRoleId" parameterType="java.lang.Long">
            delete from upm_role_auth_resource_rel
            <where>
                role_id = #{roleId}
            </where>
        </delete>

        <insert id="saveBatch" parameterType="list">
            INSERT INTO upm_role_auth_resource_rel (
            auth_resource_id,
            role_id,
            create_user_id,
            create_user_name
            ) VALUES
            <foreach collection="list" item="item" separator=",">
                (#{item.authResourceId},
                #{item.roleId},
                #{item.createUserId},
                #{item.createUserName})
            </foreach>
        </insert>
</mapper>
