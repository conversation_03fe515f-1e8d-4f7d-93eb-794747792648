<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeRoleTemplateMapper">

    <select id="selectByAuthTypeAndSystemId" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO">
        select * from upm_auth_type_role_template
        where template_id = (
            select id from upm_auth_type_template
            where auth_type = #{authGroup.code}
                and system_id = #{systemId}
        )
    </select>
</mapper>
