<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmClusterSystemRelMapper">

    <select id="queryByTenantIdAndSystemId" resultType="cn.genn.trans.upm.interfaces.dto.ClusterSystemDTO">
        select * from upm_cluster_system_rel ucsr
        left join upm_tenant ut on ucsr.vpc_group= ut.vpc_group
        <where>
            ut.deleted=0 and ucsr.deleted=0
                 and ut.id = #{tenantId}
        and ucsr.system_id = #{systemId}
        </where>
    </select>
</mapper>
