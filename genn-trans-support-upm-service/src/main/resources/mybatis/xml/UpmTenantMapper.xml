<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantMapper">


    <select id="selectListBySystemId" parameterType="java.lang.Long" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO">
        select * from upm_tenant
        <where>
            id in(
            select tenant_id from upm_tenant_system where system_id = #{systemId}
            )
        </where>
    </select>

    <select id="selectByPage" parameterType="cn.genn.trans.upm.interfaces.query.UpmTenantQuery" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO">
        select ut.* from upm_tenant ut inner join upm_tenant_system uts on ut.id =uts.tenant_id
        <where>
             uts.system_id = #{query.systemId}
            <if test="query.name != null and query.name !=''">
                and ut.`name` like CONCAT(#{query.name},'%')
            </if>
            <if test="query.code != null and query.code !=''">
                and ut.`code` = #{query.code}
            </if>
            <if test="query.type != null">
                and ut.`type` = #{query.type.code}
            </if>
            <if test="query.status != null">
                and ut.`status` = #{query.status.code}
            </if>
            and ut.deleted=0 order by ut.id desc
        </where>
    </select>

    <select id="selectByList" parameterType="cn.genn.trans.upm.interfaces.query.UpmTenantQuery" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO">
        select ut.* from upm_tenant ut inner join upm_tenant_system uts on ut.id =uts.tenant_id
        <where>
            uts.system_id = #{query.systemId}
            <if test="query.name != null and query.name !=''">
                and ut.`name` like CONCAT(#{query.name},'%')
            </if>
            <if test="query.code != null and query.code !=''">
                and ut.`code` = #{query.code}
            </if>
            <if test="query.type != null">
                and ut.`type` = #{query.type.code}
            </if>
            <if test="query.status != null">
                and ut.`status` = #{query.status.code}
            </if>
            and ut.deleted=0 order by ut.id desc
        </where>
    </select>



</mapper>
