<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmAccountMapper">

    <select id="selectByOpenIdAndAppId"
            resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO">

        select id, username, password, salt, system_id, status, remark, login_type, `type`, telephone, password_status,
        pd_update_time, deleted, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
        from upm_account ua
        where ua.deleted = 0 and telephone in (select telephone
                            from upm_user
                            where id in (select user_id
                                         from upm_user_third uut
                                         where uut.open_id = #{openId}
                                           and uut.app_id = #{appId} and uut.status=1)) and system_id=#{systemId}
    </select>
</mapper>
