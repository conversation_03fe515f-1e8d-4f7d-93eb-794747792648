<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper">

    <insert id="saveBatch" parameterType="list" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into upm_tenant_system (system_id,tenant_id, create_time, create_user_id, create_user_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.systemId},
            #{item.tenantId},
            now(),
            #{item.createUserId},
            #{item.createUserName})
        </foreach>
    </insert>
</mapper>
