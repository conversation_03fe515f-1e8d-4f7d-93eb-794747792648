<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper">


    <select id="selectByPhoneAndOpenId" resultType="cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO">
        select
            ua.*,uu.id as user_id ,uut.open_id,uut.union_id,uut.app_id,uu.auth_key,uu.system_id,uu.tenant_id
            from upm_user_third uut right join upm_user uu on uut.user_id=uu.id
        right join upm_account ua on uu.account_id=ua.id
        <where>
            <if test="openId !=null and openId !=''">
                uut.open_id = #{openId}
                and uut.app_id = #{appId}
            </if>
            and ua.telephone=#{telephone}
            and ua.system_id=#{systemId}
            and uu.system_id=#{systemId}
            and ua.deleted=0
        </where>
        limit 1

    </select>
    <select id="selectByUsernameAndOpenId" resultType="cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO">
        select
        ua.*,uu.id as user_id ,uut.open_id,uut.union_id,uut.app_id,uu.auth_key,uu.system_id,uu.tenant_id
        from upm_user_third uut right join upm_user uu on uut.user_id=uu.id
        right join upm_account ua on uu.account_id=ua.id
        <where>
            <if test="openId !=null and openId !=''">
                uut.open_id = #{openId}
                and uut.app_id =#{appId}
            </if>
            and ua.username=#{username}
            and ua.system_id=#{systemId}
            and uu.system_id=#{systemId}
            and ua.deleted=0
        </where>
        limit 1

    </select>

    <select id="getThirdByUserIdsAndSystemType" resultType="cn.genn.trans.upm.interfaces.dto.UpmUserThirdDTO">
        select uut.*,us.code as systemCode
            from upm_user_third uut right join upm_user uu on uut.user_id=uu.id
            right join upm_system us on us.id=uu.system_id
        <where>
            us.type = #{systemType}
            and uut.user_id in
            <foreach collection="userIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and uut.type = #{thirdType}
            and uut.status = 1
        </where>
    </select>
</mapper>
