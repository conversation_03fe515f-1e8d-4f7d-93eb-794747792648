<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.trans.upm.infrastructure.repository.mapper.UpmResourceMapper">

    <sql id="Column_List">
        id, system_id, `type`, `name`, resource_sort, pid, icon, code, url, title, `path`, status, component,
          redirect, active_path, auths, frame_src, show_link, show_parent, keep_alive, fixed_tag, hidden_tag, remark,
          deleted, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

    <select id="selectByRoleId" parameterType="java.lang.Long" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO">
        select
        <include refid="Column_List"/>
        from upm_resource
        <where>
            id in (
            select resource_id from upm_auth_resource_rel where id in (
            select auth_resource_id from upm_role_auth_resource_rel where role_id =#{roleId}
            )
            )
        </where>
    </select>

    <select id="selectByRoleIds" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO">
        select distinct
        <include refid="Column_List"/>
        from upm_resource
        <where>
            id in (
            select ur.id from upm_resource ur left join upm_auth_resource_rel uarr on ur.id = uarr.resource_id
            left join upm_role_auth_resource_rel urarr on uarr.id = urarr.auth_resource_id
            where urarr.role_id in
            <foreach collection="list" item="roleId" separator="," open="(" close=")">
                #{roleId}
            </foreach>
            )
            <if test="type !=null">
                and type = #{type.type}
            </if>
            and status =1 and deleted = 0
        </where>
        order by resource_sort,id
    </select>

    <select id="selectByAuthKey" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO">
        select
        <include refid="Column_List"/>
        from upm_resource
        <where>
            id in (
            select ur.id
            from upm_resource ur left join upm_auth_resource_rel uarr on ur.id = uarr.resource_id
            where uarr.auth_key = #{authKey}
            )
            <if test="type !=null">
                and type = #{type.type}
            </if>
            and status =1 and deleted = 0
        </where>
        order by resource_sort,id
    </select>

    <select id="selectByAuthKeyList" resultType="cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO">
        select
        <include refid="Column_List"/>
        from upm_resource
        <where>
            id in (
            select ur.id
            from upm_resource ur left join upm_auth_resource_rel uarr on ur.id = uarr.resource_id
            where uarr.auth_key in
                <foreach collection="authKeyList" item="authKey" separator="," open="(" close=")">
                    #{authKey}
                </foreach>
            )
            and status =1 and deleted = 0
        </where>
        order by resource_sort,id
    </select>
</mapper>
