package cn.genn.trans.upm.application.job;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.genn.trans.upm.application.dto.FsAppDTO;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserThirdPO;
import cn.genn.trans.upm.infrastructure.utils.AsyncTaskExecutor;
import cn.genn.trans.upm.interfaces.dto.feishu.FsDepartmentDTO;
import cn.genn.trans.upm.interfaces.dto.feishu.FsExtraInfoDTO;
import cn.genn.trans.upm.interfaces.enums.ThridTypeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.contact.v3.model.*;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * 该定时任务用于补充或更新upm三方表里的部门信息;
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FeishuDepartmentSyncJob extends AbstractJobHandler {
    private final UpmUserThirdMapper userThirdMapper;

    @Override
    @IgnoreTenant
    public void doExecute() {
        String jobParam = XxlJobHelper.getJobParam();
        if (CharSequenceUtil.isEmpty(jobParam)) {
            throw new BusinessException("jobParam is null");
        }
        List<FsAppDTO> pushParam = JsonUtils.parseToList(jobParam, FsAppDTO.class);
        handle(pushParam);
    }

    public void handle(List<FsAppDTO> pushParam) {
        if (CollUtil.isEmpty(pushParam)) {
            throw new BusinessException("jobParam 格式不正确");
        }
        for (FsAppDTO fsAppDTO : pushParam) {
            if (StrUtil.isBlank(fsAppDTO.getAppId())) {
                continue;
            }
            List<UpmUserThirdPO> upmUserThirdPOS = userThirdMapper.selectByAppId(fsAppDTO.getAppId(), ThridTypeEnum.FS);
            if (CollUtil.isEmpty(upmUserThirdPOS)) {
                continue;
            }
            Client client = Client.newBuilder(fsAppDTO.getAppId(), fsAppDTO.getAppSecret()).build();
            for (UpmUserThirdPO upmUserThirdPO : upmUserThirdPOS) {
                CompletableFuture.runAsync(() -> {
                            List<FsDepartmentDTO> departments = null;
                            String avatarUrl = null;
                            try {
                                GetUserRespBody body = fetchUserInfo(client, upmUserThirdPO.getOpenId());
                                if (body == null) {
                                    throw new BusinessException("请登录飞书开发者后台【https://open.feishu.cn】查看相关应用是否开通【获取通讯录基本信息】权限");
                                }
                                String[] departmentIds = body.getUser().getDepartmentIds();
                                if (ObjUtil.isNull(departmentIds) || CollUtil.isEmpty(Arrays.asList(departmentIds))) {
                                    throw new BusinessException("未查询到飞书用户部门信息: 请登录飞书开发者后台【https://open.feishu.cn】查看应用是否开通【获取用户组织架构信息】的权限");
                                }
                                String departmentId = departmentIds[0];
                                avatarUrl = body.getUser().getAvatar().getAvatarOrigin();

                                ParentDepartmentRespBody departmentRespBody = fetchDepartments(client, departmentId);
                                if (departmentRespBody == null) {
                                    throw new BusinessException("请登录飞书开发者后台【https://open.feishu.cn】查看相关应用是否开通【获取通讯录部门组织架构信息】权限");
                                }
                                Department[] parentDepartments = departmentRespBody.getItems();
                                if (CollUtil.isEmpty(Arrays.asList(parentDepartments))) {
                                    throw new BusinessException("未查询到飞书用户部门信息: 请登录飞书开发者后台【https://open.feishu.cn】查看应用是否开通【获取通讯录部门组织架构信息】的权限");
                                }
                                departments = Arrays.stream(parentDepartments).map(department -> new FsDepartmentDTO()
                                        .setOpenDepartmentId(department.getOpenDepartmentId())
                                        .setName(department.getName())).collect(Collectors.toList());
                            } catch (Exception e) {
                                log.error("获取飞书部门异常： {}", e.getMessage());
                            }
                            if (CollUtil.isNotEmpty(departments)) {
                                FsExtraInfoDTO fsExtraInfoDTO = new FsExtraInfoDTO();
                                fsExtraInfoDTO.setDepartments(departments);
                                fsExtraInfoDTO.setAvatarUrl(avatarUrl);
                                userThirdMapper.updateExtraInfoById(upmUserThirdPO.getId(), JsonUtils.toJson(fsExtraInfoDTO));
                            }
                        }, AsyncTaskExecutor.demoExecutor)
                        .exceptionally((ex -> {
                            log.error("FeishuDepartmentSyncJob error: ", ex);
                            return null;
                        }));
            }
        }
    }

    private GetUserRespBody fetchUserInfo(Client client, String openId) {
        // 创建请求对象
        GetUserResp resp = null;
        try {
            //用户当前部门
            GetUserReq req = GetUserReq.newBuilder()
                .userId(openId)
                .userIdType("open_id")
                .departmentIdType("open_department_id")
                .build();
            resp = client.contact().v3().user().get(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }
        // 处理服务端错误
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException("请登录飞书开发者后台【https://open.feishu.cn】查看相关应用是否开通【获取用户基本信息】权限");
        }
        return resp.getData();
    }

    private ParentDepartmentRespBody fetchDepartments(Client client, String DepartmentId) {
        // 创建请求对象
        ParentDepartmentResp resp = null;
        try {
            // 创建请求对象
            ParentDepartmentReq req = ParentDepartmentReq.newBuilder()
                .departmentId(DepartmentId)
                .pageSize(20)
                .build();
            // 发起请求
            resp = client.contact().department().parent(req, RequestOptions.newBuilder().build());
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }
        // 处理服务端错误
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException("请登录飞书开发者后台【https://open.feishu.cn】查看相关应用是否开通【获取部门基础信息】权限");
        }
        return resp.getData();
    }


}
