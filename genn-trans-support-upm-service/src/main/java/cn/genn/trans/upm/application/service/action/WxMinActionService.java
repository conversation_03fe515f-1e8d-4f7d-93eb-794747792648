package cn.genn.trans.upm.application.service.action;

import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserThirdPO;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/13
 */
@Service
@Slf4j
public class WxMinActionService {

    @Resource
    private UpmUserThirdMapper userThirdMapper;

    /**
     * 保持同一userId,只有一个openid在开启状态.
     * @param appId
     * @param userId
     * @param openId
     */
    public void wxChangeSingleLogin(String appId,Long userId,String openId) {
        List<UpmUserThirdPO> upmUserThirdPOS = userThirdMapper.selectByUserIdAndAppId(userId, appId);
        if (CollUtil.isNotEmpty(upmUserThirdPOS)) {
            userThirdMapper.closeStatusByUserIdAndAppId(userId,appId);
            userThirdMapper.openByOpenId(openId,userId);
        }

    }
}
