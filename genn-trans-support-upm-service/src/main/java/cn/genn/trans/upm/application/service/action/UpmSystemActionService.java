package cn.genn.trans.upm.application.service.action;

import cn.genn.trans.upm.domain.upm.factory.SystemFactory;
import cn.genn.trans.upm.domain.upm.service.SystemDomainService;
import cn.genn.trans.upm.interfaces.command.UpmChangeStatusCommand;
import cn.genn.trans.upm.interfaces.command.UpmSystemOperateCommand;
import cn.genn.trans.upm.interfaces.command.UpmSystemSaveCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional
public class UpmSystemActionService {

    @Resource
    private SystemDomainService systemDomainService;
    @Resource
    private SystemFactory systemFactory;

    /**
     * 创建系统
     * @return 系统id
     */
    public Long create(UpmSystemSaveCommand command) {
        return systemDomainService.create(systemFactory.createUpmSystem(command));
    }


    /**
     * 更新系统信息
     */
    public boolean change(UpmSystemOperateCommand command) {
        return systemDomainService.update(systemFactory.updateUpmSystem(command));
    }

    /**
     * 修改系统状态
     */
    public boolean changeStatus(UpmChangeStatusCommand command) {
        return systemDomainService.changeStatus(systemFactory.changeUpmSystemStatus(command));
    }

    /**
     * 删除系统信息
     */
    public boolean remove(List<Long> systemIdList) {
        return systemDomainService.remove(systemFactory.deleteUpmSystem(systemIdList));
    }
}

