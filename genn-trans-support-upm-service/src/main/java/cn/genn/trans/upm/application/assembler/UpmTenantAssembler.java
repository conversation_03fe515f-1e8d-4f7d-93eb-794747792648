package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmTenant;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO;
import cn.genn.trans.upm.interfaces.command.UpmTenantChangeCommand;
import cn.genn.trans.upm.interfaces.command.UpmTenantSaveCommand;
import cn.genn.trans.upm.interfaces.dto.UpmTenantDTO;
import cn.genn.trans.upm.interfaces.query.UpmTenantQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmTenantAssembler extends QueryAssembler<UpmTenantQuery, UpmTenantPO, UpmTenantDTO>{

    UpmTenant upmTenantSaveCommand2UpmTenant(UpmTenantSaveCommand command);

    UpmTenant upmTenantChangeCommand2UpmTenant(UpmTenantChangeCommand command);



}

