package cn.genn.trans.upm.domain.upm.repository;


import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.query.UpmResourceQuery;

import java.util.List;

/**
 * 资源repository
 * <AUTHOR>
 */
public interface ResourceRepository {

    /**
     * 查询
     *
     * @param resourceId
     * @return
     */
    UpmResource find(Long resourceId);

    /**
     * 查询
     *
     * @param resourceIdList
     * @return
     */
    List<UpmResource> find(List<Long> resourceIdList);

    List<UpmResource> findAllOfUrlNotNull();

    /**
     * 查询
     *
     * @param code
     * @return
     */
    UpmResource find(String code);


    /**
     * 查询
     *
     * @param systemId, code
     * @return
     */
    UpmResource find(Long systemId, String code);

    /**
     * 查询
     *
     * @param systemIdList
     * @return
     */
    List<UpmResource> findListBySystemId(List<Long> systemIdList);

    /**
     * 存储
     *
     * @param resource
     * @return
     */
    Long store(UpmResource resource);

    List<UpmResource> storeBatch(List<UpmResource> resources);

    /**
     * 更新
     * @param resourceList
     */
    boolean update(List<UpmResource> resourceList);

    /**
     * 删除
     * @param resourceList
     */
    boolean delete(List<UpmResource> resourceList);

    boolean deleteBySystemId(Long systemId);

    /**
     * 批量更新
     * @param resourceList
     * @return
     */
    boolean updateBatchById(List<UpmResource> resourceList);

    /**
     * 角色id查询资源
     */
    List<UpmResourcePO> selectByRoleId(Long roleId);

    List<UpmResourcePO> selectByRoleIds(List<Long> roleIds);

    Boolean batchDelete(List<Long> idList);

    List<UpmResourcePO> roleQueryList(UpmResourceQuery query);

    List<UpmResourcePO> queryBySystemId(Long systemId, ResourceTypeEnum type);

    List<UpmResource> selectAllByAuthKeyList(List<String> authKeyList);

}
