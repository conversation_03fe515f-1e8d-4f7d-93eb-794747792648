package cn.genn.trans.upm.domain.upm.repository;

import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/13
 */
public interface AccountRepository {

    Long createAccount(UpmAccount account);

    List<UpmAccountPO> saveBatch(List<UpmAccountPO> list);

    Boolean updatePassword(UpmAccount account);
}
