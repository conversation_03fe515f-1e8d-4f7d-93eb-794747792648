package cn.genn.trans.upm.application.service.action;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.pms.interfaces.dto.PmsDriverDTO;
import cn.genn.trans.upm.application.feign.DriverInfoClient;
import cn.genn.trans.upm.application.service.query.WxMiniQueryService;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.service.UserDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.infrastructure.utils.LoginFailUtil;
import cn.genn.trans.upm.infrastructure.utils.SmsUtil;
import cn.genn.trans.upm.infrastructure.utils.SsoTokenUtil;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.UserAccountInfoDTO;
import cn.genn.trans.upm.interfaces.dto.mini.MiniLoginUserDTO;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import cn.genn.trans.upm.interfaces.enums.ClientTypeEnum;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniSmsLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniWXLoginQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Service
@Slf4j
public class SsoAccountMiniActionService {

    @Resource
    private WxMiniQueryService wxMiniQueryService;
    @Resource
    private UpmUserThirdMapper userThirdMapper;
    @Resource
    private SsoAccountActionService ssoAccountActionService;
    @Resource
    private SystemRepository systemRepository;
    @Autowired
    private UserDomainService userDomainService;
    @Resource
    private SsoTokenActionService ssoTokenActionService;
    @Resource
    private DriverInfoClient driverInfoClient;
    @Resource
    private WxMinActionService wxMinActionService;
    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private UpmSeverProperties upmSeverProperties;


    /**
     * 小程序微信授权登录
     *
     * @param query
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginUserDTO miniWxLogin(SsoAccountMiniWXLoginQuery query) {
        String systemCode = query.getSystemCode();
        String appId = query.getAppid();
        WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxMiniQueryService.getUserTelephone(appId, query.getTelephoneCode());
        String telephone = wxMaPhoneNumberInfo.getPhoneNumber();
        WxMaJscode2SessionResult wxMaUserInfo = wxMiniQueryService.miniUserInfo(appId, query.getLoginCode());
        String openId = wxMaUserInfo.getOpenid();

        // 模拟数据
        // WxMaPhoneNumberInfo wxMaPhoneNumberInfo = new WxMaPhoneNumberInfo();
        // wxMaPhoneNumberInfo.setPhoneNumber("***********");
        // String telephone = wxMaPhoneNumberInfo.getPhoneNumber();
        // WxMaJscode2SessionResult wxMaUserInfo = new WxMaJscode2SessionResult();
        // wxMaUserInfo.setSessionKey("sessionkey");
        // wxMaUserInfo.setOpenid("o0ZsA7RBvvzbLInmMsBVuMKJbUjI");
        // wxMaUserInfo.setUnionid("unionid");
        // String openId = wxMaUserInfo.getOpenid();

        UpmSystem upmSystem = systemRepository.find(systemCode);

        UserWxInfoDTO userWxInfoDTO = userThirdMapper.selectByPhoneAndOpenId(telephone, openId, upmSystem.getId(),appId);
        // 注册用户流程
        Boolean firstLogin = Boolean.FALSE;
        if (ObjUtil.isNull(userWxInfoDTO)) {
            userWxInfoDTO = userDomainService.createMiniUser(upmSystem.getId(), appId, telephone, wxMaUserInfo);
            firstLogin = Boolean.TRUE;
        }
        // 账号是否冻结
        if (LoginFailUtil.isFreeze(String.valueOf(userWxInfoDTO.getId()))) {
            LoginFailUtil.loginFreezeException(String.valueOf(userWxInfoDTO.getId()));
        }
        // token生成
        MiniLoginUserDTO miniLoginUserDTO = this.generateUserToken(systemCode, userWxInfoDTO.getId(), userWxInfoDTO.getUserId());
        miniLoginUserDTO.setTelephone(telephone);
        miniLoginUserDTO.setFirstLogin(firstLogin);
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, miniLoginUserDTO, userWxInfoDTO, wxMaUserInfo, appId);
        userAuthInfoDTO.setToken(miniLoginUserDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(miniLoginUserDTO.getToken(), userAuthInfoDTO);
        final Long userId = userWxInfoDTO.getUserId();
        wxMinActionService.wxChangeSingleLogin(appId, userId, openId);
        log.info("userAuthInfoDTO:{}", new Gson().toJson(userAuthInfoDTO));
        return miniLoginUserDTO;

    }

    /**
     * 小程序短信验证码登录
     *
     * @param query
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginUserDTO miniSmsLogin(SsoAccountMiniSmsLoginQuery query) {
        String systemCode = query.getSystemCode();
        String appId = query.getAppid();
        String telephone = query.getTelephone();

        WxMaJscode2SessionResult wxMaUserInfo = wxMiniQueryService.miniUserInfo(appId, query.getLoginCode());
        String openId = wxMaUserInfo.getOpenid();

        // 模拟数据
        // WxMaJscode2SessionResult wxMaUserInfo = new WxMaJscode2SessionResult();
        // wxMaUserInfo.setSessionKey("sessionkey");
        // wxMaUserInfo.setOpenid("openid");
        // wxMaUserInfo.setUnionid("unionid");
        // String openId = wxMaUserInfo.getOpenid();
        Boolean firstLogin = Boolean.FALSE;
        UpmSystem upmSystem = systemRepository.find(systemCode);
        UserWxInfoDTO userWxInfoDTO = userThirdMapper.selectByPhoneAndOpenId(telephone, openId, upmSystem.getId(),appId);
        if (ObjUtil.isNull(userWxInfoDTO)) {
            // userWxInfoDTO = userDomainService.smsCreateMiniUser(upmSystem.getId(), appId, telephone, wxMaUserInfo);
            userWxInfoDTO = userDomainService.createMiniUser(upmSystem.getId(), appId, telephone, wxMaUserInfo);
            firstLogin = Boolean.TRUE;
        }
        // 账号是否冻结
        if (LoginFailUtil.isFreeze(String.valueOf(userWxInfoDTO.getId()))) {
            LoginFailUtil.loginFreezeException(String.valueOf(userWxInfoDTO.getId()));
        }
        // 校验输入的短信验证码
        SmsUtil.checkLoginTelephone(query.getTelephone(), query.getSmsVerificationCode(), userWxInfoDTO.getId());
        // 清空登录失败记录
        LoginFailUtil.clearFailLog(String.valueOf(userWxInfoDTO.getId()));
        // token生成
        MiniLoginUserDTO miniLoginUserDTO = this.generateUserToken(systemCode, userWxInfoDTO.getId(), userWxInfoDTO.getUserId());
        miniLoginUserDTO.setTelephone(telephone);
        miniLoginUserDTO.setFirstLogin(firstLogin);
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, miniLoginUserDTO, userWxInfoDTO, wxMaUserInfo, appId);
        userAuthInfoDTO.setToken(miniLoginUserDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(miniLoginUserDTO.getToken(), userAuthInfoDTO);
        final Long userId = userWxInfoDTO.getUserId();
        wxMinActionService.wxChangeSingleLogin(appId, userId, openId);
        log.info("userAuthInfoDTO:{}", new Gson().toJson(userAuthInfoDTO));
        return miniLoginUserDTO;
    }

    /**
     * 小程序账号登录
     *
     * @param query
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginUserDTO accountLogin(SsoAccountMiniLoginQuery query) {
        String username = query.getUsername();
        String password = query.getPassword();
        String systemCode = query.getSystemCode();
        String appId = query.getAppid();

        WxMaJscode2SessionResult wxMaUserInfo = wxMiniQueryService.miniUserInfo(appId, query.getLoginCode());
        String openId = wxMaUserInfo.getOpenid();

        // 模拟数据
        // WxMaJscode2SessionResult wxMaUserInfo = new WxMaJscode2SessionResult();
        // wxMaUserInfo.setSessionKey("sessionkey");
        // wxMaUserInfo.setOpenid("openid");
        // wxMaUserInfo.setUnionid("unionid");
        // String openId = wxMaUserInfo.getOpenid();

        UpmSystem upmSystem = systemRepository.find(systemCode);
        UserWxInfoDTO userWxInfoDTO = userThirdMapper.selectByUsernameAndOpenId(username, openId, upmSystem.getId(),appId);
        if (ObjUtil.isNull(userWxInfoDTO)) {
            userWxInfoDTO = userDomainService.accountCreateMiniUser(upmSystem.getId(), appId, username, wxMaUserInfo);
        }
        // 账号是否冻结
        Long accountId = userWxInfoDTO.getId();
        if (LoginFailUtil.isFreeze(String.valueOf(accountId))) {
            LoginFailUtil.loginFreezeException(String.valueOf(userWxInfoDTO.getId()));
        }
        UserAccountInfoDTO accountInfo = new UserAccountInfoDTO()
            .setPassword(userWxInfoDTO.getPassword())
            .setSalt(userWxInfoDTO.getSalt());
        if (!ssoAccountActionService.checkPassword(password, accountInfo)) {
            long failSize = LoginFailUtil.recordLoginFail(String.valueOf(accountId));
            LoginFailUtil.loginFailException(failSize, String.valueOf(accountId));
            // throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 清空登录失败记录
        LoginFailUtil.clearFailLog(String.valueOf(accountId));
        // token生成
        MiniLoginUserDTO miniLoginUserDTO = this.generateUserToken(systemCode, accountId, userWxInfoDTO.getUserId());
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, miniLoginUserDTO, userWxInfoDTO, wxMaUserInfo, appId);
        userAuthInfoDTO.setToken(miniLoginUserDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(miniLoginUserDTO.getToken(), userAuthInfoDTO);
        final Long userId = userWxInfoDTO.getUserId();
        wxMinActionService.wxChangeSingleLogin(appId, userId, openId);
        log.info("userAuthInfoDTO:{}", new Gson().toJson(userAuthInfoDTO));
        return miniLoginUserDTO;

    }

    /**
     * 子系统根据token获取用户信息
     *
     * @param token
     * @return
     */
    public LoginUserAuthInfoDTO miniUserInfo(String token) {
        LoginUserAuthInfoDTO authInfoDTO = SsoTokenUtil.getLoginUserAuthInfoFormToken(token);
        if (Objects.isNull(authInfoDTO)) {
            throw new BusinessException(MessageCode.USER_NOT_LOGIN);
        }
        // sessionKey不返回给前端
        authInfoDTO.setSessionKey("");
        return authInfoDTO;
    }

    private MiniLoginUserDTO generateUserToken(String systemCode, Long accountId, Long userId) {
        // 生成设备标识和ticket码 ticket码的维度 账号+设备码 防止多个web登录同账号的ticket码冲突
        String userDeviceIdentify = SsoTokenUtil.generateUserDeviceIdentify(systemCode);
        SaLoginModel saLoginModel = ssoTokenActionService.buildMiniSaLoginModel(systemCode);
        //登出
        if(upmSeverProperties.getLogin().isMiniSingleLogin()){
            List<LoginUserAuthInfoDTO> userAuthInfoDTOS = arrangeTokensByUseId(accountId, Collections.singletonList(userId));
            if(CollUtil.isNotEmpty(userAuthInfoDTOS)) {
                userAuthInfoDTOS.stream().filter(authInfoDTO -> ClientTypeEnum.MINI.equals(authInfoDTO.getClientType()))
                    .forEach(authInfoDTO -> StpUtil.logoutByTokenValue(authInfoDTO.getToken()));
            }
        }
        StpUtil.login(SsoTokenUtil.generateUserLoginId(accountId, userId), saLoginModel);
        String token = StpUtil.getTokenValue();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(MessageCode.USER_GENERATE_TOKEN_FAIL);
        }
        // 增加给前端返回密钥 分配用于验签的密钥 生成64位的密钥
        String secretKey = RandomUtil.randomString(32);

        MiniLoginUserDTO miniLoginUserDTO = new MiniLoginUserDTO();
        miniLoginUserDTO.setUserId(userId);
        miniLoginUserDTO.setToken(token);
        miniLoginUserDTO.setSecretKey(secretKey);
        miniLoginUserDTO.setUserDeviceIdentify(userDeviceIdentify);
        return miniLoginUserDTO;
    }

    private LoginUserAuthInfoDTO generateTokenSessionToken(UpmSystem upmSystem, MiniLoginUserDTO miniLoginUserDTO, UserWxInfoDTO userWxInfoDTO, WxMaJscode2SessionResult wxMaUserInfo, String appId) {
        LoginUserAuthInfoDTO authInfoDTO = new LoginUserAuthInfoDTO();
        authInfoDTO.setClientType(ClientTypeEnum.MINI);
        authInfoDTO.setAccountId(userWxInfoDTO.getId());
        authInfoDTO.setUserId(userWxInfoDTO.getUserId());
        authInfoDTO.setSecretKey(miniLoginUserDTO.getSecretKey());
        authInfoDTO.setUserDeviceIdentify(miniLoginUserDTO.getUserDeviceIdentify());
        authInfoDTO.setAuthKey(userWxInfoDTO.getAuthKey());
        authInfoDTO.setTenantId(userWxInfoDTO.getTenantId());
        authInfoDTO.setSystemId(upmSystem.getId());
        authInfoDTO.setSystemType(upmSystem.getType());
        authInfoDTO.setSystemCode(upmSystem.getCode());
        authInfoDTO.setSystemName(upmSystem.getName());
        authInfoDTO.setTelephone(userWxInfoDTO.getTelephone());
        authInfoDTO.setUsername(userWxInfoDTO.getUsername());
        // 小程序
        authInfoDTO.setUnionId(wxMaUserInfo.getUnionid());
        authInfoDTO.setAppId(appId);
        authInfoDTO.setOpenId(wxMaUserInfo.getOpenid());
        authInfoDTO.setSessionKey(wxMaUserInfo.getSessionKey());
        // 司机信息
        PmsDriverDTO driverInfo = driverInfoClient.getByUserId(userWxInfoDTO.getUserId());
        if (ObjUtil.isNotNull(driverInfo)) {
            authInfoDTO.setDriverId(driverInfo.getId());
            authInfoDTO.setDriverName(driverInfo.getName());
        }
        return authInfoDTO;
    }

    public LoginUserAuthInfoDTO generateTokenSessionToken(LoginUserAuthInfoDTO authInfoDTO, UserWxInfoDTO userWxInfoDTO) {
        authInfoDTO.setAccountId(userWxInfoDTO.getId());
        authInfoDTO.setUserId(userWxInfoDTO.getUserId());
        authInfoDTO.setAuthKey(userWxInfoDTO.getAuthKey());
        authInfoDTO.setTenantId(userWxInfoDTO.getTenantId());
        authInfoDTO.setTelephone(userWxInfoDTO.getTelephone());
        authInfoDTO.setUsername(userWxInfoDTO.getUsername());
        // 司机信息
        PmsDriverDTO driverInfo = driverInfoClient.getByUserId(userWxInfoDTO.getUserId());
        if (ObjUtil.isNotNull(driverInfo)) {
            authInfoDTO.setDriverId(driverInfo.getId());
            authInfoDTO.setDriverName(driverInfo.getName());
        }
        return authInfoDTO;
    }

    public void resetMiniAccountCache(Long accountId) {
        // 获取当前账号登录的所有token,全部更新缓存;
        List<UpmUserPO> upmUserPOList = upmUserMapper.selectByAccountId(accountId);
        if(CollUtil.isEmpty(upmUserPOList)){
            return;
        }
        List<Long> userIdList = upmUserPOList.stream().map(UpmUserPO::getId).distinct().collect(Collectors.toList());
        List<LoginUserAuthInfoDTO> userAuthInfos = this.arrangeTokensByUseId(accountId,userIdList);
        userAuthInfos.forEach(authInfoDTO -> {
            UserWxInfoDTO userWxInfoDTO = userThirdMapper.selectByPhoneAndOpenId(authInfoDTO.getTelephone(), authInfoDTO.getOpenId(), authInfoDTO.getSystemId(), authInfoDTO.getAppId());
            LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(authInfoDTO, userWxInfoDTO);
            SsoTokenUtil.setLoginUserAuthInfo(authInfoDTO.getToken(), userAuthInfoDTO);
        });
    }

    /**
     * 获取当前用户已登录的所有用户上下文信息
     * @param accountId
     * @param userIdList
     * @return
     */
    private List<LoginUserAuthInfoDTO> arrangeTokensByUseId(Long accountId,List<Long> userIdList){
        List<String> tokens = new ArrayList<>();
        userIdList.forEach(userId->{
            List<String> tokenList = StpUtil.getTokenValueListByLoginId(SsoTokenUtil.generateUserLoginId(accountId, userId));
            if(CollUtil.isNotEmpty(tokenList)){
                tokens.addAll(tokenList);
            }
        });
        List<LoginUserAuthInfoDTO> userAuthInfos= new ArrayList<>();
        tokens.forEach(token -> {
            LoginUserAuthInfoDTO authInfoDTO = SsoTokenUtil.getLoginUserAuthInfoFormToken(token);
            if (ObjUtil.isNull(authInfoDTO)) {
                return;
            }
            userAuthInfos.add(authInfoDTO);
        });
        return userAuthInfos;
    }

}
