package cn.genn.trans.upm.application.service.action;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.pms.interfaces.dto.PmsOperatorDTO;
import cn.genn.trans.upm.application.feign.OperatorInfoClient;
import cn.genn.trans.upm.application.service.query.FeishuQueryService;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.service.UserDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.utils.LoginFailUtil;
import cn.genn.trans.upm.infrastructure.utils.SmsUtil;
import cn.genn.trans.upm.infrastructure.utils.SsoTokenUtil;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.UserAccountInfoDTO;
import cn.genn.trans.upm.interfaces.dto.feishu.FeishuLoginUserDTO;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.genn.trans.upm.interfaces.query.feishu.SsoAccountFeishuLoginQuery;
import cn.genn.trans.upm.interfaces.query.feishu.SsoAccountFeishuSmsLoginQuery;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Service
public class SsoAccountFeishuActionService {

    @Resource
    private FeishuQueryService feishuQueryService;
    @Resource
    private UpmUserThirdMapper userThirdMapper;
    @Resource
    private SystemRepository systemRepository;
    @Autowired
    private UserDomainService userDomainService;
    @Resource
    private WxMinActionService wxMinActionService;
    @Resource
    private SsoTokenActionService ssoTokenActionService;
    @Resource
    private SsoAccountActionService ssoAccountActionService;
    @Resource
    private OperatorInfoClient operatorInfoClient;

    @Transactional(rollbackFor = Exception.class)
    public FeishuLoginUserDTO smsLogin(SsoAccountFeishuSmsLoginQuery query) {
        String systemCode = query.getSystemCode();
        String appId = query.getAppid();
        String telephone = query.getTelephone();
        // 飞书信息获取
        // GetUserInfoRespBody userInfo = feishuQueryService.userInfoByCode(query.getLoginCode());
        // String openId = userInfo.getOpenId();

        UpmSystem upmSystem = systemRepository.find(systemCode);
        UserWxInfoDTO userWxInfoDTO = userThirdMapper.selectByPhoneAndOpenId(telephone, null, upmSystem.getId(), null);
        if (ObjUtil.isNull(userWxInfoDTO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        // 账号是否冻结
        if (LoginFailUtil.isFreeze(String.valueOf(userWxInfoDTO.getId()))) {
            LoginFailUtil.loginFreezeException(String.valueOf(userWxInfoDTO.getId()));
        }
        // 校验输入的短信验证码
        SmsUtil.checkLoginTelephone(query.getTelephone(), query.getSmsVerificationCode(), userWxInfoDTO.getId());
        // 清空登录失败记录
        LoginFailUtil.clearFailLog(String.valueOf(userWxInfoDTO.getId()));
        // token生成
        FeishuLoginUserDTO feishuLoginUserDTO = this.generateUserToken(systemCode, userWxInfoDTO.getId(), userWxInfoDTO.getUserId());
        feishuLoginUserDTO.setTelephone(telephone);
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, feishuLoginUserDTO, userWxInfoDTO, null, appId);
        userAuthInfoDTO.setToken(feishuLoginUserDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(feishuLoginUserDTO.getToken(), userAuthInfoDTO);
        final Long userId = userWxInfoDTO.getUserId();
        // wxMinActionService.WxChangeSingleLogin(appId, userId, openId);
        log.info("userAuthInfoDTO:{}", new Gson().toJson(userAuthInfoDTO));
        return feishuLoginUserDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public FeishuLoginUserDTO accountLogin(SsoAccountFeishuLoginQuery query) {
        String username = query.getUsername();
        String password = query.getPassword();
        String systemCode = query.getSystemCode();
        String appId = query.getAppid();
        // 飞书信息获取
        // GetUserInfoRespBody userInfo = feishuQueryService.userInfoByCode(query.getLoginCode());
        // String openId = userInfo.getOpenId();

        UpmSystem upmSystem = systemRepository.find(systemCode);
        UserWxInfoDTO userWxInfoDTO = userThirdMapper.selectByUsernameAndOpenId(username, null, upmSystem.getId(), null);
        if (ObjUtil.isNull(userWxInfoDTO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        // 账号是否冻结
        Long accountId = userWxInfoDTO.getId();
        if (LoginFailUtil.isFreeze(String.valueOf(accountId))) {
            LoginFailUtil.loginFreezeException(String.valueOf(userWxInfoDTO.getId()));
        }
        UserAccountInfoDTO accountInfo = new UserAccountInfoDTO()
            .setPassword(userWxInfoDTO.getPassword())
            .setSalt(userWxInfoDTO.getSalt());
        if (!ssoAccountActionService.checkPassword(password, accountInfo)) {
            long failSize = LoginFailUtil.recordLoginFail(String.valueOf(accountId));
            LoginFailUtil.loginFailException(failSize, String.valueOf(accountId));
            // throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 清空登录失败记录
        LoginFailUtil.clearFailLog(String.valueOf(accountId));
        // token生成
        FeishuLoginUserDTO feishuLoginUserDTO = this.generateUserToken(systemCode, accountId, userWxInfoDTO.getUserId());
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, feishuLoginUserDTO, userWxInfoDTO, null, appId);
        userAuthInfoDTO.setToken(feishuLoginUserDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(feishuLoginUserDTO.getToken(), userAuthInfoDTO);
        final Long userId = userWxInfoDTO.getUserId();
        // wxMinActionService.WxChangeSingleLogin(appId, userId, openId);
        log.info("userAuthInfoDTO:{}", new Gson().toJson(userAuthInfoDTO));
        return feishuLoginUserDTO;

    }

    /**
     * 子系统根据token获取用户信息
     *
     * @param token
     * @return
     */
    public LoginUserAuthInfoDTO feishuUserInfo(String token) {
        LoginUserAuthInfoDTO authInfoDTO = SsoTokenUtil.getLoginUserAuthInfoFormToken(token);
        if (Objects.isNull(authInfoDTO)) {
            throw new BusinessException(MessageCode.USER_NOT_LOGIN);
        }
        // sessionKey不返回给前端
        authInfoDTO.setSessionKey("");
        return authInfoDTO;
    }

    private FeishuLoginUserDTO generateUserToken(String systemCode, Long accountId, Long userId) {
        // 生成设备标识和ticket码 ticket码的维度 账号+设备码 防止多个web登录同账号的ticket码冲突
        String userDeviceIdentify = SsoTokenUtil.generateUserDeviceIdentify(systemCode);
        SaLoginModel saLoginModel = ssoTokenActionService.buildMiniSaLoginModel(systemCode);
        // StpUtil.logout(SsoTokenUtil.generateUserLoginId(accountId, userId));
        StpUtil.login(SsoTokenUtil.generateUserLoginId(accountId, userId), saLoginModel);
        String token = StpUtil.getTokenValue();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(MessageCode.USER_GENERATE_TOKEN_FAIL);
        }
        // 增加给前端返回密钥 分配用于验签的密钥 生成64位的密钥
        String secretKey = RandomUtil.randomString(32);

        FeishuLoginUserDTO feishuLoginUserDTO = new FeishuLoginUserDTO();
        feishuLoginUserDTO.setUserId(userId);
        feishuLoginUserDTO.setToken(token);
        feishuLoginUserDTO.setSecretKey(secretKey);
        feishuLoginUserDTO.setUserDeviceIdentify(userDeviceIdentify);
        return feishuLoginUserDTO;
    }

    private LoginUserAuthInfoDTO generateTokenSessionToken(UpmSystem upmSystem, FeishuLoginUserDTO feishuLoginUserDTO, UserWxInfoDTO userWxInfoDTO, GetUserInfoRespBody feishuUserInfo, String appId) {
        LoginUserAuthInfoDTO authInfoDTO = new LoginUserAuthInfoDTO();
        authInfoDTO.setAccountId(userWxInfoDTO.getId());
        authInfoDTO.setUserId(userWxInfoDTO.getUserId());
        authInfoDTO.setSecretKey(feishuLoginUserDTO.getSecretKey());
        authInfoDTO.setUserDeviceIdentify(feishuLoginUserDTO.getUserDeviceIdentify());
        authInfoDTO.setAuthKey(userWxInfoDTO.getAuthKey());
        authInfoDTO.setTenantId(userWxInfoDTO.getTenantId());
        authInfoDTO.setSystemId(upmSystem.getId());
        authInfoDTO.setSystemType(upmSystem.getType());
        authInfoDTO.setSystemCode(upmSystem.getCode());
        authInfoDTO.setSystemName(upmSystem.getName());
        authInfoDTO.setTelephone(userWxInfoDTO.getTelephone());
        authInfoDTO.setUsername(userWxInfoDTO.getUsername());
        if (upmSystem.getType().equals(SystemTypeEnum.OPERATOR)) {
            PmsOperatorDTO pmsOperatorDTO = operatorInfoClient.getByTenantId(userWxInfoDTO.getTenantId());
            authInfoDTO.setOperatorId(pmsOperatorDTO.getId());
            authInfoDTO.setOperatorCode(pmsOperatorDTO.getCode());
            authInfoDTO.setOperatorName(pmsOperatorDTO.getName());
        }
        // 小程序
        // authInfoDTO.setUnionId(wxMaUserInfo.getUnionid());
        // authInfoDTO.setAppId(appId);
        // authInfoDTO.setOpenId(wxMaUserInfo.getOpenid());
        // authInfoDTO.setSessionKey(wxMaUserInfo.getSessionKey());
        return authInfoDTO;
    }

}
