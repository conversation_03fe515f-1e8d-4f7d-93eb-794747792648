package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmRoleAuthResourceRelPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmRoleAuthResourceRelMapper extends BaseMapper<UpmRoleAuthResourceRelPO> {

    /**
     * 清除角色数据
     */
    void deleteByRoleId(Long roleId);

    /**
     * 批量写入
     *
     * @param list
     * @return
     */
    void saveBatch(List<UpmRoleAuthResourceRelPO> list);

    default void deleteByAuthResourceIds(List<Long> authResourceIds){
        LambdaQueryWrapper<UpmRoleAuthResourceRelPO> wrapper = Wrappers.lambdaQuery(UpmRoleAuthResourceRelPO.class)
            .in(UpmRoleAuthResourceRelPO::getAuthResourceId, authResourceIds);
        delete(wrapper);
    }
}
