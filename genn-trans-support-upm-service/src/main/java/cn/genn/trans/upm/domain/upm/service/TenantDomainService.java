package cn.genn.trans.upm.domain.upm.service;

import cn.genn.trans.upm.domain.upm.model.entity.UpmTenant;
import cn.genn.trans.upm.domain.upm.repository.TenantRepository;
import cn.genn.trans.upm.infrastructure.converter.UpmTenantConverter;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/11
 */
@Service
public class TenantDomainService {

    @Resource
    private UpmTenantConverter converter;
    @Resource
    private TenantRepository tenantRepository;
    @Resource
    private UpmTenantMapper upmTenantMapper;

    public UpmTenantPO save(UpmTenant upmTenant) {
        UpmTenantPO upmTenantPO = converter.entity2PO(upmTenant);
        upmTenantMapper.insert(upmTenantPO);
        return upmTenantPO;
    }

    public boolean change(UpmTenant upmTenant) {
        UpmTenantPO upmTenantPO = converter.entity2PO(upmTenant);
        return tenantRepository.update(upmTenantPO);
    }

    public boolean delete(Long id){
        upmTenantMapper.deleteById(id);
        return true;
    }

    public boolean updateStatus(List<Long> idList, StatusEnum status){
        return tenantRepository.updateStatus(idList,status);
    }



}
