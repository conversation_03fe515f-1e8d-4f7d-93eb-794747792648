package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.AccountTypeEnum;
import cn.genn.trans.upm.interfaces.enums.LoginTypeEnum;
import cn.genn.trans.upm.interfaces.enums.PasswrodStatusEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmAccountPO对象
 *
 * <AUTHOR>
 * @desc 账号表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_account", autoResultMap = true)
public class UpmAccountPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 账号
     */
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 盐
     */
    @TableField("salt")
    private String salt;

    /**
     * 密码状态
     */
    @TableField("password_status")
    private PasswrodStatusEnum passwordStatus;

    /**
     * 密码修改时间
     */
    @TableField(value = "pd_update_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime pdUpdateTime;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 登录类型（phone:手机，normal:普通账号）
     */
    @TableField("login_type")
    private LoginTypeEnum loginType;

    /**
     * 类型（system:系统账号，default:默认账号；unity:统一登录账号）
     */
    @TableField("type")
    private AccountTypeEnum type;

    /**
     * 手机号
     */
    @TableField("telephone")
    private String telephone;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

