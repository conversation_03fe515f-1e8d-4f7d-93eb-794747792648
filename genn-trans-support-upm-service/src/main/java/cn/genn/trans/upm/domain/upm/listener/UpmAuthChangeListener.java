package cn.genn.trans.upm.domain.upm.listener;

import cn.genn.spring.boot.starter.event.spring.component.SpringEventAsyncListener;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEvent;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEventDimensionEnum;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.domain.upm.repository.UserRepository;
import cn.genn.trans.upm.domain.upm.service.ResourceDominOfCasbinRuleService;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 对于权限变更的事件监听处理
 */
@Component
@Slf4j
public class UpmAuthChangeListener extends SpringEventAsyncListener<UpmAuthChangeEvent> {

    @Autowired
    private ResourceDominOfCasbinRuleService casbinRuleService;
    @Resource
    private UserRepository userRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private ResourceRepository resourceRepository;


    @Override
    protected void onMessage(UpmAuthChangeEvent event) {
        log.info("UpmAuthChangeListener onMessage body={}", JSONUtil.toJsonStr(event));
        /**
         * 进行全量更新
         */
        if (event.getDimension() == UpmAuthChangeEventDimensionEnum.ALL) {
            casbinRuleService.convertUrlOfResourceToCasbinRule();
            return;
        }
        try {
            Table<Long, Long, List<UpmRole>> table = getAllRoleAndResourceData(); // 优化：仅调用一次

            switch (event.getDimension()) {
                case ALL:
//                    casbinRuleService.convertUrlOfResourceToCasbinRule();
                    break;
                // 如果更改的是系统维度的数据 需要计算出该系统下的所有租户进行更新
                case SYSTEM:
                    reloadRulesForSystem(event.getSystemId(), table);
                    break;
                // 如果更改的是租户维度的数据 需要计算出该租户下的所有系统进行更新
                case TENANT:
                    reloadRulesForTenant(event.getTenantId(), table);
                    break;
                // 如果更改的是用户的数据 需要计算出该用户绑定的系统id和租户id进行更新
                case USER:
                    reloadRulesForUser(event.getUserId(), table);
                    break;
                // 如果更改的是角色维度的数据 需要计算出该角色绑定的系统id和租户id进行更新
                case ROLE:
                    reloadRulesForRole(event.getRoleId(), table);
                    break;
                // 更新资源 查询资源绑定的系统进行更新
                case RESOURCE:
                    reloadRulesForResource(event.getResourceId(), table);
                    break;
                default:
                    log.warn("Unsupported dimension: {}", event.getDimension());
            }
        } catch (Exception e) {
            log.error("Error processing UpmAuthChangeEvent: ", e);
        }
    }

    private Table<Long, Long, List<UpmRole>> getAllRoleAndResourceData() {
        Table<Long, Long, List<UpmRole>> table = casbinRuleService.getAllRoleAndResourceTable();
        log.info("getAllRoleAndResourceData result: {}", JSONUtil.toJsonStr(table));
        return table;
    }

    private void reloadRulesForSystem(Long systemId, Table<Long, Long, List<UpmRole>> table) {
        table.cellSet().forEach(cell -> {
            if (Objects.equals(cell.getRowKey(), systemId)) {
                casbinRuleService.reloadCasbinRule(cell.getRowKey(), cell.getColumnKey(), cell.getValue());
            }
        });
    }

    private void reloadRulesForTenant(Long tenantId, Table<Long, Long, List<UpmRole>> table) {
        table.cellSet().forEach(cell -> {
            if (Objects.equals(cell.getColumnKey(), tenantId)) {
                casbinRuleService.reloadCasbinRule(cell.getRowKey(), cell.getColumnKey(), cell.getValue());
            }
        });
    }

    private void reloadRulesForUser(Long userId, Table<Long, Long, List<UpmRole>> table) {
        UpmUserPO upmUserPO = userRepository.selectById(userId);
        if (upmUserPO != null) {
            reloadRulesForSystemAndTenant(upmUserPO.getSystemId(), upmUserPO.getTenantId(), table);
        }
    }

    private void reloadRulesForRole(Long roleId, Table<Long, Long, List<UpmRole>> table) {
        UpmRolePO upmRolePO = roleRepository.selectById(roleId);
        if (upmRolePO != null) {
            reloadRulesForSystemAndTenant(upmRolePO.getSystemId(), upmRolePO.getTenantId(), table);
        }
    }

    private void reloadRulesForResource(Long resourceId, Table<Long, Long, List<UpmRole>> table) {
        UpmResource upmResource = resourceRepository.find(resourceId);
        if (upmResource != null) {
            table.cellSet().forEach(cell -> {
                if (Objects.equals(cell.getRowKey(), upmResource.getUpmSystem().getId())) {
                    casbinRuleService.reloadCasbinRule(cell.getRowKey(), cell.getColumnKey(), cell.getValue());
                }
            });
        }
        casbinRuleService.initUrlGlobalSet();
    }

    private void reloadRulesForSystemAndTenant(Long systemId, Long tenantId, Table<Long, Long, List<UpmRole>> table) {
        table.cellSet().forEach(cell -> {
            if (Objects.equals(cell.getRowKey(), systemId) && Objects.equals(cell.getColumnKey(), tenantId)) {
                casbinRuleService.reloadCasbinRule(cell.getRowKey(), cell.getColumnKey(), cell.getValue());
            }
        });
    }

}
