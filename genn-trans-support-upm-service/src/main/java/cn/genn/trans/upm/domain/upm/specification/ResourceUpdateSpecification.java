package cn.genn.trans.upm.domain.upm.specification;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.infrastructure.common.AbstractSpecification;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;

/**
 * 资源变更Specification
 *
 * <AUTHOR>
 **/
public class ResourceUpdateSpecification extends AbstractSpecification<UpmResource> {

    private ResourceRepository repository;

    public ResourceUpdateSpecification(ResourceRepository repository) {
        this.repository = repository;
    }

    @Override
    public boolean isSatisfiedBy(UpmResource upmResource) {
        // 编码校验
        if (upmResource.getCode() != null) {
            UpmResource existSystemResource = repository.find(upmResource.getUpmSystem().getId(), upmResource.getCode());
            if (existSystemResource != null && !existSystemResource.getId().equals(upmResource.getId())) {
                throw new BusinessException(MessageCode.RESOURCE_CODE_EXIST_ERROR);
            }
        }
        // 其他校验
        return true;
    }
}
