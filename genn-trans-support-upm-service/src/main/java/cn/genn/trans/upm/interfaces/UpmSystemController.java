package cn.genn.trans.upm.interfaces;

import cn.genn.trans.upm.application.service.action.UpmSystemActionService;
import cn.genn.trans.upm.application.service.query.UpmSystemQueryService;
import cn.genn.trans.upm.interfaces.dto.UpmSystemDTO;
import cn.genn.trans.upm.interfaces.query.SystemQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "系统管理")
@RestController
@RequestMapping("/upmSystem")
public class UpmSystemController {

    @Resource
    private UpmSystemQueryService queryService;
    @Resource
    private UpmSystemActionService actionService;

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public UpmSystemDTO get(@ApiParam(name = "id", required = true) @Validated @RequestParam Long id) {
        return queryService.get(id);
    }

    /**
     * systemCode查询系统信息
     *
     * @param systemCode
     * @return
     */
    @PostMapping("/getByCode")
    @ApiOperation(value = "根据systemCode查询")
    public UpmSystemDTO getByCode(@ApiParam(name = "systemCode", required = true) @Validated @RequestParam String systemCode) {
        return queryService.getByCode(systemCode);
    }

    /**
     * idlist查询信息
     * @param idList
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询系统列表")
    List<UpmSystemDTO> list(@RequestBody List<Long> idList){
        return queryService.list(idList);
    }

    @GetMapping("/listAll")
    @ApiOperation(value = "查询所有系统列表")
    public List<UpmSystemDTO> listAll(){
        return queryService.listAll();
    }

    @PostMapping("/queryList")
    @ApiOperation(value = "条件查询系统列表")
    public List<UpmSystemDTO> queryList(@RequestBody SystemQuery query){
        return queryService.queryList(query);
    }

    // /**
    //  * 分页查询列表
    //  *
    //  * @param query 查询条件
    //  * @return
    //  */
    // @PostMapping("/page")
    // @ApiOperation(value = "分页查询列表")
    // public PageResultDTO<UpmSystemDTO> page(@ApiParam(value = "查询类") @Validated @RequestBody UpmSystemQuery query) {
    //     return queryService.page(query);
    // }


    // /**
    //  * 查询列表
    //  *
    //  * @param query 查询条件
    //  * @return
    //  */
    // @PostMapping("/queryList")
    // @ApiOperation(value = "查询列表")
    // public List<UpmSystemDTO> queryList(@ApiParam(value = "查询类") @RequestBody UpmSystemQuery query) {
    //     return queryService.queryList(query);
    // }



    // /**
    //  * 添加系统
    //  *
    //  * @param command
    //  * @return Long
    //  */
    // @PostMapping("/save")
    // @ApiOperation(value = "添加系统")
    // public Long save(@ApiParam(value = "系统数据操作类") @Validated @RequestBody UpmSystemSaveCommand command) {
    //     return actionService.create(command);
    // }
    //
    // /**
    //  * 修改系统
    //  *
    //  * @param command
    //  * @return Boolean
    //  */
    // @PostMapping("/change")
    // @ApiOperation(value = "修改系统")
    // public Boolean change(@ApiParam(value = "系统数据操作类") @Validated @RequestBody UpmSystemOperateCommand command) {
    //     return actionService.change(command);
    // }

    // /**
    //  * 批量启用停用
    //  *
    //  * @param command
    //  * @return
    //  */
    // @PostMapping("/changeStatus")
    // @ApiOperation(value = "批量启用停用")
    // public Boolean changeStatus(@ApiParam(value = "批量启用禁用") @RequestBody @Validated UpmChangeStatusCommand command) {
    //     return actionService.changeStatus(command);
    // }


    // /**
    //  * 删除系统
    //  *
    //  * @param systemIdList
    //  * @return Boolean
    //  */
    // @PostMapping("/remove")
    // @ApiOperation(value = "删除系统")
    // public Boolean remove(@ApiParam(name = "systemIdList", required = true) @Validated @RequestBody List<Long> systemIdList) {
    //     return actionService.remove(systemIdList);
    // }



}

