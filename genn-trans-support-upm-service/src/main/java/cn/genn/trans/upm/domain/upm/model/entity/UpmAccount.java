package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.domain.upm.model.valobj.Password;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import cn.genn.trans.upm.interfaces.enums.AccountTypeEnum;
import cn.genn.trans.upm.interfaces.enums.LoginTypeEnum;
import cn.genn.trans.upm.interfaces.enums.PasswrodStatusEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UpmAccount implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 账号
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐
     */
    private String salt;

    /**
     * 密码状态
     */
    private PasswrodStatusEnum passwordStatus;

    /**
     * 密码修改时间
     */
    private LocalDateTime pdUpdateTime;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 用户
     */
    private UpmUser user;

    /**
     * 备注
     */
    private String remark;

    /**
     * 登录类型（phone:手机，normal:普通账号）
     */
    private LoginTypeEnum loginType;

    /**
     * 类型（system:系统账号；default:默认账号；unity:统一登录账号）
     */
    private AccountTypeEnum type;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 逻辑删除
     */
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private Long updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;


    public static UpmAccount fromPo(UpmAccountPO po) {
        return UpmAccount.builder()
            .id(po.getId())
            .username(po.getUsername())
            .password(po.getPassword())
            .salt(po.getSalt())
            .status(po.getStatus())
            .user(UpmUser.builder().build())
            .remark(po.getRemark())
            .loginType(po.getLoginType())
            .type(po.getType())
            .passwordStatus(po.getPasswordStatus())
            .pdUpdateTime(po.getPdUpdateTime())
            .telephone(po.getTelephone())
            .deleted(po.getDeleted())
            .createTime(po.getCreateTime())
            .createUserId(po.getCreateUserId())
            .createUserName(po.getCreateUserName())
            .updateTime(po.getUpdateTime())
            .updateUserId(po.getUpdateUserId())
            .updateUserName(po.getUpdateUserName())
            .build();
    }

    public static UpmAccount fromOfficialAccountUser(OfficialAccountUser officialAccountUser) {
        UpmAccount account = UpmAccount.builder()
            .systemId(officialAccountUser.getSystemId())
            .loginType(officialAccountUser.getLoginType())
            .type(officialAccountUser.getType())
            .telephone(officialAccountUser.getTelephone())
            .username(officialAccountUser.getUsername())
            .build();
        Password password = Password.create(officialAccountUser.getPassword());
        account.setPassword(password.getPassword());
        account.setSalt(password.getSalt());
        return account;
    }
}
