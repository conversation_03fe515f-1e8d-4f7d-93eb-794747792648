package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmAuthGroupPO对象
 *
 * <AUTHOR>
 * @desc 权限组
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_auth_group", autoResultMap = true)
public class UpmAuthGroupPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 权限组key,根据定义决定业务含义
     */
    @TableField("auth_key")
    private String authKey;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * 类型：pl平台,op运营,ca承运
     */
    @TableField("type")
    private AuthGroupEnum type;

    /**
     * 删除：0未删除，1删除
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}

