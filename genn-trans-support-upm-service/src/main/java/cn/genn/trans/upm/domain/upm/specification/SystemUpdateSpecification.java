package cn.genn.trans.upm.domain.upm.specification;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.infrastructure.common.AbstractSpecification;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;

/**
 * 系统修改Specification
 *
 * <AUTHOR>
 **/
public class SystemUpdateSpecification extends AbstractSpecification<UpmSystem> {

    private SystemRepository repository;

    public SystemUpdateSpecification(SystemRepository repository) {
        this.repository = repository;
    }

    @Override
    public boolean isSatisfiedBy(UpmSystem upmSystem) {
        // 编码校验
        if (upmSystem.getCode() != null) {
            UpmSystem existSystem = repository.find(upmSystem.getCode());
            if (existSystem != null && !existSystem.getId().equals(upmSystem.getId())) {
                throw new BusinessException(MessageCode.SYSTEM_CODE_EXIST_ERROR);
            }
        }
        // 其他校验
        return true;
    }
}
