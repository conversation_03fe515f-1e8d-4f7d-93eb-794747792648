package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmResourceMapper extends BaseMapper<UpmResourcePO> {

    List<UpmResourcePO> selectByRoleId(Long roleId);

    List<UpmResourcePO> selectByRoleIds(@Param("list") List<Long> roleIdList, @Param("type") ResourceTypeEnum type);

    List<UpmResourcePO> selectByAuthKey(@Param("authKey")String authKey,@Param("type")ResourceTypeEnum type);

    List<UpmResourcePO> selectByAuthKeyList(@Param("authKeyList")List<String> authKeyList);

    default UpmResourcePO selectByTitle(Long systemId, String title,Long pid) {
        LambdaQueryWrapper<UpmResourcePO> wrapper = Wrappers.lambdaQuery(UpmResourcePO.class)
            .eq(UpmResourcePO::getSystemId, systemId)
            .eq(UpmResourcePO::getType,ResourceTypeEnum.MENU)
            .eq(UpmResourcePO::getPid,pid)
            .eq(UpmResourcePO::getTitle, title);
        return selectOne(wrapper);
    }

    default UpmResourcePO selectByPath(Long systemId, String path) {
        LambdaQueryWrapper<UpmResourcePO> wrapper = Wrappers.lambdaQuery(UpmResourcePO.class)
            .eq(UpmResourcePO::getSystemId, systemId)
            .eq(UpmResourcePO::getType,ResourceTypeEnum.MENU)
            .eq(UpmResourcePO::getPath, path);
        return selectOne(wrapper);
    }

    default List<UpmResourcePO> queryByPid(List<Long> idList){
        LambdaQueryWrapper<UpmResourcePO> wrapper = Wrappers.lambdaQuery(UpmResourcePO.class)
            .in(UpmResourcePO::getPid, idList);
        return selectList(wrapper);
    }




}
