package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.infrastructure.repository.po.UpmSystemPO;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmSystemMapper extends BaseMapper<UpmSystemPO> {

    default UpmSystemPO selectByCode(String systemCode){
        return selectOne(Wrappers.lambdaQuery(UpmSystemPO.class)
            .eq(UpmSystemPO::getCode, systemCode)
            .last("limit 1"));
    }

    default List<UpmSystemPO> selectByType(SystemTypeEnum type){
        LambdaQueryWrapper<UpmSystemPO> wrapper = Wrappers.lambdaQuery(UpmSystemPO.class)
            .eq(UpmSystemPO::getType, type)
            .eq(UpmSystemPO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectList(wrapper);
    }
}
