package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.domain.upm.repository.UserRoleRepository;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserRoleMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserRolePO;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.el.stream.Stream;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public class UserRoleRepositoryImpl extends ServiceImpl<UpmUserRoleMapper, UpmUserRolePO> implements UserRoleRepository {
    /**
     * 关联角色
     *
     * @param upmUser
     */
    @Override
    public Boolean relatedRole(UpmUser upmUser, boolean clearSign) {
        Long userId = upmUser.getId();
        if(clearSign){
            LambdaQueryWrapper<UpmUserRolePO> wrapper = Wrappers.lambdaQuery(UpmUserRolePO.class)
                .eq(UpmUserRolePO::getUserId, userId);
            baseMapper.delete(wrapper);
        }
        if(CollectionUtil.isNotEmpty(upmUser.getRoleIdList())){
            List<UpmUserRolePO> list = upmUser.getRoleIdList().stream().map(roleId -> {
                return new UpmUserRolePO().setUserId(userId).setRoleId(roleId);
            }).collect(Collectors.toList());
            return saveBatch(list);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchRelatedRoleBySystemType(List<UpmUser> userList, boolean clearSign) {
        if (CollectionUtil.isEmpty(userList)) {
            return Boolean.TRUE;
        }
        if (clearSign) {
            LambdaQueryWrapper<UpmUserRolePO> wrapper = Wrappers.lambdaQuery(UpmUserRolePO.class)
                .in(UpmUserRolePO::getUserId, userList.stream().map(UpmUser::getId).collect(Collectors.toList()));
            baseMapper.delete(wrapper);
        }
        List<UpmUserRolePO> upmUserRoleList = userList.stream()
            .filter(user -> !CollectionUtils.isEmpty(user.getRoleIdList()))
            .map(user -> {
                List<UpmUserRolePO> list = user.getRoleIdList().stream().map(roleId -> {
                    return new UpmUserRolePO().setUserId(user.getId()).setRoleId(roleId);
                }).collect(Collectors.toList());
                return list;
            }).flatMap(List::stream).collect(Collectors.toList());
        saveBatch(upmUserRoleList);
        return Boolean.TRUE;
    }

    @Override
    public Boolean saveBatch(List<UpmUserRolePO> userRolePOList) {
        super.saveBatch(userRolePOList);
        return true;
    }


}
