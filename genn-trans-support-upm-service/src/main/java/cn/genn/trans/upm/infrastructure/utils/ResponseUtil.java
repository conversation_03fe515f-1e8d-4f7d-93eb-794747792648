package cn.genn.trans.upm.infrastructure.utils;

import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class ResponseUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResponseUtil.class);

    public static boolean handleError(HttpServletResponse response) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        ResponseResult responseResult = new ResponseResult();
//        responseResult.setCode("501");
//        responseResult.setMsg("登录失败");
//        responseResult.setErr("");
        response.getWriter().print(JsonUtils.toJson(responseResult));
        response.flushBuffer();
        return false;
    }

    public static void write(HttpServletResponse response, String str) {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/json; charset=utf-8");
            PrintWriter out = response.getWriter();
            out.println(str);
            out.flush();
            out.close();
        } catch (Exception e) {
            LOGGER.error("e={}", e);
        }
    }

    public static void write(HttpServletResponse response, Object o) {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/json; charset=utf-8");
            PrintWriter out = response.getWriter();
            out.println(JSONUtil.toJsonStr(o));
            out.flush();
            out.close();
        } catch (Exception e) {
            LOGGER.error("e={}", e);
        }
    }

    public static void write(HttpServletResponse response, int status, Object o) {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/json; charset=utf-8");
            response.setStatus(status);
            PrintWriter out = response.getWriter();
            out.println(JSONUtil.toJsonStr(o));
            out.flush();
            out.close();
        } catch (Exception e) {
            LOGGER.error("e={}", e);
        }
    }
}
