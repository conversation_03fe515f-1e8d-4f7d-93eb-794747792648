package cn.genn.trans.upm.infrastructure.common;

import java.io.Serializable;

/**
 * Entity interface
 *
 * <AUTHOR>
 **/
public interface Entity<T> extends Serializable {

    /**
     * Entities compare by identity, not by attributes.
     *
     * @param other The other entity.
     * @return true if the identities are the same, regardless of other attributes.
     */
    boolean sameIdentityAs(T other);
}
