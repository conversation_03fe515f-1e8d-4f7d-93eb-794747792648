package cn.genn.trans.upm.application.service.action;

import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEvent;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEventDimensionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Date: 2024/4/26
 * @Author: kang<PERSON>an
 */
@Service
@Slf4j
public class SpringEventPublishService {
    @Resource
    private SpringEventPublish springEventPublish;

    public void publishUpmAuthChangeEvent(UpmAuthChangeEventDimensionEnum dimension, Long value) {
        springEventPublish.publish(new UpmAuthChangeEvent(this, dimension, value));
    }
}
