package cn.genn.trans.upm.application.feign;

import cn.genn.trans.pms.interfaces.api.IDriverInfoService;
import cn.genn.trans.pms.interfaces.dto.PmsDriverDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/2
 */
@Component
public class DriverInfoClient {

    @Resource
    private IDriverInfoService driverInfoService;

    public PmsDriverDTO getByUserId(Long userId){
        return driverInfoService.getByUserId(userId);
    }
}
