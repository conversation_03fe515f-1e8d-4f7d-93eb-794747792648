package cn.genn.trans.upm.domain.upm.model.valobj;

import cn.genn.trans.upm.infrastructure.utils.UpmHmacDigestUtil;
import lombok.Getter;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 密码值对象
 *
 * <AUTHOR>
 */
@Getter
public class Password {
    /**
     * 密码
     */
    private final String password;

    /**
     * 盐
     */
    private final String salt;

    public Password(String password, String salt) {
        if (StringUtils.isEmpty(password)) {
            throw new IllegalArgumentException("密码不能为空");
        }
        this.password = password;
        this.salt = salt;
    }

    public static Password create(String passwordStr) {
        String salt = RandomStringUtils.randomAlphanumeric(20);
        String password = UpmHmacDigestUtil.hmacSha256AsHex(salt, passwordStr);
        return new Password(password, salt);
    }

    public static Password create(String passwordStr, String salt) {
        if (passwordStr.length() < 6) {
            throw new IllegalArgumentException("密码长度不能小于6");
        }

        String password = UpmHmacDigestUtil.hmacSha256AsHex(salt, passwordStr);
        return new Password(password, salt);
    }

}
