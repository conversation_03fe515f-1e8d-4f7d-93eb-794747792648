package cn.genn.trans.upm.domain.upm.service;

import cn.genn.trans.upm.domain.upm.model.entity.AuthType;
import cn.genn.trans.upm.infrastructure.converter.AuthTypeConverter;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeRoleTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Service
@Slf4j
public class AuthTypeDomainService {

    @Resource
    private UpmAuthTypeTemplateMapper authTypeTemplateMapper;
    @Resource
    private AuthTypeConverter authTypeConverter;
    @Resource
    private UpmAuthTypeRoleTemplateMapper authTypeRoleTemplateMapper;


    public void update(AuthType authType) {
        authTypeTemplateMapper.updateById(authTypeConverter.entity2PO(authType));
    }

    public Boolean roleTemplateSave(UpmAuthTypeRoleTemplatePO po) {
        authTypeRoleTemplateMapper.insert(po);
        return true;
    }

    public Boolean roleTemplateUpdate(UpmAuthTypeRoleTemplatePO po) {
        authTypeRoleTemplateMapper.updateById(po);
        return true;
    }

    public Boolean roleTemplateDeletes(Long id) {
        authTypeRoleTemplateMapper.deleteById(id);
        return true;
    }

}
