package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.trans.upm.domain.upm.repository.TenantRepository;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TenantRepositoryImpl extends ServiceImpl<UpmTenantMapper, UpmTenantPO> implements TenantRepository {

    @Override
    public boolean update(UpmTenantPO upmTenantPO) {
        LambdaUpdateWrapper<UpmTenantPO> wrapper = Wrappers.lambdaUpdate(UpmTenantPO.class)
            .eq(UpmTenantPO::getId, upmTenantPO.getId())
            .set(StrUtil.isNotBlank(upmTenantPO.getName()), UpmTenantPO::getName, upmTenantPO.getName())
            .set(UpmTenantPO::getRemark, upmTenantPO.getRemark());
        baseMapper.update(wrapper);
        return true;
    }

    /**
     * 批量修改状态
     *
     * @param idList
     * @param status
     */
    @Override
    public boolean updateStatus(List<Long> idList, StatusEnum status) {
        LambdaUpdateWrapper<UpmTenantPO> wrapper = Wrappers.lambdaUpdate(UpmTenantPO.class)
            .in(UpmTenantPO::getId, idList)
            .set(UpmTenantPO::getStatus, status);
        baseMapper.update(wrapper);
        return true;
    }

}
