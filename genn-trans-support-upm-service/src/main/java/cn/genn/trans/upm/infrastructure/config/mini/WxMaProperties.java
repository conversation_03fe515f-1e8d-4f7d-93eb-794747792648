package cn.genn.trans.upm.infrastructure.config.mini;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Data
@ConfigurationProperties(prefix = "genn.wx.miniapp")
public class WxMaProperties {

    private List<Config> configs;

    @Data
    public static class Config {
        /**
         * 司机系统code
         */
        private String systemCode;

        /**
         * 设置微信小程序的appid
         */
        private String appid;

        /**
         * 设置微信小程序的Secret
         */
        private String secret;

        /**
         * 设置微信小程序消息服务器配置的token
         */
        private String token;

        /**
         * 设置微信小程序消息服务器配置的EncodingAESKey
         */
        private String aesKey;

        /**
         * 消息格式，XML或者JSON
         */
        private String msgDataFormat;
    }

    /**
     * key:systemCode
     * value:小程序信息
     * @return
     */
    public Map<String,Config> getConfigMap(){
        if(CollUtil.isEmpty(configs)){
            return Collections.emptyMap();
        }
        return configs.stream().collect(Collectors.toMap(Config::getSystemCode, Function.identity()));
    }

}
