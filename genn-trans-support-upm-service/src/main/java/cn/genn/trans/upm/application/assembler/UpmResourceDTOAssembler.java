package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.DTOAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmResourceDTOAssembler extends DTOAssembler<UpmResource, UpmResourceDTO> {

    @Override
    @Mappings({
        @Mapping(source = "code", target = "baseCode")
    })
    UpmResourceDTO entity2DTO(UpmResource entity);
}

