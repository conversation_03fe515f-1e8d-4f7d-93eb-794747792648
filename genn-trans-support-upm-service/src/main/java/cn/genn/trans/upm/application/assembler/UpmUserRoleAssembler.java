package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserRolePO;
import cn.genn.trans.upm.interfaces.dto.UpmUserRoleDTO;
import cn.genn.trans.upm.interfaces.query.UpmUserRoleQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmUserRoleAssembler extends QueryAssembler<UpmUserRoleQuery, UpmUserRolePO, UpmUserRoleDTO>{
}

