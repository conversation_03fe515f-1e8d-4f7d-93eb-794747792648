package cn.genn.trans.upm.infrastructure.utils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @Description: 树操作方法工具类
 * @Author: 公众号：赵侠客
 * @Copyright: Copyright (c) 赵侠客
 * @Date: 2024-07-22 10:42
 * @Version: 1.0
 */
public class TreeBuildUtil {
    /**
     * 使用Map合成树
     *
     * @param menuList       需要合成树的List
     * @param pId            对象中的父ID字段,如:Menu:getPid
     * @param id             对象中的id字段 ,如：Menu:getId
     * @param rootCheck      判断E中为根节点的条件，如：x->x.getPId()==-1L , x->x.getParentId()==null,x->x.getParentMenuId()==0
     * @param setSubChildren E中设置下级数据方法，如： Menu::setSubMenus
     * @param <T>            ID字段类型
     * @param <E>            泛型实体对象
     * @return
     */
    public static <T, E> List<E> makeTree(List<E> menuList, Function<E, T> pId,
                                          Function<E, T> id, Predicate<E> rootCheck, BiConsumer<E, List<E>> setSubChildren) {
        //按原数组顺序构建父级数据Map，使用Optional考虑pId为null
        Map<Optional<T>, List<E>> parentMenuMap = menuList.stream().collect(Collectors.groupingBy(
                node -> Optional.ofNullable(pId.apply(node)),
                LinkedHashMap::new,
                Collectors.toList()
        ));
        List<E> result = new ArrayList<>();
        for (E node : menuList) {
            //添加到下级数据中
            setSubChildren.accept(node, parentMenuMap.get(Optional.ofNullable(id.apply(node))));
            //如里是根节点，加入结构
            if (rootCheck.test(node)) {
                result.add(node);
            }
        }
        return result;
    }


//    public static <T, E> List<E> makeTree(List<E> menuList, Function<E, T> pId,
//                                          Function<E, T> id, Predicate<E> rootCheck,
//                                          BiConsumer<E, List<E>> setSubChildren,
//                                          Comparator<T> keyComparator,
//                                          Comparator<E> valueComparator) {
//        //按原数组顺序构建父级数据Map，使用Optional考虑pId为null
//        Map<Optional<T>, List<E>> parentMenuMap = menuList.stream().collect(Collectors.groupingBy(
//            node -> Optional.ofNullable(pId.apply(node)),
//            LinkedHashMap::new,
//            Collectors.toList()
//        ));
//
//        // 对 parentMenuMap 的键进行排序
//        List<Map.Entry<Optional<T>, List<E>>> sortedEntries = parentMenuMap.entrySet().stream()
//            .sorted(Map.Entry.comparingByKey(keyComparator))
//            .collect(Collectors.toList());
//
//
//        // 对每个 List<E> 中的元素进行排序
//        for (Map.Entry<Optional<T>, List<E>> entry : sortedEntries) {
//            List<E> list = entry.getValue();
//            list.sort(valueComparator); // 按给定的 Comparator 进行排序
//        }
//
//        List<E> result = new ArrayList<>();
//        for (E node : menuList) {
//            // 添加到下级数据中
//            setSubChildren.accept(node, parentMenuMap.get(Optional.ofNullable(id.apply(node))));
//            // 如是根节点，加入结构
//            if (rootCheck.test(node)) {
//                result.add(node);
//            }
//        }
//        return result;
//    }
//
//    public static <T, E> Map<Optional<T>, List<E>> collectAndSort(
//        List<E> menuList,
//        Function<E, T> pId,
//        Comparator<Optional<T>> keyComparator,
//        Comparator<List<E>> valueComparator) {
//
//        // Collect the map using groupingBy
//        Map<Optional<T>, List<E>> parentMenuMap = menuList.stream()
//            .collect(Collectors.groupingBy(
//                node -> Optional.ofNullable(pId.apply(node)),
//                LinkedHashMap::new,
//                Collectors.toList()
//            ));
//
//        // Sort the map by keys and then by values
//        return parentMenuMap.entrySet().stream()
//            .sorted(Map.Entry.comparingByKey(keyComparator)
//                .thenComparing(entry -> valueComparator.compare(entry.getValue(), entry.getValue())))
//            .collect(Collectors.toMap(
//                Map.Entry::getKey,
//                Map.Entry::getValue,
//                (e1, e2) -> e1,
//                LinkedHashMap::new // Keep insertion order
//            ));
//    }
//
//    public static void main(String[] args) {
//        // Demo usage
//        List<String> menuList = Arrays.asList("item1", "item2", "item3");
//        Function<String, Integer> pId = item -> item.length(); // just an example
//        Comparator<Optional<Integer>> keyComparator = Comparator.naturalOrder();
//        Comparator<List<String>> valueComparator = Comparator.comparingInt(List::size);
//
//        Map<Optional<Integer>, List<String>> sortedMap = collectAndSort(menuList, pId, keyComparator, valueComparator);
//
//        sortedMap.forEach((key, value) -> System.out.println(key + ": " + value));
//    }

    /**
     * 树中过滤
     * @param tree  需要过滤的树
     * @param predicate  过滤条件
     * @param getChildren 获取下级数据方法，如：MenuVo::getSubMenus
     * @return List<E> 过滤后的树
     * @param <E> 泛型实体对象
     */
    public static <E> List<E> filter(List<E> tree, Predicate<E> predicate, Function<E, List<E>> getChildren) {
        return tree.stream().filter(item -> {
            if (predicate.test(item)) {
                List<E> children = getChildren.apply(item);
                if (children != null && !children.isEmpty()) {
                    filter(children, predicate, getChildren);
                }
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }


    /**
     * 树中搜索
     * @param tree
     * @param predicate
     * @param getSubChildren
     * @return 返回搜索到的节点及其父级到根节点
     * @param <E>
     */
    public static <E> List<E> search(List<E> tree, Predicate<E> predicate, Function<E, List<E>> getSubChildren) {
        Iterator<E> iterator = tree.iterator();
        while (iterator.hasNext()) {
            E item = iterator.next();
            List<E> childList = getSubChildren.apply(item);
            if (childList != null && !childList.isEmpty()) {
                search(childList, predicate, getSubChildren);
            }
            if(!predicate.test(item) && ( childList == null || childList.isEmpty()) ){
                iterator.remove();
            }
        }
        return tree;
    }

}
