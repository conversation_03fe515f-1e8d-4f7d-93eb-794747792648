package cn.genn.trans.upm.infrastructure.converter;

import cn.genn.core.model.converter.POConverter;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmRoleConverter extends POConverter<UpmRole, UpmRolePO> {
}
