package cn.genn.trans.upm.infrastructure.utils;

import cn.hutool.core.util.StrUtil;

/**
 * 字符串截断
 *
 * <AUTHOR>
 * @date 2024/7/2
 */
public class StringUtil {

    /**
     * 长度大于20截断,末尾拼接...
     */
    public static String truncateString(String s) {
        if(StrUtil.isBlank(s)){
            return s;
        }
        if (s.length() > 20) {
            return s.substring(0, 17) + "...";
        } else {
            return s;
        }
    }
}
