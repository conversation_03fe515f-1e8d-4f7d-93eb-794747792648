package cn.genn.trans.upm.interfaces.mini;

import cn.genn.trans.upm.application.service.action.UpmUserActionService;
import cn.genn.trans.upm.application.service.query.UpmUserQueryService;
import cn.genn.trans.upm.interfaces.command.DriverCertificationCommand;
import cn.genn.trans.upm.interfaces.command.DriverRegisterCommand;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Api(tags = "小程序用户管理")
@RestController
@RequestMapping("/mini/user")
public class MiniUserController {

    @Resource
    private UpmUserQueryService queryService;
    @Resource
    private UpmUserActionService actionService;


    /**
     * 司机注册
     */
    @PostMapping("/driverRegister")
    @ApiOperation("司机upm注册")
    public UserWxInfoDTO driverRegister(@RequestBody @Validated DriverRegisterCommand command){
        return actionService.driverRegister(command);
    }

    /**
     * 司机认证
     */
    @PostMapping("/driverAuth")
    @ApiOperation("司机认证")
    public Boolean driver(@RequestBody @Validated DriverCertificationCommand command){
        return actionService.certificationDriver(command);
    }

}
