package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmTenantSystemMapper extends BaseMapper<UpmTenantSystemPO> {

    int saveBatch(List<UpmTenantSystemPO> list);

    default UpmTenantSystemPO selectByTenantIdAndSystemId(Long tenantId,Long systemId){
        LambdaQueryWrapper<UpmTenantSystemPO> wrapper = Wrappers.lambdaQuery(UpmTenantSystemPO.class)
            .eq(UpmTenantSystemPO::getTenantId, tenantId)
            .eq(UpmTenantSystemPO::getSystemId, systemId);
        return selectOne(wrapper);
    }
    default List<UpmTenantSystemPO> selectByTenantIdAndSystemIds(Long tenantId,List<Long> systemIds){
        LambdaQueryWrapper<UpmTenantSystemPO> wrapper = Wrappers.lambdaQuery(UpmTenantSystemPO.class)
            .eq(UpmTenantSystemPO::getTenantId, tenantId)
            .in(UpmTenantSystemPO::getSystemId, systemIds);
        return selectList(wrapper);
    }
}
