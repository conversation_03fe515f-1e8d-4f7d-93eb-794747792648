package cn.genn.trans.upm.infrastructure.config;

import cn.genn.cache.redis.component.RedisService;
import cn.genn.trans.upm.infrastructure.constant.CacheConstants;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.casbin.adapter.domain.CasbinRule;
import org.casbin.adapter.util.Util;
import org.casbin.jcasbin.model.Assertion;
import org.casbin.jcasbin.model.Model;
import org.casbin.jcasbin.persist.Adapter;
import org.casbin.jcasbin.persist.BatchAdapter;
import org.casbin.jcasbin.persist.Helper;
import org.springframework.data.redis.core.RedisTemplate;
import redis.clients.jedis.Jedis;

import java.util.*;

/**
 * Adapter represents the Redis adapter for policy storage.
 *
 * <AUTHOR>
 * @since 2024.04.25
 */
public class RedisOfCasbinAdapter implements Adapter, BatchAdapter {

    private Long systemId;
    private Long tenantId;

//    private Jedis jedis;

    private RedisService redisService;

    public void generateKeyData(Long systemId, Long tenantId) {
        this.systemId = systemId;
        this.tenantId = tenantId;
    }
    public String generatedKey() {
        return CacheConstants.CACHE_PRE + "casbin_rule_url_resource_v2_" + systemId + "_" + tenantId;
    }

    public RedisOfCasbinAdapter(RedisService redisService) {
        this.redisService = redisService;
    }

    /**
     * loadPolicy loads all policy rules from the storage.
     */
    @Override
    public void loadPolicy(Model model) {
        Long length = redisService.listSize(generatedKey());
        if (length == null) {
            return;
        }
        List<String> policies = redisService.listRange(generatedKey(), 0, length);
        for (String policy : policies) {
            CasbinRule rule = ((JSONObject) JSONObject.parse(policy)).toJavaObject(CasbinRule.class);
            loadPolicyLine(rule, model);
        }
    }

    /**
     * savePolicy saves all policy rules to the storage.
     */
    @Override
    public void savePolicy(Model model) {
        redisService.delete(generatedKey());
        extracted(model, "p");
        extracted(model, "g");
    }

    /**
     * addPolicy adds a policy rule to the storage.
     */
    @Override
    public void addPolicy(String sec, String ptype, List<String> rule) {
        if (CollectionUtils.isEmpty(rule)) {
            return;
        }
        CasbinRule line = savePolicyLine(ptype, rule);
        redisService.listRightPush(generatedKey(), JSONObject.toJSONString(line));
    }

    /**
     * removePolicy removes a policy rule from the storage.
     */
    @Override
    public void removePolicy(String sec, String ptype, List<String> rule) {
        if (CollectionUtils.isEmpty(rule)) {
            return;
        }
        CasbinRule line = savePolicyLine(ptype, rule);
        redisService.listRemove(generatedKey(), 1, JSONObject.toJSONString(line));
    }

    /**
     * removeFilteredPolicy removes policy rules that match the filter from the storage.
     */
    @Override
    public void removeFilteredPolicy(String sec, String ptype, int fieldIndex, String... fieldValues) {
        List<String> values = Optional.of(Arrays.asList(fieldValues)).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(values)) {
            return;
        }

        String regexRule = "";
        for (int i = 0; i < values.size(); ++i) {
            regexRule += "v" + fieldIndex + ":" + values.get(i) + (i + 1 == values.size() ? "" : ",");
            fieldIndex++;
        }

        List<String> rulesMatch = redisService.listRange(generatedKey(), 0, -1);
        redisService.listTrim(generatedKey(), 1, 0);

        String finalRegexRule = ".*" + regexRule + ".*";
        rulesMatch.forEach(rule -> {
            // "{}" is regex symbol in rule lead to regex throw exception, so remove the char

            String tempRule = rule.replaceAll("[\\{ | \\} | \"]", "");
            if (!tempRule.matches(finalRegexRule)) {
                redisService.listRightPush(generatedKey(), rule);
            }
        });
    }

    /**
     * AddPolicies adds policy rules to the storage.
     */
    @Override
    public void addPolicies(String sec, String ptype, List<List<String>> rules) {
        for (List<String> rule : rules) {
            addPolicy(sec, ptype, rule);
        }
    }

    /**
     * RemovePolicies removes policy rules from the storage.
     */
    @Override
    public void removePolicies(String sec, String ptype, List<List<String>> rules) {
        for (List<String> rule : rules) {
            removePolicy(sec, ptype, rule);
        }
    }

    private void extracted(Model model, String type) {
        for (Map.Entry<String, Assertion> entry : model.model.get(type).entrySet()) {
            String ptype = entry.getKey();
            Assertion ast = entry.getValue();

            for (List<String> rule : ast.policy) {
                CasbinRule line = savePolicyLine(ptype, rule);
                redisService.listRightPush(generatedKey(), JSONObject.toJSONString(line));
            }
        }
    }

    private void loadPolicyLine(CasbinRule line, Model model) {
        String lineText = line.getPtype();
        if (!"".equals(line.getV0()) && line.getV0() != null) {
            lineText += ", " + line.getV0();
        }
        if (!"".equals(line.getV1()) && line.getV1() != null) {
            lineText += ", " + line.getV1();
        }
        if (!"".equals(line.getV2()) && line.getV2() != null) {
            lineText += ", " + line.getV2();
        }
        if (!"".equals(line.getV3()) && line.getV3() != null) {
            lineText += ", " + line.getV3();
        }
        if (!"".equals(line.getV4()) && line.getV4() != null) {
            lineText += ", " + line.getV4();
        }
        if (!"".equals(line.getV5()) && line.getV5() != null) {
            lineText += ", " + line.getV5();
        }

        Helper.loadPolicyLine(lineText, model);
    }

    private CasbinRule savePolicyLine(String ptype, List<String> rule) {
        CasbinRule line = new CasbinRule();

        line.setPtype(ptype);
        if (rule.size() > 0) {
            line.setV0(rule.get(0));
        }
        if (rule.size() > 1) {
            line.setV1(rule.get(1));
        }
        if (rule.size() > 2) {
            line.setV2(rule.get(2));
        }
        if (rule.size() > 3) {
            line.setV3(rule.get(3));
        }
        if (rule.size() > 4) {
            line.setV4(rule.get(4));
        }
        if (rule.size() > 5) {
            line.setV5(rule.get(5));
        }

        return line;
    }
}
