package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.utils.SqlEscapeUtil;
import cn.genn.trans.upm.interfaces.dto.fsp.SuperUserDTO;
import cn.genn.trans.upm.interfaces.query.fsp.SuperUserPageQuery;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Service
public class FspAuthQueryService {

    @Resource
    private UpmUserMapper userMapper;

    /**
     * 查询超管用户
     * @param query
     * @return
     */
    public PageResultDTO<SuperUserDTO> page(SuperUserPageQuery query){
        // 特殊字符格式转换
        if (StrUtil.isNotBlank(query.getUsername())) {
            query.setUsername(SqlEscapeUtil.escape(query.getUsername()));
        }
        if (StrUtil.isNotBlank(query.getNick())) {
            query.setNick(SqlEscapeUtil.escape(query.getNick()));
        }
        if (StrUtil.isNotBlank(query.getTelephone())) {
            query.setTelephone(SqlEscapeUtil.escape(query.getTelephone()));
        }
        Page<SuperUserDTO> resultPage = userMapper.selectSuperPage(new Page<>(query.getPageNo(), query.getPageSize()), query);
        return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), resultPage.getTotal(), resultPage.getRecords());
    }
}
