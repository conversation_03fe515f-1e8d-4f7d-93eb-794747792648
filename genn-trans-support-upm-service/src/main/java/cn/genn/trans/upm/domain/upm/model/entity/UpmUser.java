package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.UserTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UpmUser implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 权限组key
     */
    private String authKey;

    /**
     * 所属角色
     */
    private List<UpmRole> roleList;

    /**
     * 关联角色Id
     */
    private List<Long> roleIdList;

    /**
     * 账号
     */
    private UpmAccount account;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 逻辑删除
     */
    private DeletedEnum deleted;

    /**
     * 用户类型(system,tenant)
     */
    private UserTypeEnum type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private Long updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;

    /**
     * 创建用户名
     */
    private String createUserName;

    private String companyName;

    private String saleName;

    private LocalDateTime effectiveTime;


    public static UpmUser fromPo(UpmUserPO po){
        return UpmUser.builder()
            .id(po.getId())
            .accountId(po.getAccountId())
            .tenantId(po.getTenantId())
            .systemId(po.getSystemId())
            .roleList(new ArrayList<>())
            .account(UpmAccount.builder().id(po.getAccountId()).build())
            .status(po.getStatus())
            .nick(po.getNick())
            .type(po.getType())
            .telephone(po.getTelephone())
            .deleted(po.getDeleted())
            .createTime(po.getCreateTime())
            .createUserId(po.getCreateUserId())
            .createUserName(po.getCreateUserName())
            .updateTime(po.getUpdateTime())
            .updateUserId(po.getUpdateUserId())
            .updateUserName(po.getUpdateUserName())
            .effectiveTime(po.getEffectiveTime())
            .build();
    }

    public static UpmUser fromPo(UpmUserPO po, List<UpmRole> roleList){
        return UpmUser.builder()
            .id(po.getId())
            .accountId(po.getAccountId())
            .tenantId(po.getTenantId())
            .systemId(po.getSystemId())
            .roleList(roleList)
            .account(UpmAccount.builder().id(po.getAccountId()).build())
            .status(po.getStatus())
            .nick(po.getNick())
            .type(po.getType())
            .telephone(po.getTelephone())
            .deleted(po.getDeleted())
            .createTime(po.getCreateTime())
            .createUserId(po.getCreateUserId())
            .createUserName(po.getCreateUserName())
            .updateTime(po.getUpdateTime())
            .updateUserId(po.getUpdateUserId())
            .updateUserName(po.getUpdateUserName())
            .effectiveTime(po.getEffectiveTime())
            .build();
    }

    public static UpmUser fromOfficialAccountUser(OfficialAccountUser officialAccountUser) {
        return UpmUser.builder()
            .accountId(officialAccountUser.getAccountId())
            .tenantId(officialAccountUser.getTenantId())
            .authKey(officialAccountUser.getAuthKey())
            .nick(officialAccountUser.getTelephone())
            .systemId(officialAccountUser.getSystemId())
            .telephone(officialAccountUser.getTelephone())
            .type(UserTypeEnum.TENANT)
            .build();
    }
}
