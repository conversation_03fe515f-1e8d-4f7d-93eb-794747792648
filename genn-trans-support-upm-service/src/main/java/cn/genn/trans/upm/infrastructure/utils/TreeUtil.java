package cn.genn.trans.upm.infrastructure.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;

/**
 * <AUTHOR>
 */
public class TreeUtil {

    /**
     * 资源tree转换
     * @param arr
     * @param id
     * @param pid
     * @param child
     * @return
     */
    public static JSONArray listToTree(JSONArray arr, String id, String pid, String child) {
        JSONArray r = new JSONArray();
        JSONObject hash = new JSONObject();

        for (int i = 0; i < arr.size(); i++) {
            JSONObject json = (JSONObject) arr.get(i);
            hash.put(json.getStr(id), json);
        }

        for (int j = 0; j < arr.size(); j++) {

            JSONObject aVal = (JSONObject) arr.get(j);

            Object pidObj = aVal.get(pid);
            if (pidObj == null) {
                r.add(aVal);
            } else {

                JSONObject hashVp = (JSONObject) hash.get(aVal.get(pid).toString());

                if (hashVp != null) {

                    if (hashVp.get(child) != null) {
                        JSONArray ch = (JSONArray) hashVp.get(child);
                        ch.add(aVal);
                        hashVp.put(child, ch);
                    } else {
                        JSONArray ch = new JSONArray();
                        ch.add(aVal);
                        hashVp.put(child, ch);
                    }
                } else {
                    r.add(aVal);
                }
            }
        }
        return r;
    }
}
