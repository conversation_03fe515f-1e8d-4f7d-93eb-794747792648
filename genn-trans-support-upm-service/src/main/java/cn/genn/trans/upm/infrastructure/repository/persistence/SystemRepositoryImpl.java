package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.infrastructure.converter.UpmSystemConverter;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmSystemPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.genn.trans.upm.interfaces.query.SystemQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.restart.RestartEndpoint;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class SystemRepositoryImpl extends ServiceImpl<UpmSystemMapper, UpmSystemPO> implements SystemRepository {

    @Resource
    private UpmSystemMapper upmSystemMapper;
    @Resource
    private ResourceRepository resourceRepository;
    @Resource
    private UpmSystemConverter upmSystemConverter;

    @Override
    public UpmSystem find(Long systemId) {
        UpmSystemPO upmSystemPO = this.getById(systemId);
        if (Objects.isNull(upmSystemPO)) {
            return null;
        }
        return UpmSystem.formPo(this.getById(systemId));
    }

    @Override
    public List<UpmSystem> find(List<Long> systemIdList) {
        QueryWrapper queryWrapper = new QueryWrapper<UpmSystemPO>()
            .in("id", systemIdList)
            .eq("deleted", DeletedEnum.NOT_DELETED.getCode());
        List<UpmSystemPO> poList = this.list(queryWrapper);
        return poList.stream().map(UpmSystem::formPo).collect(Collectors.toList());
    }

    @Override
    public UpmSystem find(String code) {
        QueryWrapper queryWrapper = new QueryWrapper<UpmSystemPO>()
            .eq("code", code)
            .eq("deleted", DeletedEnum.NOT_DELETED.getCode());
        List<UpmSystemPO> poList = this.list(queryWrapper);
        Optional<UpmSystemPO> optional = poList.stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        return UpmSystem.formPo(optional.get());
    }

    @Override
    public List<UpmSystem> findBySystemType(SystemTypeEnum systemTypeEnum) {
        List<UpmSystemPO> poList = this.list(new LambdaQueryWrapper<UpmSystemPO>()
            .eq(UpmSystemPO::getType, systemTypeEnum.getCode())
            .eq(UpmSystemPO::getDeleted, DeletedEnum.NOT_DELETED.getCode()));
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.EMPTY_LIST;
        }
        List<UpmResource> resourceList = resourceRepository.findListBySystemId(poList.stream().map(UpmSystemPO::getId).distinct().collect(Collectors.toList()));
        Map<Long, List<UpmResource>> resource = resourceList.stream().collect(Collectors.groupingBy(UpmResource::getSystemId));
        List<UpmSystem> systemList = poList.stream().map(system -> {
            UpmSystem upmSystem = UpmSystem.formPo(system);
            upmSystem.setSystemResourceList(resource.get(system.getId()));
            return upmSystem;
        }).collect(Collectors.toList());
        return systemList;
    }

    @Override
    public Map<Long, Set<Long>> findResourceBySystemType(SystemTypeEnum systemTypeEnum) {
        List<UpmSystemPO> poList = this.list(new LambdaQueryWrapper<UpmSystemPO>()
            .eq(UpmSystemPO::getType, systemTypeEnum.getCode())
            .eq(UpmSystemPO::getDeleted, DeletedEnum.NOT_DELETED.getCode()));
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.EMPTY_MAP;
        }
        List<UpmResource> resourceList = resourceRepository.findListBySystemId(poList.stream().map(UpmSystemPO::getId).distinct().collect(Collectors.toList()));
        Map<Long, Set<Long>> resource = resourceList.stream().collect(Collectors.groupingBy(UpmResource::getSystemId, Collectors.mapping(UpmResource::getId, Collectors.toSet())));
        return resource;
    }

    @Override
    public List<UpmSystemPO> selectList(SystemQuery query) {
        LambdaQueryWrapper<UpmSystemPO> wrapper = Wrappers.lambdaQuery(UpmSystemPO.class)
            .in(CollUtil.isNotEmpty(query.getIdList()), UpmSystemPO::getId, query.getIdList())
            .notIn(CollUtil.isNotEmpty(query.getNoIdList()), UpmSystemPO::getId, query.getNoIdList())
            .eq(ObjUtil.isNotNull(query.getSystemType()), UpmSystemPO::getType, query.getSystemType())
            .ne(ObjUtil.isNotNull(query.getNoSystemType()), UpmSystemPO::getType, query.getNoSystemType())
            .orderByAsc(UpmSystemPO::getId);

        return baseMapper.selectList(wrapper);
    }

    @Override
    public Long store(UpmSystem system) {
        UpmSystemPO upmSystemPO = upmSystemConverter.entity2PO(system);
        this.saveOrUpdate(upmSystemPO);

        // 更新系统资源
        if (!CollectionUtils.isEmpty(system.getResourceList())) {
            resourceRepository.updateBatchById(system.getResourceList());
        }

        return upmSystemPO.getId();
    }


    @Override
    public Boolean update(List<UpmSystem> systemList) {
        this.saveOrUpdateBatch(upmSystemConverter. entity2PO(systemList));
        List<UpmResource> upmResourceList = systemList.stream()
            .filter(system -> !CollectionUtils.isEmpty(system.getResourceList()))
            .map(UpmSystem::getResourceList)
            .flatMap(List::stream)
            .collect(Collectors.toList());

        // 更新系统资源
        if (!CollectionUtils.isEmpty(upmResourceList)) {
            resourceRepository.updateBatchById(upmResourceList);
        }

        return true;
    }

    @Override
    public Boolean delete(List<UpmSystem> systemList) {
        baseMapper.deleteBatchIds(systemList.stream().map(UpmSystem::getId).collect(Collectors.toList()));
        List<Long> resourceIdList = systemList.stream()
            .filter(system -> !CollectionUtils.isEmpty(system.getResourceList()))
            .map(UpmSystem::getResourceList)
            .flatMap(List::stream)
            .map(UpmResource::getId)
            .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(resourceIdList)) {
            resourceRepository.batchDelete(resourceIdList);
        }

        return true;
    }
}
