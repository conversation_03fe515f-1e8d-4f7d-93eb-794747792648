package cn.genn.trans.upm.application.processor;

import cn.genn.core.exception.CheckException;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmResourceMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.interfaces.command.UpmResourceOperateCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceSaveCommand;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
@Component
public class ResourceProcessor {

    @Resource
    private UpmResourceMapper upmResourceMapper;

    public void save(UpmResourceSaveCommand command) {
        //同一系统同一层级下的菜单名称不能相同
        UpmResourcePO po = upmResourceMapper.selectByTitle(command.getSystemId(), command.getTitle(),command.getPid());
        if (ObjUtil.isNotNull(po)) {
            throw new CheckException(MessageCode.RESOURCE_NAME_REPEAT);
        }
        po = upmResourceMapper.selectByPath(command.getSystemId(), command.getPath());
        if (ObjUtil.isNotNull(po)) {
            throw new CheckException(MessageCode.RESOURCE_PATH_REPEAT);
        }

    }

    public void change(UpmResourceOperateCommand command) {
        if (StrUtil.isNotBlank(command.getTitle())) {
            UpmResourcePO po = upmResourceMapper.selectByTitle(command.getSystemId(), command.getTitle(),command.getPid());
            if (ObjUtil.isNotNull(po) && !po.getId().equals(command.getId())) {
                throw new CheckException(MessageCode.RESOURCE_NAME_REPEAT);
            }
        }
        if (StrUtil.isNotBlank(command.getPath())) {
            UpmResourcePO po = upmResourceMapper.selectByPath(command.getSystemId(), command.getPath());
            if (ObjUtil.isNotNull(po) && !po.getId().equals(command.getId())) {
                throw new CheckException(MessageCode.RESOURCE_PATH_REPEAT);
            }
        }

    }
}
