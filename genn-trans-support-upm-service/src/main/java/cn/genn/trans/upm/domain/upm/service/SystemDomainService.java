package cn.genn.trans.upm.domain.upm.service;


import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.repository.TenantSystemRepository;
import cn.genn.trans.upm.domain.upm.specification.SystemUpdateSpecification;
import cn.genn.trans.upm.infrastructure.converter.UpmSystemConverter;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SystemDomainService {

    @Resource
    private SystemRepository systemRepository;
    @Resource
    private TenantSystemRepository tenantSystemRepository;
    @Resource
    private UpmSystemMapper upmSystemMapper;
    @Resource
    private UpmSystemConverter systemConverter;
    @Autowired
    private UpmTenantSystemMapper upmTenantSystemMapper;


    public UpmSystem find(String code) {
        return systemRepository.find(code);
    }

    public UpmSystem find(Long id) {
        return systemRepository.find(id);
    }

    public List<String> getAuthKeysBySystemType(SystemTypeEnum systemType, AuthGroupEnum authGroup, Long originId) {
        List<UpmSystem> systems = this.findBySystemType(systemType);
        if (CollectionUtil.isEmpty(systems)) {
            throw new BusinessException(MessageCode.SYSTEM_REOURCE_NOT_EXIST_CANT_CREATE_ROLE);
        }
        Long tenantId = CurrentUserHolder.getTenantId();
        List<String> authKeyList = systems.stream().map(system -> AuthKeyUtil.getAuthKey(authGroup, system.getId(), tenantId, originId)).collect(Collectors.toList());
        return authKeyList;
    }

    public List<UpmSystem> findBySystemType(SystemTypeEnum systemType) {
        return systemRepository.findBySystemType(systemType);
    }

    public Map<Long, Set<Long>> findResourceBySystemType(SystemTypeEnum systemType) {
        return systemRepository.findResourceBySystemType(systemType);
    }

    public Long create(UpmSystem upmSystem) {
        return systemRepository.store(upmSystem);
    }

    public Boolean update(UpmSystem upmSystem) {
        SystemUpdateSpecification updateSpecification = new SystemUpdateSpecification(systemRepository);
        updateSpecification.isSatisfiedBy(upmSystem);
        return systemRepository.update(Collections.singletonList(upmSystem));
    }

    public Boolean changeStatus(List<UpmSystem> upmSystemList) {
        return systemRepository.update(upmSystemList);
    }

    public Boolean remove(List<UpmSystem> upmSystemList) {
        return systemRepository.delete(upmSystemList);
    }

    /**
     * 系统关联租户
     */
    public List<UpmTenantSystemPO> tenantSystemRelation(List<Long> systemIdList, Long tenantId) {
        List<UpmTenantSystemPO> tenantSystemPOList = systemIdList.stream().map(systemId -> {
            UpmTenantSystemPO tenantSystemPO = new UpmTenantSystemPO();
            tenantSystemPO.setSystemId(systemId);
            tenantSystemPO.setTenantId(tenantId);
            return tenantSystemPO;
        }).collect(Collectors.toList());
        upmTenantSystemMapper.saveBatch(tenantSystemPOList);
        return tenantSystemPOList;
    }

    public List<UpmTenantSystemPO> createTenantSystemRelation(List<Long> systemIdList, Long tenantId) {
        // 参数非空检查
        if (CollectionUtil.isEmpty(systemIdList)) {
            return new ArrayList<>(0);
        }

        // 查询已存在的租户和系统关系
        List<UpmTenantSystemPO> existingRelations = Optional.ofNullable(
            upmTenantSystemMapper.selectByTenantIdAndSystemIds(tenantId, systemIdList)
        ).orElse(new ArrayList<>());

        // 提取已存在的 systemId
        List<Long> existingSystemIds = existingRelations.stream()
            .map(UpmTenantSystemPO::getSystemId)
            .collect(Collectors.toList());

        // 过滤出需要新增的 systemId（即未查询到的 systemId）
        List<Long> newSystemIds = systemIdList.stream()
            .filter(systemId -> !existingSystemIds.contains(systemId))
            .collect(Collectors.toList());

        // 如果没有需要新增的记录，直接返回已存在的关系
        if (CollectionUtil.isEmpty(newSystemIds)) {
            return existingRelations;
        }

        // 构建需要新增的 UpmTenantSystemPO 列表
        List<UpmTenantSystemPO> newRelations = newSystemIds.stream()
            .map(systemId -> {
                UpmTenantSystemPO tenantSystemPO = new UpmTenantSystemPO();
                tenantSystemPO.setSystemId(systemId);
                tenantSystemPO.setTenantId(tenantId);
                return tenantSystemPO;
            })
            .collect(Collectors.toList());

        // 执行批量保存操作
        upmTenantSystemMapper.saveBatch(newRelations);

        // 合并已存在的记录和新增的记录
        existingRelations.addAll(newRelations);
        return existingRelations;
    }


}
