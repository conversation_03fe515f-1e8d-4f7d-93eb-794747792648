package cn.genn.trans.upm.interfaces;

import cn.genn.trans.upm.application.service.action.UpmTenantActionService;
import cn.genn.trans.upm.application.service.query.UpmTenantQueryService;
import cn.genn.trans.upm.interfaces.command.TenantRegistrationChangeCommand;
import cn.genn.trans.upm.interfaces.command.TenantSaveAfterCommand;
import cn.genn.trans.upm.interfaces.command.UpmChangeStatusCommand;
import cn.genn.trans.upm.interfaces.command.UpmTenantSaveCommand;
import cn.genn.trans.upm.interfaces.dto.UpmTenantDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Api(tags = "租户管理")
@RestController
@RequestMapping("/upmTenant")
public class UpmTenantController {

    @Resource
    private UpmTenantQueryService queryService;
    @Resource
    private UpmTenantActionService actionService;

    @PostMapping("/listAll")
    @ApiOperation(value = "查询所有租户列表")
    public List<UpmTenantDTO> listAll(){
        return queryService.listAll();
    }

    @GetMapping("/list")
    @ApiOperation(value = "查询当前系统下所有租户集群信息")
    public List<UpmTenantDTO> listBySystemCode() {
        return queryService.list();
    }

    /**
     * 运营商添加租户
     */
    @PostMapping("/save")
    @ApiOperation(value = "运营商添加租户")
    public UpmTenantDTO save(@ApiParam(value = "操作类") @RequestBody @Validated UpmTenantSaveCommand command) {
        return actionService.save(command);
    }

    /**
     * 运营商添加租户后置操作
     */
    @PostMapping("/saveAfter")
    @ApiOperation(value = "运营商添加租户后置操作")
    public Boolean saveAfter(@ApiParam(value = "操作类") @RequestBody @Validated TenantSaveAfterCommand command) {
        return actionService.saveAfter(command);
    }

    // /**
    //  * 租户入驻：添加租户，关联系统，关联资源，添加超级管理员和角色
    //  *
    //  * @param command
    //  * @return Long
    //  */
    // @PostMapping("/registration")
    // @ApiOperation(value = "租户入驻：添加租户，关联系统，添加超级管理员和角色，关联资源")
    // public UpmTenantDTO registration(@ApiParam(value = "操作类") @RequestBody @Validated TenantRegistrationCommand command) {
    //     return actionService.registration(command);
    // }

    /**
     * 编辑租户信息：编辑租户，关联系统，关联资源
     */
    @PostMapping("/registration/change")
    @ApiOperation(value = "编辑租户信息：编辑租户，关联系统，关联资源")
    public boolean changeRegistration(@ApiParam(value = "操作类") @RequestBody @Validated TenantRegistrationChangeCommand command) {
        return actionService.changeRegistration(command);
    }

    @PostMapping("/change/status")
    @ApiOperation(value = "批量启用禁用")
    public boolean changeStatus(@RequestBody UpmChangeStatusCommand command){
        return actionService.changeStatus(command);
    }

    @PostMapping("delete")
    @ApiOperation(value = "删除租户")
    public boolean delete(@RequestParam("id") Long tenantId){
        return actionService.delete(tenantId);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public UpmTenantDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id) {
        return queryService.get(id);
    }

}

