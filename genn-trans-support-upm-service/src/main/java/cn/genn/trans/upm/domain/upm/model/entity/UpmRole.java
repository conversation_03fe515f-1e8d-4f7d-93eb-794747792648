package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UpmRole implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 权限组
     */
    private String authKey;

    /**
     * 编码
     */
    private String code;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 关联用户
     */
    private List<UpmUser> userList;

    /**
     * 关联资源
     */
    private List<UpmResource> resourceList;

    /**
     * 关联资源id
     */
    private List<Long> resourceIdList;

    /**
     * 类型(system:系统角色，tenant:租户角色)
     */
    private RoleTypeEnum type;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private Long updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;

    public static UpmRole fromPo(UpmRolePO po) {
        return UpmRole.builder()
            .id(po.getId())
            .name(po.getName())
            .systemId(po.getSystemId())
            .tenantId(po.getTenantId())
            .userList(new ArrayList<>())
            .resourceList(new ArrayList<>())
            .type(po.getType())
            .status(po.getStatus())
            .remark(po.getRemark())
            .deleted(po.getDeleted())
            .createTime(po.getCreateTime())
            .createUserId(po.getCreateUserId())
            .createUserName(po.getCreateUserName())
            .updateTime(po.getUpdateTime())
            .updateUserId(po.getUpdateUserId())
            .updateUserName(po.getUpdateUserName())
            .build();
    }
}
