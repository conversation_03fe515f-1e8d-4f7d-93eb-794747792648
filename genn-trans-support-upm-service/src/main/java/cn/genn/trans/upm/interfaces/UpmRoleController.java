package cn.genn.trans.upm.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.service.action.UpmRoleActionService;
import cn.genn.trans.upm.application.service.query.UpmRoleQueryService;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleDTO;
import cn.genn.trans.upm.interfaces.query.UpmRolePageQuery;
import cn.genn.trans.upm.interfaces.query.UpmRoleQuery;
import cn.genn.trans.upm.interfaces.query.UpmRoleResourceBySystemTypeQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/upmRole")
public class UpmRoleController {

    @Resource
    private UpmRoleQueryService queryService;
    @Resource
    private UpmRoleActionService actionService;

    /**
     * 分页查询角色
     *
     * @param query
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询角色")
    public PageResultDTO<UpmRoleDTO> page(@ApiParam(value = "查询类") @RequestBody @Validated UpmRolePageQuery query) {
        return queryService.page(query);
    }

    /**
     * 条件查询角色列表
     * @param query
     * @return
     */
    @PostMapping("/conditionList")
    @ApiOperation(value = "条件查询角色列表")
    public List<UpmRoleDTO> conditionList(@ApiParam(value = "条件查询参数") @RequestBody @Validated UpmRoleQuery query) {
        return queryService.conditionList(query);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public UpmRoleDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id) {
        return queryService.get(id);
    }

    /**
     * 添加角色
     *
     * @param command
     * @return Long
     */
    @PostMapping("/save")
    @ApiOperation(value = "添加")
    public Boolean save(@ApiParam(value = "角色") @RequestBody @Validated UpmRoleSaveCommand command) {
        return actionService.save(command);
    }


    @PostMapping("/saveReturnId")
    @ApiOperation(value = "添加")
    public Long saveReturnId(@ApiParam(value = "角色") @RequestBody @Validated UpmRoleSaveCommand command) {
        return actionService.saveReturnId(command);
    }

    @PostMapping("/saveBySystemType")
    @ApiOperation(value = "依照系统类型新增角色")
    public List<UpmRoleDTO> saveBySystemType(@ApiParam(value = "依照系统类型新增角色") @RequestBody @Validated UpmRoleSaveBySystemTypeCommand command) {
        return actionService.saveBySystemType(command);
    }

    @PostMapping("/updateBySystemType")
    @ApiOperation(value = "依照系统类型修改角色")
    public Boolean updateBySystemType(@ApiParam(value = "依照系统类型修改角色") @RequestBody @Validated UpmRoleUpdateBySystemTypeCommand command) {
        return actionService.updateBySystemType(command);
    }

    @PostMapping("/change/status/bySystemType")
    @ApiOperation(value = "依照系统类型批量启用禁用角色")
    public Boolean changeStatusBySystemType(@ApiParam(value = "依照系统类型批量启用禁用角色") @RequestBody @Validated UpmChangeStatusBySystemTypeCommand command) {
        return actionService.changeStatusBySystemType(command);
    }


    @PostMapping("/batch/delete/bySystemType")
    @ApiOperation(value = "依照系统类型批量删除角色")
    public Boolean batchDeleteBySystemType(@ApiParam(value = "依照系统类型批量删除角色") @RequestBody @Validated UpmRoleDelBySystemTypeCommand command) {
        return actionService.batchDeleteBySystemType(command);
    }

    @PostMapping("/query/resource/bySystemType")
    @ApiOperation(value = "依照系统类型查询主角色id资源")
    public List<UpmResourceDTO> queryResourceBySystemType(@ApiParam(value = "依照系统类型批量删除角色") @RequestBody @Validated UpmRoleResourceBySystemTypeQuery query) {
        return queryService.queryResourceBySystemType(query);
    }

    @PostMapping("/change/resource/bySystemType")
    @ApiOperation(value = "依照系统类型修改角色下菜单权限")
    public Boolean changeResourceBySystemType(@ApiParam(value = "角色id") @RequestBody @Validated UpmRoleResourceChangeBySystemTypeCommand command) {
        return actionService.changeResourceBySystemType(command);
    }


    /**
     * 修改角色
     *
     * @param command
     * @return Boolean
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Boolean change(@ApiParam(value = "角色") @RequestBody @Validated UpmRoleUpdateCommand command) {
        return actionService.change(command);
    }

    /**
     * 批量启用停用角色
     *
     * @param command
     * @return
     */
    @PostMapping("/change/status")
    @ApiOperation(value = "启用停用角色")
    public Boolean changeStatus(@ApiParam(value = "批量启用禁用角色") @RequestBody @Validated UpmChangeStatusCommand command) {
        return actionService.changeStatus(command);
    }

    /**
     * 批量删除角色
     */
    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除角色")
    public Boolean batchRemove(@ApiParam(value = "批量删除角色") @RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return actionService.batchRemove(idList);
    }

    @PostMapping("/batch/search")
    @ApiOperation(value = "依照角色id列表批量获取角色信息")
    public List<UpmRoleDTO> bachSearch(@ApiParam(value = "依照id列表批量获取角色信息") @RequestBody List<Long> idList) {
        return queryService.selectBatchIds(idList);
    }

    /**
     * 查询角色id资源
     *
     * @param id
     * @return
     */
    @PostMapping("/query/resource")
    @ApiOperation(value = "查询角色id资源")
    public List<UpmResourceDTO> queryResourceTreeById(@ApiParam(value = "角色id", name = "id", required = true) @RequestParam Long id) {
        return queryService.queryResourceTreeById(id);
    }

    /**
     * 修改菜单权限
     *
     * @param command
     * @return
     */
    @PostMapping("/update/resource")
    @ApiOperation(value = "修改角色下菜单权限")
    public Boolean changeResource(@ApiParam(value = "角色id") @RequestBody @Validated UpmRoleResourceCommand command) {
        return actionService.changeResource(command);
    }

    @PostMapping("/queryAuthKeyByUserId")
    public Map<Long, Set<String>> queryAuthKeyByUserId(@RequestBody List<Long> userIdList){
        return queryService.queryAuthKeyByUserId(userIdList);
    }

}

