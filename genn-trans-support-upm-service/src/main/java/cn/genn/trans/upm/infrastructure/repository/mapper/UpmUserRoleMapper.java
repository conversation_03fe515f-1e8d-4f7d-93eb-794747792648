package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmUserRolePO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
 * <AUTHOR>
 */
public interface UpmUserRoleMapper extends BaseMapper<UpmUserRolePO> {


    default UpmUserRolePO selectByUserIdAndRoleId(Long userId,Long roleId){
        LambdaQueryWrapper<UpmUserRolePO> wrapper = Wrappers.lambdaQuery(UpmUserRolePO.class)
            .eq(UpmUserRolePO::getUserId, userId)
            .eq(UpmUserRolePO::getRoleId, roleId);
        return selectOne(wrapper);
    }
}
