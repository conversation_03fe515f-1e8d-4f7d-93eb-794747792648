package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.infrastructure.converter.ResourceConverter;
import cn.genn.trans.upm.infrastructure.converter.UpmResourceConverter;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmResourceMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthResourceRelPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRoleAuthResourceRelPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.query.UpmResourceQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class ResourceRepositoryImpl extends ServiceImpl<UpmResourceMapper, UpmResourcePO> implements ResourceRepository {

    @Resource
    private UpmRoleAuthResourceRelMapper upmRoleAuthResourceRelMapper;
    @Resource
    private UpmAuthResourceRelMapper upmAuthResourceRelMapper;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private ResourceConverter resourceConverter;

    @Override
    public UpmResource find(Long ResourceId) {
        UpmResourcePO upmResourcePO = this.getById(ResourceId);
        if (Objects.isNull(upmResourcePO)) {
            return null;
        }
        return UpmResource.fromPo(upmResourcePO);
    }

    @Override
    public List<UpmResource> find(List<Long> resourceIdList) {
        QueryWrapper queryWrapper = new QueryWrapper<UpmResourcePO>().in("id", resourceIdList).eq("deleted", DeletedEnum.NOT_DELETED.getCode());
        List<UpmResourcePO> poList = this.list(queryWrapper);
        return poList.stream().map(UpmResource::fromPo).collect(Collectors.toList());
    }

    @Override
    public List<UpmResource> findAllOfUrlNotNull() {
        QueryWrapper queryWrapper = new QueryWrapper<UpmResourcePO>().eq("deleted", DeletedEnum.NOT_DELETED.getCode()).isNotNull("url");
        List<UpmResourcePO> poList = this.list(queryWrapper);
        return poList.stream().map(UpmResource::fromPo).collect(Collectors.toList());
    }

    @Override
    public UpmResource find(String code) {
        QueryWrapper queryWrapper = new QueryWrapper<UpmResourcePO>().eq("code", code).eq("deleted", DeletedEnum.NOT_DELETED.getCode());
        List<UpmResourcePO> poList = this.list(queryWrapper);
        Optional<UpmResourcePO> optional = poList.stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        return UpmResource.fromPo(optional.get());
    }

    @Override
    public UpmResource find(Long systemId, String code) {
        QueryWrapper queryWrapper = new QueryWrapper<UpmResourcePO>().eq("code", code).eq("system_id", systemId).eq("deleted", DeletedEnum.NOT_DELETED.getCode());
        List<UpmResourcePO> poList = this.list(queryWrapper);
        Optional<UpmResourcePO> optional = poList.stream().findFirst();
        if (!optional.isPresent()) {
            return null;
        }
        return UpmResource.fromPo(optional.get());
    }

    @Override
    public List<UpmResource> findListBySystemId(List<Long> systemIdList) {
        QueryWrapper queryWrapper = new QueryWrapper<UpmResourcePO>().in("system_id", systemIdList).eq("deleted", DeletedEnum.NOT_DELETED.getCode());
        List<UpmResourcePO> poList = this.list(queryWrapper);
        return poList.stream().map(UpmResource::fromPo).collect(Collectors.toList());
    }

    @Override
    public Long store(UpmResource Resource) {
        UpmResourcePO upmResourcePO = UpmResourceConverter.toPo(Resource);
        this.saveOrUpdate(upmResourcePO);

        return upmResourcePO.getId();
    }

    @Override
    public boolean update(List<UpmResource> resourceList) {
        updateBatchById(UpmResourceConverter.toPo(resourceList));

        // todo 待定 修改状态 查询当前资源 关联 租户系统资源列表


        return true;
    }

    @Override
    public List<UpmResource> storeBatch(List<UpmResource> resources) {
        if (CollectionUtils.isEmpty(resources)) {
            return new ArrayList<>(0);
        }
        List<UpmResourcePO> upmResourcePOs = resourceConverter.entity2PO(resources);
        this.saveBatch(upmResourcePOs);
        List<UpmResource> resourceList = resourceConverter.PO2Entity(upmResourcePOs);
        return resourceList;
    }

    @Override
    public boolean deleteBySystemId(Long systemId) {
        this.baseMapper.delete(new LambdaQueryWrapper<UpmResourcePO>()
            .eq(UpmResourcePO::getSystemId, systemId)
            .eq(UpmResourcePO::getDeleted, DeletedEnum.NOT_DELETED)
        );
        return Boolean.TRUE;
    }

    @Override
    public boolean updateBatchById(List<UpmResource> resourceList) {
        return updateBatchById(UpmResourceConverter.toPo(resourceList));
    }

    @Override
    public boolean delete(List<UpmResource> resourceList) {
        baseMapper.deleteBatchIds(resourceList.stream().map(UpmResource::getId).collect(Collectors.toList()));

        // 查询当前资源 关联 权限组资源列表
        List<Long> resourceIdList = resourceList.stream().map(UpmResource::getId).collect(Collectors.toList());
        QueryWrapper<UpmAuthResourceRelPO> utsrpQW = new QueryWrapper<UpmAuthResourceRelPO>()
            .in("resource_id", resourceIdList);
        List<UpmAuthResourceRelPO> authResourceRelPOS = upmAuthResourceRelMapper.selectList(utsrpQW);
        if (!CollectionUtils.isEmpty(authResourceRelPOS)) {
            List<Long> tenantSystemResourceIdList = authResourceRelPOS.stream().map(UpmAuthResourceRelPO::getId).collect(Collectors.toList());
            upmAuthResourceRelMapper.deleteBatchIds(tenantSystemResourceIdList);
            QueryWrapper<UpmRoleAuthResourceRelPO> urrpQW = new QueryWrapper<>();
            urrpQW.in("auth_resource_id", tenantSystemResourceIdList);
            List<UpmRoleAuthResourceRelPO> upmRoleResourcePOList = upmRoleAuthResourceRelMapper.selectList(urrpQW);
            if (!CollectionUtils.isEmpty(upmRoleResourcePOList)) {
                // 删除租户角色资源
                List<Long> roleResourceIdList = upmRoleResourcePOList.stream().map(UpmRoleAuthResourceRelPO::getId).collect(Collectors.toList());
                upmRoleAuthResourceRelMapper.deleteBatchIds(roleResourceIdList);
            }
        }

        return true;
    }

    @Override
    public List<UpmResourcePO> selectByRoleId(Long roleId) {
        return baseMapper.selectByRoleId(roleId);
    }

    @Override
    public List<UpmResourcePO> selectByRoleIds(List<Long> roleIds) {
        return baseMapper.selectByRoleIds(roleIds, null);
    }

    /**
     * @param idList
     * @return
     */
    @Override
    public Boolean batchDelete(List<Long> idList) {
        baseMapper.deleteBatchIds(idList);
        return true;
    }

    /**
     * 查询角色资源
     * @return
     */
    @Override
    public List<UpmResourcePO> roleQueryList(UpmResourceQuery query) {
        //如果角色中存在超级管理员,直接返回租户范围下所有资源
        List<UpmRolePO> rolePOS = roleRepository.selectSystemRoleList(query.getRoleIdList());
        String authKey = CurrentUserHolder.getAuthKey();
        if(CollUtil.isNotEmpty(rolePOS)){
            baseMapper.selectByAuthKey(authKey,query.getType());
        }
        return baseMapper.selectByRoleIds(query.getRoleIdList(),query.getType());
    }

    /**
     * 查询系统下资源
     * @param systemId
     * @param type
     * @return
     */
    @Override
    public List<UpmResourcePO> queryBySystemId(Long systemId, ResourceTypeEnum type) {
        LambdaQueryWrapper<UpmResourcePO> wrapper = Wrappers.lambdaQuery(UpmResourcePO.class)
            .eq(UpmResourcePO::getSystemId, systemId)
            .eq(ObjUtil.isNotNull(type), UpmResourcePO::getType, type);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<UpmResource> selectAllByAuthKeyList(List<String> authKeyList) {
        List<Long> resourceIds = upmAuthResourceRelMapper.getRoleIdsByAuthResourceIds(authKeyList);
        LambdaQueryWrapper<UpmResourcePO> resourceWrapper = Wrappers.lambdaQuery(UpmResourcePO.class)
            .in(UpmResourcePO::getId, resourceIds)
            .eq(UpmResourcePO::getStatus, StatusEnum.ENABLE)
            .eq(UpmResourcePO::getDeleted, DeletedEnum.NOT_DELETED)
            .orderByAsc(UpmResourcePO::getResourceSort)
            .orderByAsc(UpmResourcePO::getId);
        List<UpmResourcePO> upmResourcePOS = baseMapper.selectList(resourceWrapper);
        return resourceConverter.PO2Entity(upmResourcePOS);
    }
}
