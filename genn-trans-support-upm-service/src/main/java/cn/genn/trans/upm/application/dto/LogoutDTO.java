package cn.genn.trans.upm.application.dto;

import lombok.Data;

import java.util.List;

/**
 * 退出登录用户范围
 */
@Data
public class LogoutDTO {

    /**
     * 退出SystemType下所有用户
     */
    private String systemType;
    /**
     * 此参数会退出系统下所有用户
     */
    private List<Long> systemIds;
    /**
     * 退出AuthKey下用户
     */
    private String authKey;
    /**
     * 退出accountId下所有用户
     */
    private List<Long> accountIds;
    /**
     * 退出用户ids用户
     */
    private List<Long> userIds;


}
