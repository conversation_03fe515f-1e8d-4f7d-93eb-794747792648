package cn.genn.trans.upm.infrastructure.utils;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * 图形验证码
 *
 * <AUTHOR>
 * @date 2024/7/13
 */
@Slf4j
public class QryUtil<T> {

    public static <T> T operateBeforeQuery(T query) {
        Arrays.stream(query.getClass().getDeclaredFields()).forEach(field -> {
            field.setAccessible(true);
            try {
                if (field.getType().equals(String.class)) {
                    String value = (String) field.get(query);
                    if (StrUtil.isNotBlank(value)) {
                        value = StrUtil.trim(value);
                        String escapedValue = SqlEscapeUtil.escape(value); // 特殊字符格式转换
                        field.set(query, escapedValue);
                    }
                }
            } catch (IllegalAccessException e) {
                throw new BusinessException(MessageCode.REFLECTION_OPERATION_BEFORE_STRING_TYPE_QUERY_FAILED);
            }
        });
        return query;
    }
}
