package cn.genn.trans.upm.interfaces.mini;

import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.application.service.action.SsoAccountMiniActionService;
import cn.genn.trans.upm.application.service.action.SsoTokenActionService;
import cn.genn.trans.upm.application.service.action.UpmUserActionService;
import cn.genn.trans.upm.interfaces.base.web.exception.MessageCode;
import cn.genn.trans.upm.interfaces.command.mini.MiniSsoThirdDataSaveCommand;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.mini.MiniLoginUserDTO;
import cn.genn.trans.upm.interfaces.query.SsoUserTokenQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniSmsLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniWXLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoUserTokenMiniQuery;
import cn.hutool.core.util.StrUtil;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@Slf4j
@RequestMapping("/sso/mini")
@Api(tags = "SSO用户登录管理")
@RestController
public class MiniSsoController {

    @Resource
    private SsoAccountMiniActionService ssoService;
    @Resource
    private SsoTokenActionService ssoTokenActionService;
    @Resource
    private UpmUserActionService upmUserActionService;

    @PostMapping("/wxLogin")
    @ApiOperation("小程序微信授权登录")
    public MiniLoginUserDTO miniWxLogin(@Validated @RequestBody SsoAccountMiniWXLoginQuery query) {
        MiniLoginUserDTO miniLoginUserDTO;
        try{
            miniLoginUserDTO = ssoService.miniWxLogin(query);
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("mini wxLogin error,query:{}" ,new Gson().toJson(query),e);
            throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
        return miniLoginUserDTO;
    }

    @PostMapping("/smsLogin")
    @ApiOperation("小程序短信验证码登录")
    public MiniLoginUserDTO miniSmsLogin(@Validated @RequestBody SsoAccountMiniSmsLoginQuery query) {
        MiniLoginUserDTO miniLoginUserDTO;
        try{
            miniLoginUserDTO = ssoService.miniSmsLogin(query);
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("mini smsLogin error,query:{}" ,new Gson().toJson(query),e);
            throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
        return miniLoginUserDTO;

    }

    @PostMapping("/accountLogin")
    @ApiOperation("小程序账号登录")
    public MiniLoginUserDTO accountLogin(@Validated @RequestBody SsoAccountMiniLoginQuery query) {
        MiniLoginUserDTO miniLoginUserDTO;
        try{
            miniLoginUserDTO = ssoService.accountLogin(query);
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("mini accountLogin error,query:{}" ,new Gson().toJson(query),e);
            throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
        return miniLoginUserDTO;
    }

    /**
     * 根据token获取用户信息
     *
     * @return
     */
    @PostMapping("/userInfo")
    @ApiOperation("子系统根据token获取用户信息")
    public LoginUserAuthInfoDTO miniUserInfo(@Validated @RequestBody SsoUserTokenMiniQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            token = StpUtil.getTokenValue();
        }
        LoginUserAuthInfoDTO loginUserAuthInfo = ssoService.miniUserInfo(token);
        return loginUserAuthInfo;
    }

    /**
     * 按设备查询同一个账号下的所有会话 并注销
     *
     * @return
     */
    @PostMapping("/logout")
    @ApiOperation("登出")
    public Boolean logout(@Validated @RequestBody SsoUserTokenQuery query) {
        ssoTokenActionService.ssoAccountLogout(query.getToken(), query.getUserDeviceIdentify());
        return true;
    }

    /**
     * token续约
     *
     * @return
     */
    @PostMapping("/refreshToken")
    @ApiOperation("子系统token续约")
    public Boolean refreshToken(@Validated @RequestBody SsoUserTokenQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            token = StpUtil.getTokenValue();
        }
        ssoTokenActionService.refreshToken(token);
        return true;
    }

    /**
     * 注销账号
     */
    @PostMapping("/logoff")
    @ApiOperation(value = "注销账号")
    public Boolean logoff(@RequestParam("userId") Long userId) {
        // return upmUserActionService.batchRemove(Collections.singletonList(userId));
        //ToDo:空接口,因为即使注销后,也能自动注册登录,反而会造成其他数据问题
        return true;
    }

    @PostMapping("/saveThirdData")
    @ApiOperation(value = "保存微信openId")
    public void saveThirdData(@RequestBody MiniSsoThirdDataSaveCommand command) {
        upmUserActionService.saveThirdData(command);
    }

}

