package cn.genn.trans.upm.infrastructure.config;

import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

@Component
public class RedisKeyChangeListener implements MessageListener {
    @Override
    public void onMessage(Message message, byte[] pattern) {
        // 处理接收到的消息
        String messageBody = new String(message.getBody());
        System.out.println("Received message: " + messageBody);

        // 根据消息内容处理key的变更
        // ...
    }
}
