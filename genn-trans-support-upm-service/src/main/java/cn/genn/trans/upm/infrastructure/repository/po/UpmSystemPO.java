package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmSystemPO对象
 *
 * <AUTHOR>
 * @desc 系统表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_system", autoResultMap = true)
public class UpmSystemPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 系统默认样式
     */
    @TableField("pattern")
    private String pattern;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 系统类型(platform:平台方,operator:运营商,carrier:承运商)
     */
    @TableField("type")
    private SystemTypeEnum type;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

