package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.dto.UpmResourceTreeDTO;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.query.UpmResourceQuery;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmResourceAssembler extends QueryAssembler<UpmResourceQuery, UpmResourcePO, UpmResourceDTO> {

    default List<UpmResourceDTO> PO2DTO(List<UpmResourcePO> poList) {
        List<UpmResourceDTO> resultList = new ArrayList<>(poList.size());
        sortForPO(poList);
        for (UpmResourcePO po : poList) {
            if (po.getPid() == null || po.getPid().equals(0L)) {
                UpmResourceDTO dto = BeanUtil.copyProperties(po, UpmResourceDTO.class);
                resultList.add(this.buildResourceDTO(dto, poList));
            }
        }
        sortListForDTOList(resultList);
        return resultList;
    }

    default List<UpmResourceTreeDTO> PO2TREEDTO(List<UpmResourcePO> poList) {
        List<UpmResourceTreeDTO> resultList = new ArrayList<>();
        sortForPO(poList);
        for (UpmResourcePO po : poList) {
            if ((po.getPid() == null || po.getPid().equals(0L)) && ResourceTypeEnum.MENU.equals(po.getType())) {
                UpmResourceTreeDTO dto = this.tree2PO(po);
                resultList.add(this.buildResourceTree(dto, poList));
            }
        }
        sortListForTreeDTOList(resultList);
        return resultList;
    }

    // 递归方法
    default UpmResourceTreeDTO buildResourceTree(UpmResourceTreeDTO dto, List<UpmResourcePO> allList) {
        List<UpmResourceTreeDTO> children = new ArrayList<>();
        for (UpmResourcePO po : allList) {
            if (po.getPid().equals(dto.getId())) {
                switch (po.getType()) {
                    case MENU:
                        UpmResourceTreeDTO childrenDTO = this.tree2PO(po);
                        children.add(this.buildResourceTree(childrenDTO, allList));
                        break;
                    case BUTTON:
                        List<String> auths = dto.getMeta().getAuths();
                        if(CollUtil.isEmpty(auths)){
                            dto.getMeta().setAuths(new ArrayList<>());
                        }
                        dto.getMeta().getAuths().add(po.getAuths());
                        break;
                    //todo:链接和列数据跳过
                }
            }

        }
        dto.setChildren(children);
        return dto;
    }

    default UpmResourceDTO buildResourceDTO(UpmResourceDTO dto, List<UpmResourcePO> allList) {
        List<UpmResourceDTO> children = new ArrayList<>();
        for (UpmResourcePO po : allList) {
            if (po.getPid().equals(dto.getId())) {
                UpmResourceDTO childrenDTO = BeanUtil.copyProperties(po, UpmResourceDTO.class);
                children.add(this.buildResourceDTO(childrenDTO, allList));
            }
        }
        dto.setChildren(children);
        return dto;
    }

    default UpmResourceTreeDTO tree2PO(UpmResourcePO upmResourcePO) {
        UpmResourceTreeDTO treeDTO = new UpmResourceTreeDTO();
        BeanUtil.copyProperties(upmResourcePO, treeDTO);
        UpmResourceTreeDTO.MetaDTO meta = new UpmResourceTreeDTO.MetaDTO();
        BeanUtil.copyProperties(upmResourcePO, meta);
        treeDTO.setMeta(meta);
        return treeDTO;
    }

    static void sortListForTreeDTOList(List<UpmResourceTreeDTO> dtoList) {
        for (UpmResourceTreeDTO sysMenuDTO : dtoList) {
            sortForTreeDTO(sysMenuDTO);
        }
    }

    static void sortForTreeDTO(UpmResourceTreeDTO sysMenuDTO) {
        List<UpmResourceTreeDTO> children = sysMenuDTO.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        sortForTreeDTO(children);
        for (UpmResourceTreeDTO child : children) {
            sortForTreeDTO(child);
        }
    }

    static List<UpmResourceTreeDTO> sortForTreeDTO(List<UpmResourceTreeDTO> dtoList) {
        Collections.sort(dtoList, (o1, o2) -> {
            if ((o1.getMeta().getResourceSort() > o2.getMeta().getResourceSort())) {
                return 1;
            }
            if (o1.getMeta().getResourceSort().equals(o2.getMeta().getResourceSort())) {
                if (o1.getId() != null && o1.getId().compareTo(o2.getId()) == 1) {
                    return -1;
                } else {
                    return 1;
                }
            }
            return -1;
        });
        return dtoList;
    }

    static void sortListForDTOList(List<UpmResourceDTO> dtoList) {
        for (UpmResourceDTO sysMenuDTO : dtoList) {
            sortForDTO(sysMenuDTO);
        }
    }

    static void sortForDTO(UpmResourceDTO sysMenuDTO) {
        List<UpmResourceDTO> children = sysMenuDTO.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        sortForDTO(children);
        for (UpmResourceDTO child : children) {
            sortForDTO(child);
        }
    }

    // 升序排列ForDTO
    static List<UpmResourceDTO> sortForDTO(List<UpmResourceDTO> dtoList) {
        Collections.sort(dtoList, (o1, o2) -> {
            if ((o1.getResourceSort() > o2.getResourceSort())) {
                return 1;
            }
            if (o1.getResourceSort().equals(o2.getResourceSort())) {
                if (o1.getId() != null && o1.getId().compareTo(o2.getId()) == 1) {
                    return -1;
                } else {
                    return 1;
                }
            }
            return -1;
        });
        return dtoList;
    }

    // 升序排列ForPO
    static List<UpmResourcePO> sortForPO(List<UpmResourcePO> poList) {
        Collections.sort(poList, (o1, o2) -> {
            if ((o1.getResourceSort() > o2.getResourceSort())) {
                return 1;
            }
            if (o1.getResourceSort().equals(o2.getResourceSort())) {
                if (o1.getId() != null && o1.getId().compareTo(o2.getId()) == 1) {
                    return -1;
                } else {
                    return 1;
                }
            }
            return -1;
        });
        return poList;
    }


}

