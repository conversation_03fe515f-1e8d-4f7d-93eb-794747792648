package cn.genn.trans.upm.domain.upm.factory;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.interfaces.command.UpmChangeStatusCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceOperateCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceSaveCommand;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class ResourceFactory {

    @Resource
    private SystemRepository systemRepository;
    @Resource
    private ResourceRepository resourceRepository;

    public UpmResource createUpmResource(UpmResourceSaveCommand command) {

        UpmSystem upmSystem = systemRepository.find(command.getSystemId());
        if (Objects.isNull(upmSystem)) {
            throw new BusinessException(MessageCode.RESOURCE_BIND_SYSTEM_NOT_EXIST_ERROR);
        }
        UpmResource upmResource = resourceRepository.find(command.getSystemId(), command.getCode());
        if (Objects.nonNull(upmResource)) {
            throw new BusinessException(MessageCode.RESOURCE_CODE_EXIST_ERROR);
        }
        return UpmResource.createUpmResource(command, upmSystem);
    }

    public UpmResource updateUpmResource(UpmResourceOperateCommand command) {

        UpmResource upmResource = resourceRepository.find(command.getId());
        if (Objects.isNull(upmResource)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST_ERROR);
        }

        UpmSystem upmSystem = systemRepository.find(command.getSystemId());
        if (Objects.isNull(upmSystem)) {
            throw new BusinessException(MessageCode.SYSTEM_CODE_NOT_EXIST_ERROR);
        }

        return UpmResource.updateUpmResource(command, upmSystem);
    }

    public List<UpmResource> changeStatus(UpmChangeStatusCommand command) {
        List<Long> resourceIdList = command.getIdList();
        List<UpmResource> upmResources = resourceRepository.find(resourceIdList);

        if (CollectionUtils.isEmpty(upmResources)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST_ERROR);
        }
        Map<Long, UpmResource> resourceMap = upmResources.stream().collect(Collectors.toMap(UpmResource::getId, id -> id));

        resourceIdList.forEach(resourceId -> {
            if (!resourceMap.containsKey(resourceId)) {
                throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST_ERROR);
            }
        });

        List<UpmResource> resourceList = new ArrayList<>();
        upmResources.forEach(upmResource -> {
            if (StatusEnum.ENABLE.equals(command.getStatus())) {
                upmResource.enable();
            } else {
                upmResource.disable();
            }
            resourceList.add(upmResource);
        });
        return resourceList;
    }

    public List<UpmResource> deleteUpmResource(List<Long> resourceIdList) {
        List<UpmResource> upmResources = resourceRepository.find(resourceIdList);

        if (CollectionUtils.isEmpty(upmResources)) {
            throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST_ERROR);
        }
        Map<Long, UpmResource> resourceMap = upmResources.stream().collect(Collectors.toMap(UpmResource::getId, id -> id));

        resourceIdList.forEach(resourceId -> {
            if (!resourceMap.containsKey(resourceId)) {
                throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST_ERROR);
            }
        });

        List<UpmResource> resourceList = new ArrayList<>();
        upmResources.forEach(upmResource -> {
            upmResource.doDelete();
            resourceList.add(upmResource);
        });
        return resourceList;
    }

}
