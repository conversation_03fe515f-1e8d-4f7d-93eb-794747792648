package cn.genn.trans.upm.domain.upm.model.valobj;

import cn.genn.trans.upm.infrastructure.common.ValueObject;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import java.util.regex.Pattern;

/**
 * 系统编码
 *
 * <AUTHOR>
 **/
@Getter
public class SystemCode implements ValueObject<SystemCode> {

    private final String code;

    private static final Pattern VALID_PATTERN = Pattern.compile("^[A-Za-z0-9]+$");

    public SystemCode(final String code) {
        if (StringUtils.isEmpty(code)) {
            throw new IllegalArgumentException("系统编码不能为空");
        }
        Validate.isTrue(VALID_PATTERN.matcher(code).matches(),
            "编码格式不正确");
        this.code = code;
    }


    @Override
    public boolean sameValueAs(SystemCode other) {
        return other != null && this.code.equals(other.code);
    }

    @Override
    public String toString() {
        return code;
    }
}
