package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import cn.genn.trans.upm.interfaces.dto.UpmAccountDTO;
import cn.genn.trans.upm.interfaces.query.UpmAccountQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmAccountAssembler extends QueryAssembler<UpmAccountQuery, UpmAccountPO, UpmAccountDTO>{
}

