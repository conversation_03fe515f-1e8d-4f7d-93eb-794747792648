package cn.genn.trans.upm.interfaces;

import cn.genn.trans.upm.application.service.action.AuthGroupActionService;
import cn.genn.trans.upm.interfaces.command.AuthBuildChangeCommand;
import cn.genn.trans.upm.interfaces.command.AuthBuildCommand;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
@Api(tags = "权限组管理")
@RestController
@RequestMapping("/upmAuth")
public class UpmAuthGroupController {

    @Resource
    private AuthGroupActionService originActionService;

    /**
     * 新增承运商的后续操作
     */
    @PostMapping("/builderAuth")
    @ApiOperation("新增承运商的后续操作")
    public List<Long> builderAuth(@RequestBody @Validated AuthBuildCommand command){
        return originActionService.builderAuth(command);
    }

    /**
     * 编辑承运商的后续操作
     */
    @PostMapping("/changeAuth")
    @ApiOperation("编辑承运商的后续操作")
    public Boolean changeAuth(@RequestBody @Validated AuthBuildChangeCommand command){
        // CompletableFuture.runAsync(()->originActionService.changeAuth(command));
        originActionService.changeAuth(command);
        return true;
    }

}
