package cn.genn.trans.upm.interfaces.app;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.application.feign.MengSmsClient;
import cn.genn.trans.upm.application.service.action.SsoAccountAppActionService;
import cn.genn.trans.upm.interfaces.base.web.exception.MessageCode;
import cn.genn.trans.upm.interfaces.dto.mini.MiniLoginUserDTO;
import cn.genn.trans.upm.interfaces.query.app.MengSmsQuery;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppLoginQuery;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppSmsLoginQuery;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppUsernameQuery;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RequestMapping("/sso/app")
@Api(tags = "SSO用户登录管理")
@RestController
public class AppSsoController {

    @Resource
    private SsoAccountAppActionService ssoService;

    @PostMapping("/oneClickLogin")
    @ApiOperation("移动端本机一键登录")
    public MiniLoginUserDTO oneClickLogin(@Validated @RequestBody SsoAccountAppLoginQuery query) {
        MiniLoginUserDTO miniLoginUserDTO;
        try{
            miniLoginUserDTO = ssoService.oneClickLogin(query);
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("mini wxLogin error,query:{}" ,new Gson().toJson(query),e);
            throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
        return miniLoginUserDTO;
    }

    @PostMapping("/smsLogin")
    @ApiOperation("移动端短信验证码登录")
    public MiniLoginUserDTO smsLogin(@Validated @RequestBody SsoAccountAppSmsLoginQuery query) {
        MiniLoginUserDTO miniLoginUserDTO;
        try{
            miniLoginUserDTO = ssoService.smsLogin(query);
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("mini smsLogin error,query:{}" ,new Gson().toJson(query),e);
            throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
        return miniLoginUserDTO;

    }

    @PostMapping("/accountLogin")
    @ApiOperation("移动端账号密码登录")
    public MiniLoginUserDTO accountLogin(@Validated @RequestBody SsoAccountAppUsernameQuery query) {
        MiniLoginUserDTO miniLoginUserDTO;
        try{
            miniLoginUserDTO = ssoService.accountLogin(query);
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("mini accountLogin error,query:{}" ,new Gson().toJson(query),e);
            throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
        return miniLoginUserDTO;
    }

    //todo:测试结束后删除
    @Resource
    private MengSmsClient mengSmsClient;
    @PostMapping("/test")
    @ApiOperation("测试")
    public String test(@RequestBody MengSmsQuery query){
        String mengSmsDTO = mengSmsClient.queryMengSms(query);
        return mengSmsDTO;
        // String svcKey = upmSeverProperties.getMengSms().getSvcKey();
        // String decrypt = MengAESUtil.decrypt("ZYfoCNJv6ZGeuWN0WZuaRvaAztH390nUVEHh0qURM26cqgt7d/QxVT9MOQt6VQU14DdtdWb/uoURwBddXmPR3g==", svcKey);
        // return decrypt;
    }



}
