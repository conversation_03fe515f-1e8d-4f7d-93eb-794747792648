package cn.genn.trans.upm.application.service.action;

import cn.genn.trans.upm.interfaces.command.UpmUserThirdOperateCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmUserThirdActionService {


    /**
     * 新增
     *
     * @return Long
     */
    public Long save(UpmUserThirdOperateCommand command) {
        return 0L;
    }


    /**
     * 修改
     *
     * @return Boolean
     */
    public Boolean update(UpmUserThirdOperateCommand command) {
        return true;
    }

}

