package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.infrastructure.repository.po.UpmSystemPO;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * UpmSystem
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UpmSystem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Long id;

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;
    /**
     * 系统类型(platform,operator,carrier)
     */
    private SystemTypeEnum type;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 系统默认样式
     */
    private String pattern;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联租户
     */
    private List<UpmTenant> tenantList;

    /**
     * 关联租户id
     */
    private List<Long> tenantIdList;

    /**
     * 所属资源
     */
    private List<UpmResource> resourceList;

    /**
     * 逻辑删除
     */
    private DeletedEnum deleted;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 更新用户ID
     */
    private Long updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;

    /**
     * 系统资源实体列表
     */
    private List<UpmResource> systemResourceList;


    public UpmSystem(String code, String name, String pattern, String remark) {
        this.code = code;
        this.name = name;
        this.status = StatusEnum.ENABLE;
        this.pattern = pattern;
        this.remark = remark;
    }

    public UpmSystem(UpmSystem system, List<UpmResource> systemResourceList) {
        this.id = system.getId();
        this.code = system.getCode();
        this.name = system.getName();
        this.status = system.getStatus();
        this.pattern = system.getPattern();
        this.remark = system.getRemark();
        this.systemResourceList = systemResourceList;
        this.deleted = system.getDeleted();
    }


    public UpmSystem(Long id, String code, String name, String pattern, String remark) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.pattern = pattern;
        this.remark = remark;
    }

    /**
     * 启用
     */
    public void enable() {
        this.status = StatusEnum.ENABLE;
    }

    /**
     * 禁用
     */
    public void disable() {
        this.status = StatusEnum.DISABLE;
    }

    /**
     * 禁用
     */
    public void doDelete() {
        this.deleted = DeletedEnum.DELETED;
    }

    public static UpmSystem formPo(UpmSystemPO po) {
        return UpmSystem.builder()
            .id(po.getId())
            .name(po.getName())
            .code(po.getCode())
            .type(po.getType())
            .pattern(po.getPattern())
            .status(po.getStatus())
            .remark(po.getRemark())
            .build();
    }

}

