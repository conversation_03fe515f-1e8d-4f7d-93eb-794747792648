package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.DTOAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.interfaces.dto.UpmRoleDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmRoleReturnAssembler extends DTOAssembler<UpmRole, UpmRoleDTO> {
}

