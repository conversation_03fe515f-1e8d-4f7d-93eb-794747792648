package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Data
@Accessors(chain = true)
public class AuthType {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 权限组类型：pl平台,op运营,ca承运,cl客户
     */
    private AuthGroupEnum authType;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 资源树json
     */
    private List<Long> resourceJson;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
