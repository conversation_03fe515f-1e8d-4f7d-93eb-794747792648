package cn.genn.trans.upm.application.feign;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.application.dto.MengSmsDTO;
import cn.genn.trans.upm.application.feign.api.IMengSmsService;
import cn.genn.trans.upm.interfaces.query.app.MengSmsQuery;
import cn.genn.trans.upm.infrastructure.utils.MengAESUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
public class MengSmsClient {

    @Resource
    private IMengSmsService mengSmsService;
    @Resource
    private UpmSeverProperties upmSeverProperties;

    public String queryMengSms(MengSmsQuery query) {
        // log.info("token:{}", token);
        // MengSmsQuery query = arrangeQuery(token);
        log.info("query:{}", JsonUtils.toJson(query));
        MengSmsQuery data = mengSmsService.queryMsisdn(query);
        log.info("response:{}", JsonUtils.toJson(data));
        //解密
        String svcKey = upmSeverProperties.getMengSms().getSvcKey();
        String decrypt = MengAESUtil.decrypt(data.getPayload(), svcKey);
        MengSmsDTO result = JsonUtils.parse(decrypt, MengSmsDTO.class);
        if(Objects.nonNull(result) && StrUtil.isNotBlank(result.getMsisdn())){
            return result.getMsisdn();
        }
        log.error("mengSms result:{}",JsonUtils.toJson(result));
        throw new BusinessException("获取手机号失败,{}",result.getDescription());
    }

}
