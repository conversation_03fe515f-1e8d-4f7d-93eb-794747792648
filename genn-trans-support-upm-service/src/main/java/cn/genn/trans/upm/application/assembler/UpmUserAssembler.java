package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.interfaces.command.UpmUserSaveBySystemTypeCommand;
import cn.genn.trans.upm.interfaces.command.UpmUserSaveCommand;
import cn.genn.trans.upm.interfaces.command.UserRegisTerCommand;
import cn.genn.trans.upm.interfaces.command.feishu.UpmFSUserCommand;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.query.UpmUserQuery;
import cn.hutool.core.bean.BeanUtil;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmUserAssembler extends QueryAssembler<UpmUserQuery, UpmUserPO, UpmUserDTO>{

    default UpmUser UpmUserSaveCommand2UpmUser(UpmUserSaveCommand command){
        UpmUser upmUser = new UpmUser();
        BeanUtil.copyProperties(command,upmUser);
        UpmAccount upmAccount = new UpmAccount()
            .setUsername(command.getUsername())
            .setPassword(command.getPassword())
            .setTelephone(command.getTelephone());
        upmUser.setAccount(upmAccount);
        return upmUser;
    }
    default UpmUser fsUserCommand2UpmUser(UpmFSUserCommand command){
        UpmUser upmUser = new UpmUser();
        BeanUtil.copyProperties(command,upmUser);
        UpmAccount upmAccount = new UpmAccount()
            .setUsername(command.getUsername())
            .setPassword(command.getPassword())
            .setTelephone(command.getTelephone());
        upmUser.setAccount(upmAccount);
        return upmUser;
    }

    default UpmUser upmUserSaveBySystemType2UpmUser(UpmUserSaveBySystemTypeCommand command){
        UpmUser upmUser = new UpmUser();
        BeanUtil.copyProperties(command,upmUser);
        UpmAccount upmAccount = new UpmAccount()
            .setUsername(command.getUsername())
            .setPassword(command.getPassword())
            .setTelephone(command.getTelephone());
        upmUser.setAccount(upmAccount);
        return upmUser;
    }

    default UpmUser UserRegisTerCommand2UpmUser(UserRegisTerCommand command){
        UpmUser upmUser = new UpmUser();
        BeanUtil.copyProperties(command,upmUser);
        UpmAccount upmAccount = new UpmAccount()
            .setUsername(command.getUsername())
            .setPassword(command.getPassword())
            .setSystemId(command.getSystemId())
            .setTelephone(command.getTelephone());
        upmUser.setAccount(upmAccount);
        upmUser.setSystemId(command.getSystemId());
        upmUser.setNick(Optional.ofNullable(command.getNick()).orElse(command.getUsername()));
        return upmUser;
    }
}

