package cn.genn.trans.upm.application.service.query;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.hutool.core.util.ObjUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.authen.v1.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 飞书sdk查询封装
 */
@Slf4j
@Service
public class FeishuQueryService {

    @Resource
    private Client feishuClient;

    /**
     * 用户基础信息
     * @param loginCode
     * @return
     */
    public GetUserInfoRespBody userInfoByCode(String loginCode){
        CreateOidcAccessTokenRespBody accessToken = this.getAccessToken(loginCode);
        return this.getUserInfo(accessToken.getAccessToken());
    }

    /**
     * 飞书用户信息
     */
    public GetUserInfoRespBody getUserInfo(String accessToken){
        try {
            RequestOptions build = RequestOptions.newBuilder().appAccessToken(accessToken).build();
            GetUserInfoResp resp = feishuClient.authen().v1().userInfo().get(build);
            if(ObjUtil.isNull(resp) || !resp.success()){
                log.error("飞书小程序获取用户信息失败,resp:{}", JsonUtils.toJson(resp));
                throw new BusinessException(MessageCode.FEISHU_USER_INFO_ERROR);
            }
            return resp.getData();
        } catch (Exception e) {
            log.error("飞书小程序获取用户信息失败", e);
            throw new BusinessException(MessageCode.FEISHU_USER_INFO_ERROR);
        }
    }

    /**
     * 飞书登录token
     * @param loginCode
     * @return
     */
    public CreateOidcAccessTokenRespBody getAccessToken(String loginCode){
        CreateOidcAccessTokenReq req = CreateOidcAccessTokenReq.newBuilder()
            .createOidcAccessTokenReqBody(CreateOidcAccessTokenReqBody.newBuilder()
                .grantType("authorization_code")
                .code(loginCode)
                .build())
            .build();
        try {
            CreateOidcAccessTokenResp resp = feishuClient.authen().v1().oidcAccessToken().create(req);
            if(ObjUtil.isNull(resp) || !resp.success()){
                log.error("飞书小程序登录失败,resp:{}", JsonUtils.toJson(resp));
                throw new BusinessException(MessageCode.FEISHU_LOGIN_FAIL);
            }
            return resp.getData();
        } catch (Exception e) {
            log.error("飞书小程序登录失败", e);
            throw new BusinessException(MessageCode.FEISHU_LOGIN_FAIL);
        }

    }


}
