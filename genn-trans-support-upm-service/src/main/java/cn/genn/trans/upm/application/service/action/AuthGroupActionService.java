package cn.genn.trans.upm.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.domain.upm.repository.AuthGroupRepository;
import cn.genn.trans.upm.domain.upm.repository.UserRoleRepository;
import cn.genn.trans.upm.domain.upm.service.ResourceDomainService;
import cn.genn.trans.upm.domain.upm.service.RoleDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.repository.po.*;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.properties.SsoAuthProperties;
import cn.genn.trans.upm.interfaces.command.AuthBuildChangeCommand;
import cn.genn.trans.upm.interfaces.command.AuthBuildCommand;
import cn.genn.trans.upm.interfaces.command.SystemResourceCommand;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.UserTypeEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
@Slf4j
@Service
public class AuthGroupActionService {


    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private UserRoleRepository userRoleRepository;
    @Resource
    private AuthGroupRepository authGroupRepository;
    @Resource
    private ResourceDomainService resourceDomainService;
    @Resource
    private RoleDomainService roleDomainService;
    @Resource
    private UpmSeverProperties upmSeverProperties;
    @Resource
    private UpmTenantSystemMapper upmTenantSystemMapper;
    @Resource
    private UpmAuthResourceRelMapper authResourceRelMapper;
    @Resource
    private SsoAuthProperties ssoAuthProperties;

    /**
     * 新增承运商的后续操作
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Long> builderAuth(AuthBuildCommand command) {
        Long tenantId = CurrentUserHolder.getTenantId();
        Long carrierId = command.getCarrierId();
        Long superAccountId = command.getSuperAccountId();
        List<Long> systemIdList = command.getSystemResourceList().stream().map(SystemResourceCommand::getSystemId).distinct().collect(Collectors.toList());
        UpmUserPO upmUserPO = upmUserMapper.selectByAccountIdAndTenantId(superAccountId,ssoAuthProperties.getPlatformTenantId());
        if(ObjUtil.isNull(upmUserPO)){
            log.error("承运商的初始游客user不存在,accountId:{}",superAccountId);
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        // 承运商新增权限组
        List<UpmAuthGroupPO> upmAuthGroupPOS = this.arrangeAuthGroupCarrier(systemIdList, carrierId);
        authGroupRepository.saveBatch(upmAuthGroupPOS);
        // 承运层权限组资源关联
        List<UpmTenantSystemPO> systemPOList = upmTenantSystemMapper.selectByTenantIdAndSystemIds(tenantId,systemIdList);
        resourceDomainService.saveAuthResourceRel(systemPOList, systemIdList, command.getSystemResourceList(), tenantId, carrierId, AuthGroupEnum.CARRIER);
        // 承运层创建超级管理员角色
        List<UpmRolePO> upmRolePOList = roleDomainService.createSuperRole(tenantId, systemIdList, carrierId, AuthGroupEnum.CARRIER);
        // 承运层创建用户
        List<UpmUserPO> upmUserPOList = this.arrangeUserData(systemIdList, upmUserPO, tenantId,carrierId);
        upmUserMapper.saveBatch(upmUserPOList);
        // 关联角色
        List<UpmUserRolePO> userRolePOList = this.arrangeUserRoleData(upmUserPOList, upmRolePOList);
        userRoleRepository.saveBatch(userRolePOList);
        return upmUserPOList.stream().map(UpmUserPO::getId).collect(Collectors.toList());
    }


    /**
     * 编辑承运商的后续操作
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeAuth(AuthBuildChangeCommand command){
        if(CollUtil.isNotEmpty(command.getSystemResourceList())){
            Long tenantId = command.getTenantId();
            Long carrierId = command.getCarrierId();
            // 承运层权限组资源关联
            resourceDomainService.batchSystemReplaceAuthResource(command.getSystemResourceList(),tenantId,carrierId,AuthGroupEnum.CARRIER);
            //ToDo:角色替换,用户替换,现在承运层只有一个系统,不存在替换;
        }
    }

    /**
     * 整理权限组数据
     *
     * @param systemIdList
     * @param carrierId
     * @return
     */
    private List<UpmAuthGroupPO> arrangeAuthGroupCarrier(List<Long> systemIdList, Long carrierId) {
        Long tenantId = CurrentUserHolder.getTenantId();
        return systemIdList.stream().map(systemId -> new UpmAuthGroupPO()
            .setAuthKey(AuthKeyUtil.getCarrierKey(systemId, tenantId, carrierId))
            .setTenantId(tenantId)
            .setSystemId(systemId)
            .setType(AuthGroupEnum.CARRIER)).collect(Collectors.toList());
    }

    private List<UpmUserPO> arrangeUserData(List<Long> systemIdList, UpmUserPO upmUserPO, Long tenantId,Long carrierId) {
        List<UpmUserPO> upmUserPOList = new ArrayList<>();
        for (Long systemId : systemIdList) {
            UpmUserPO userPO = new UpmUserPO()
                .setAccountId(upmUserPO.getAccountId())
                .setTelephone(upmUserPO.getTelephone())
                .setTenantId(tenantId)
                .setSystemId(systemId)
                .setAuthKey(AuthKeyUtil.getCarrierKey(systemId,tenantId,carrierId))
                .setType(UserTypeEnum.SYSTEM)
                .setNick(upmSeverProperties.getDefaultSuperNick());
            upmUserPOList.add(userPO);
        }
        return upmUserPOList;
    }

    private List<UpmUserRolePO> arrangeUserRoleData(List<UpmUserPO> upmUserPOList, List<UpmRolePO> roleList) {
        Map<Long, Long> systemRoleIdMap = roleList.stream().collect(Collectors.toMap(UpmRolePO::getSystemId, UpmRolePO::getId));
        List<UpmUserRolePO> userRolePOList = new ArrayList<>();
        for (UpmUserPO upmUserPO : upmUserPOList) {
            UpmUserRolePO po = new UpmUserRolePO();
            po.setUserId(upmUserPO.getId());
            po.setRoleId(systemRoleIdMap.get(upmUserPO.getSystemId()));
            userRolePOList.add(po);
        }
        return userRolePOList;
    }
}
