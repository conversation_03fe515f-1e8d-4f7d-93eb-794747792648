package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthResourceRelPO;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface UpmAuthResourceRelMapper extends BaseMapper<UpmAuthResourceRelPO> {

    default void deleteByTenantId(Long tenantId){
        LambdaQueryWrapper<UpmAuthResourceRelPO> wrapper = Wrappers.lambdaQuery(UpmAuthResourceRelPO.class)
            .eq(UpmAuthResourceRelPO::getTenantId, tenantId);
        delete(wrapper);
    }

    default void deleteByAuthKeys(List<String> authKeyList){
        LambdaQueryWrapper<UpmAuthResourceRelPO> wrapper = Wrappers.lambdaQuery(UpmAuthResourceRelPO.class)
            .in(UpmAuthResourceRelPO::getAuthKey, authKeyList);
        delete(wrapper);
    }
    default List<UpmAuthResourceRelPO> selectByAuthKey(String authKey){
        LambdaQueryWrapper<UpmAuthResourceRelPO> wrapper = Wrappers.lambdaQuery(UpmAuthResourceRelPO.class)
            .eq(UpmAuthResourceRelPO::getAuthKey, authKey);
        return selectList(wrapper);
    }

    default List<UpmAuthResourceRelPO> selectByAuthGroupAndSystemId(AuthGroupEnum group, Long systemId){
        QueryWrapper<UpmAuthResourceRelPO> wrapper = new QueryWrapper<UpmAuthResourceRelPO>()
            .select("distinct auth_key, system_id, tenant_id")
            .likeRight("auth_key", group.getCode())
            .eq("system_id", systemId);
        return selectList(wrapper);
    }

    default void deleteByAuthResources(Long systemId, AuthGroupEnum authGroup,List<Long> resourceIds){
        LambdaQueryWrapper<UpmAuthResourceRelPO> wrapper = Wrappers.lambdaQuery(UpmAuthResourceRelPO.class)
            .eq(UpmAuthResourceRelPO::getSystemId, systemId)
            .likeRight(UpmAuthResourceRelPO::getAuthKey, authGroup.getCode())
            .in(UpmAuthResourceRelPO::getResourceId, resourceIds);
        delete(wrapper);
    }

    default List<Long> getRoleIdsByAuthResourceIds(List<String> authKeyList){
        LambdaQueryWrapper<UpmAuthResourceRelPO> authResourceWrapper = Wrappers.lambdaQuery(UpmAuthResourceRelPO.class)
            .select(UpmAuthResourceRelPO::getResourceId)
            .in(UpmAuthResourceRelPO::getAuthKey, authKeyList);
        return Optional.ofNullable(selectList(authResourceWrapper)).orElse(new ArrayList<>(0))
            .stream().map(UpmAuthResourceRelPO::getResourceId).collect(Collectors.toList());
    }

}
