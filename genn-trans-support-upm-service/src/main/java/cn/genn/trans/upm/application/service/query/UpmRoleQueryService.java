package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.assembler.UpmResourceAssembler;
import cn.genn.trans.upm.application.assembler.UpmRoleAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.domain.upm.service.RoleDomainService;
import cn.genn.trans.upm.domain.upm.service.SystemDomainService;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeRoleTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.infrastructure.utils.SqlEscapeUtil;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleDTO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleUserDTO;
import cn.genn.trans.upm.interfaces.query.UpmRolePageQuery;
import cn.genn.trans.upm.interfaces.query.UpmRoleQuery;
import cn.genn.trans.upm.interfaces.query.UpmRoleResourceBySystemTypeQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmRoleQueryService {

    @Resource
    private UpmRoleMapper mapper;
    @Resource
    private UpmRoleAssembler assembler;
    @Resource
    private UpmResourceAssembler resourceAssembler;
    @Resource
    private ResourceRepository resourceRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private UpmAuthTypeRoleTemplateMapper authTypeRoleTemplateMapper;
    @Resource
    private RoleDomainService roleDomainService;
    @Resource
    private SystemDomainService systemDomainService;

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return UpmRoleDTO分页对象
     */
    public PageResultDTO<UpmRoleDTO> page(UpmRolePageQuery query) {
        if (StrUtil.isNotBlank(query.getName())) {
            query.setName(SqlEscapeUtil.escape(query.getName()));
        }
        String authKey = CurrentUserHolder.getAuthKey();
        PageResultDTO<UpmRoleDTO> pageResult = assembler.toPageResult(mapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query, authKey));
        //模板角色禁止操作
        List<UpmAuthTypeRoleTemplatePO> poList =  authTypeRoleTemplateMapper.selectByAuthTypeAndSystemId(CurrentUserHolder.getAuthGroup(),CurrentUserHolder.getSystemId());
        List<String> codeList = poList.stream().map(UpmAuthTypeRoleTemplatePO::getRoleCode).distinct().collect(Collectors.toList());
        for (UpmRoleDTO upmRoleDTO : pageResult.getList()) {
            upmRoleDTO.setOperateSign(!codeList.contains(upmRoleDTO.getCode()));
        }
        return pageResult;
    }

    /**
     * 查询角色列表
     *
     * @param query
     * @return
     */
    public List<UpmRoleDTO> conditionList(UpmRoleQuery query) {
        if (StrUtil.isNotBlank(query.getName())) {
            query.setName(SqlEscapeUtil.escape(query.getName()));
        }
        String authKey = CurrentUserHolder.getAuthKey();
        List<UpmRoleDTO> dtoList = assembler.PO2DTO(mapper.selectByList(query, authKey));
        //模板角色禁止操作
        // List<UpmAuthTypeRoleTemplatePO> poList =  authTypeRoleTemplateMapper.selectByAuthTypeAndSystemId(CurrentUserHolder.getAuthGroup(),CurrentUserHolder.getSystemId());
        // List<String> codeList = poList.stream().map(UpmAuthTypeRoleTemplatePO::getRoleCode).distinct().collect(Collectors.toList());
        // for (UpmRoleDTO upmRoleDTO : dtoList) {
        //     if(codeList.contains(upmRoleDTO.getCode())){
        //         upmRoleDTO.setOperateSign(false);
        //     }
        // }
        return dtoList;
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return UpmRoleDTO
     */
    public UpmRoleDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }

    /**
     * 依照id 列表查询
     * @param roleIds
     * @return
     */
    public List<UpmRoleDTO> selectBatchIds(List<Long> roleIds) {
        return assembler.PO2DTO(roleRepository.selectBatchIds(roleIds));
    }

    /**
     * 查询角色id资源
     *
     * @param id
     * @return
     */
    public List<UpmResourceDTO> queryResourceTreeById(Long id) {
        List<UpmResourcePO> upmResourcePOS = resourceRepository.selectByRoleId(id);
        return resourceAssembler.PO2DTO(upmResourcePOS);
    }

    public List<UpmResourceDTO> queryResourceBySystemType(UpmRoleResourceBySystemTypeQuery query) {
        List<String> authKeyList = systemDomainService.getAuthKeysBySystemType(query.getSystemType(), query.getAuthGroup(), query.getOriginId());
        List<UpmRole> upmRoles = roleDomainService.selectByAuthKeyAndMainRoleId(authKeyList, query.getMainRoleId());
        if (CollectionUtil.isEmpty(upmRoles)) {
            return Collections.emptyList();
        }
        List<Long> roleIds = upmRoles.stream().map(UpmRole::getId).collect(Collectors.toList());
        List<UpmResourcePO> upmResourcePOS = resourceRepository.selectByRoleIds(roleIds);
        return resourceAssembler.PO2DTO(upmResourcePOS);
    }

    /**
     * 用户id查询启用角色
     */
    public List<UpmRoleUserDTO> queryByUserIds(List<Long> userIdList) {
        return roleRepository.queryByUserIds(userIdList);
    }

    /**
     * userIdList 返回Map<userId,Set<authKey>>
     *
     * @param userIdList
     * @return
     */
    public Map<Long, Set<String>> queryAuthKeyByUserId(List<Long> userIdList) {
        List<UpmUserPO> upmUserPOList = upmUserMapper.selectBatchIds(userIdList);
        if (CollUtil.isEmpty(upmUserPOList)) {
            return Collections.emptyMap();
        }
        return upmUserPOList.stream().collect(Collectors.groupingBy(UpmUserPO::getId, Collectors.mapping(UpmUserPO::getAuthKey, Collectors.toSet())));
    }

}

