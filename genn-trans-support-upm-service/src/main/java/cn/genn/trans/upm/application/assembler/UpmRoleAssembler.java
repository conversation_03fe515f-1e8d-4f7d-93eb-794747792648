package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.command.UpmRoleSaveBySystemTypeCommand;
import cn.genn.trans.upm.interfaces.command.UpmRoleSaveCommand;
import cn.genn.trans.upm.interfaces.command.UpmRoleUpdateBySystemTypeCommand;
import cn.genn.trans.upm.interfaces.command.UpmRoleUpdateCommand;
import cn.genn.trans.upm.interfaces.dto.UpmRoleDTO;
import cn.genn.trans.upm.interfaces.query.UpmRoleQuery;
import org.mapstruct.*;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmRoleAssembler extends QueryAssembler<UpmRoleQuery, UpmRolePO, UpmRoleDTO>{

    UpmRole upmRoleSaveCommand2UpmRole(UpmRoleSaveCommand command);

    UpmRole upmRoleUpdateCommand2UpmRole(UpmRoleUpdateCommand command);

    UpmRole upmRoleSaveBySystem2UpmRole(UpmRoleSaveBySystemTypeCommand command);

    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    void updateBySystemTypeCmd(UpmRoleUpdateBySystemTypeCommand command, @MappingTarget UpmRole role);
}

