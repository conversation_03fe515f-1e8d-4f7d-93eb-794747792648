package cn.genn.trans.upm.infrastructure.converter;

import cn.genn.core.model.converter.POConverter;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ResourceConverter extends POConverter<UpmResource, UpmResourcePO> {


}

