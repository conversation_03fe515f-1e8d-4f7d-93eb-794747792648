package cn.genn.trans.upm.application.feign.api;

import cn.genn.trans.upm.interfaces.query.app.MengSmsQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name="meng-sms", url="${genn.upm.meng-sms.url}", path="",contextId = "IMengSmsService")
public interface IMengSmsService {

    @PostMapping("/svcapi/v1/query_msisdn")
    MengSmsQuery queryMsisdn(@RequestBody MengSmsQuery query);
}
