package cn.genn.trans.upm.infrastructure.config.feishu;

import com.lark.oapi.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class FeishuConfiguration {

    private final FeishuProperties properties;

    @Autowired
    public FeishuConfiguration(FeishuProperties properties) {
        this.properties = properties;
    }

    @Bean
    public Client feishuClient(){
        return Client.newBuilder(properties.getAppid(), properties.getAppSecret())
            // .openBaseUrl(BaseUrlEnum.FeiShu) // 设置域名，默认为飞书
            // .helpDeskCredential("helpDeskId","helpDeskSecret") // 服务台应用才需要设置
            .requestTimeout(3, TimeUnit.SECONDS) // 设置httpclient 超时时间，默认永不超时
            // .disableTokenCache() // 禁用token管理，禁用后需要开发者自己传递token
            // .logReqAtDebug(true) // 在 debug 模式下会打印 http 请求和响应的 headers,body 等信息。
            .build();
    }
}
