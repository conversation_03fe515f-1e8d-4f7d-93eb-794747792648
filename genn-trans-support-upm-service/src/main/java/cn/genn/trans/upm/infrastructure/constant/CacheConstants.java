package cn.genn.trans.upm.infrastructure.constant;

/**
 * 缓存相关key定义
 *
 * <AUTHOR>
 */
public class CacheConstants {

    /**
     * 统一的缓存前缀
     */
    public static final String CACHE_PRE = "GENN:CORE:UPM:";
    public static final String LOCK_PRE = "GENN:LOCK:UPM";


    /**
     * 具体的缓存key
     */
    public static final String CACHE_LOGIN_FAIL = "GENN:CACHE:LOGIN_FAIL_";
    public static final String CACHE_LOGIN_SMS_CODE = "GENN:CACHE:LOGIN_SMS_CODE_";
    public static final String CACHE_LOGIN_SMS_COUNT = "GENN:CACHE:LOGIN_SMS_COUNT_";
    public static final String CACHE_SEND_SMS_LAST_ACTIVE_TIME = "GENN:CACHE:SEND_SMS_LAST_ACTIVE_TIME_";

    /**
     * 全量casbin资源规则的key更新时间
     */
    public static final String CASBIN_RULE_URL_RESOURCE_ALL_LOAD = CACHE_PRE + "casbin_rule_url_resource_all_load_v2";
    public static final String CHECK_URI_PERMISSION = CACHE_PRE + "checkUriPermission";


    /**
     * 分布式锁相关key
     */
    public static final String LOCK_LOGIN_FREEZE = "GENN:LOCK:UPM:LOGIN_FREEZE_";

}
