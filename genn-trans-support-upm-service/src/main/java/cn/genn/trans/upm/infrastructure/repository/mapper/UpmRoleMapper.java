package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.infrastructure.dto.RoleUserRelDTO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleUserDTO;
import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.query.UpmRolePageQuery;
import cn.genn.trans.upm.interfaces.query.UpmRoleQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmRoleMapper extends BaseMapper<UpmRolePO> {

    Page<UpmRolePO> selectByPage(Page<UpmRolePO> page, @Param("query") UpmRolePageQuery query, @Param("authKey")String authKey);

    List<UpmRolePO> selectByList(@Param("query") UpmRoleQuery query, @Param("authKey")String authKey);

    List<UpmRole> queryAllRoleAndResource();

    List<UpmRoleUserDTO> queryByUserIds(List<Long> userIdList);

    int saveBatch(List<UpmRolePO> list);

    List<UpmRolePO> selectAllByTenantId(Long tenantId);

    default List<UpmRolePO> queryByTenantIdAndSystemIds(Long tenantId, List<Long> systemIdList, RoleTypeEnum roleTypeEnum){
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getTenantId, tenantId)
            .in(UpmRolePO::getSystemId, systemIdList)
            .eq(UpmRolePO::getType, roleTypeEnum);
        return selectList(wrapper);
    }


    List<RoleUserRelDTO> getAllUserAndRoleRel();

    UpmRolePO queryByCodeAndAuthKey(@Param("authKey")String authKey,@Param("code") String code);

    default UpmRolePO queryByCode(String code){
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getCode, code);
        return selectOne(wrapper);
    }

    default List<UpmRolePO> queryByNamesAndAuthKey(List<String> nameList, String authKey){
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getAuthKey, authKey)
            .in(UpmRolePO::getName, nameList);
        return selectList(wrapper);
    }


    default List<UpmRolePO> queryByCodesAndAuthKey(List<String> codeList, String authKey){
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getAuthKey, authKey)
            .in(UpmRolePO::getCode, codeList);
        return selectList(wrapper);
    }

    default List<UpmRolePO> selectByCodeAndAuthKeyBefore(String roleCode,String authKeyBefore){
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .likeRight(UpmRolePO::getAuthKey, authKeyBefore)
            .eq(UpmRolePO::getCode, roleCode);
        return selectList(wrapper);
    }

    default List<UpmRolePO> selectByAuthKeyAndMainRoleName(String name, List<String> authKeyList){
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .in(UpmRolePO::getAuthKey, authKeyList)
            .eq(UpmRolePO::getName, name);
        return selectList(wrapper);
    }

    List<UpmRolePO> selectByAuthKeyAndMainRoleNames(@Param("authKeyList")List<String> authKeyList, @Param("roleNameList")List<String> roleNameList);


    default void updateBatch(List<Long> roleIdList,String code,String name,String remark){
        LambdaUpdateWrapper<UpmRolePO> wrapper = Wrappers.lambdaUpdate(UpmRolePO.class)
            .set(UpmRolePO::getCode, code)
            .set(UpmRolePO::getName, name)
            .set(UpmRolePO::getRemark, remark)
            .in(UpmRolePO::getId, roleIdList);
        update(wrapper);
    }

    default void deleteAuthKeyAndCode(List<String> authkeyList, String roleCode){
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .in(UpmRolePO::getAuthKey, authkeyList)
            .eq(UpmRolePO::getCode, roleCode);
        delete(wrapper);
    }


}
