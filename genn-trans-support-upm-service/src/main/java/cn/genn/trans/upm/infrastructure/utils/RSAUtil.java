package cn.genn.trans.upm.infrastructure.utils;/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/31.
 */


import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

import java.io.InputStream;

/**
 * 加密解密工具类
 *
 * <AUTHOR>
 * @create 2024-04-18
 **/

public class RSAUtil {

    /**
     * 私钥字符串
     */
    private static String PRIVATE_KEY = "";
    /**
     * 公钥字符串
     */
    private static String PUBLIC_KEY = "";


    public static final String KEY_ALGORITHM = "RSA";


    /**
     * 读取密钥字符串
     *
     * @throws Exception
     */

    public static void convert() throws Exception {
        // if(PRIVATE_KEY !=null && PUBLIC_KEY !=null){
        //     return;
        // }
        byte[] data1 = null;
        byte[] data2 = null;

        try {
            InputStream is1 = RSAUtil.class.getResourceAsStream("/enc_pri");
            int length = is1.available();
            data1 = new byte[length];
            is1.read(data1);
            InputStream is2 = RSAUtil.class.getResourceAsStream("/enc_pub");
            int length2 = is2.available();
            data2 = new byte[length2];
            is2.read(data2);
        } catch (Exception e) {
        }

        String dataStr1 = new String(data1);
        String dataStr2 = new String(data2);
        try {
            PRIVATE_KEY = dataStr1;
            PUBLIC_KEY = dataStr2;
        } catch (Exception e) {
        }

        if (PRIVATE_KEY == null) {
            throw new Exception("Fail to retrieve key");
        }
    }


    /**
     * 私钥解密
     *
     * @param content
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPrivateKey(byte[] content) throws Exception {
        convert();
        RSA rsa = new RSA(PRIVATE_KEY, PUBLIC_KEY);
        byte[] data = rsa.decrypt(content, KeyType.PrivateKey);
        return data;
    }

    /**
     * 公钥加密
     *
     * @param content
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPublicKey(byte[] content) throws Exception {
        convert();
        RSA rsa = new RSA(PRIVATE_KEY, PUBLIC_KEY);
        byte[] data = rsa.encrypt(content, KeyType.PublicKey);
        return data;
    }

    public static String decryptBase64(String content) throws Exception {
        convert();
        RSA rsa = new RSA(PRIVATE_KEY, PUBLIC_KEY);
        byte[] data = rsa.decrypt(Base64.decode(content), KeyType.PrivateKey);
        return new String(data);
    }

    public static String encryptBase64(String content) throws Exception {
        convert();
        RSA rsa = new RSA(PRIVATE_KEY, PUBLIC_KEY);
        byte[] data = rsa.encrypt(content, KeyType.PublicKey);
        return Base64.encode(data);
    }


    public static void main(String[] args) throws Exception {
        convert();
        RSA rsa = new RSA(PRIVATE_KEY, PUBLIC_KEY);
        byte[] data = rsa.encrypt("password", KeyType.PublicKey);
        String content = Base64.encode(data);
        System.out.println(content);
        byte[] data2 = rsa.decrypt(Base64.decode(content), KeyType.PrivateKey);
        System.out.println(new String(data2));

    }

}
