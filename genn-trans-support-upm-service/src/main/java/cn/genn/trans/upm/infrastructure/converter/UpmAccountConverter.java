package cn.genn.trans.upm.infrastructure.converter;

import cn.genn.core.model.converter.POConverter;
import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmAccountConverter extends POConverter<UpmAccount, UpmAccountPO> {
}
