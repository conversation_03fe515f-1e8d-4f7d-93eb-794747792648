package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.database.mybatisplus.typehandler.ListLongTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * UpmAuthTypeRoleTemplatePO对象
 *
 * <AUTHOR>
 * @desc 权限组角色模版表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_auth_type_role_template", autoResultMap = true)
public class UpmAuthTypeRoleTemplatePO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 权限类型模板id
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 角色code
     */
    @TableField("role_code")
    private String roleCode;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 角色关联资源
     */
    @TableField(value = "resource_json",typeHandler = ListLongTypeHandler.class)
    private List<Long> resourceJson = new ArrayList<>();

    /**
     * 描述
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 删除标记0未删除；1删除
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}

