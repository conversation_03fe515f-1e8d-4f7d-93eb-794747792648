package cn.genn.trans.upm.infrastructure.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

/**
 * <AUTHOR>
 */
public class UpmHmacDigestUtil {
    private static final String ALGORITHM_SHA256 = "HmacSHA256";


    public static byte[] hmacSha256(byte[] key, byte[] data) {
        return digestData(ALGORITHM_SHA256, new SecretKeySpec(key, ALGORITHM_SHA256), data);
    }

    public static String hmacSha256AsHex(String key, String data) {
        return UpmHexUtil.encodeHex(hmacSha256(key.getBytes(), data.getBytes()));
    }

    public static String hmacSha256AsBase64(String key, String data) {
        return UpmBase64Util.encodeToString(hmacSha256(key.getBytes(), data.getBytes()));
    }

    public static String hmacSha256AsBase64UrlSafe(String key, String data) {
        return UpmBase64Util.encodeToUrlSafeString(hmacSha256(key.getBytes(), data.getBytes()));
    }

    public static byte[] digestData(String algorithm, Key key, byte[] data) {
        try {
            Mac mac = Mac.getInstance(algorithm);
            mac.init(key);
            return mac.doFinal(data);
        } catch (Exception var4) {
            throw new RuntimeException("数据加解密出错！");
        }
    }
}
