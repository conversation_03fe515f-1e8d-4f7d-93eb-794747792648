package cn.genn.trans.upm.interfaces;

import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.application.service.action.SsoTokenActionService;
import cn.genn.trans.upm.application.service.action.UpmUserActionService;
import cn.genn.trans.upm.infrastructure.utils.SsoUserUtil;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.exception.MessageCode;
import cn.genn.trans.upm.interfaces.command.CheckVerificationCodeCommand;
import cn.genn.trans.upm.interfaces.command.PdChangeByPhoneCommand;
import cn.genn.trans.upm.interfaces.command.UpmSendSmsCommand;
import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.UserTokenDTO;
import cn.genn.trans.upm.interfaces.query.*;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Splitter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * SSO Server端 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping({"/sso"})
@Api(value = "SSO用户登录管理", tags = {"SSO"})
@Slf4j
@RefreshScope
public class SsoServerController {

    @Resource
    private SsoTokenActionService ssoService;
    @Value(value = "${superAdminUserName: }")
    private String superAdminUserName;
    @Resource
    private UpmUserActionService upmUserActionService;

    /**
     * 账号的登录 校验账号密码
     * 并且返回租户和可登陆的系统供用户选择
     * 账号登录需要为设备分配唯一码
     *
     * @return
     */
    @PostMapping("/account/login")
    @ApiOperation("账号登录")
    public LoginUserAccountDTO auth(@Validated @RequestBody SsoAccountLoginQuery query,@RequestHeader(value = "vpcGroup",required = false) String vpcGroup) {
        LoginUserAccountDTO loginUserAccount = ssoService.ssoAccountDoLogin(query,vpcGroup);
        return loginUserAccount;
    }

    @PostMapping("/account/smsLogin")
    @ApiOperation("账号登录-短信验证码登录")
    public LoginUserAccountDTO smsLogin(@Validated @RequestBody SsoAccountSmsLoginQuery query,@RequestHeader(value = "vpcGroup",required = false) String vpcGroup) {
        LoginUserAccountDTO loginUserAccount = ssoService.ssoAccountDoSmsLogin(query,vpcGroup);
        return loginUserAccount;
    }

    @PostMapping("/account/sendSms")
    @ApiOperation("发送短信验证码")
    public Boolean sendSms(@Validated @RequestBody UpmSendSmsCommand command) {
        return ssoService.sendSms(command.getTelephone(), command.getBusinessCode());
    }

    /**
     * 账号选择完具体的系统和租户获取子系统token
     *
     * @return
     */
    @PostMapping("/system/login")
    @ApiOperation("子系统用户登录token")
    public UserTokenDTO systemLogin(@Validated @RequestBody SsoUserLoginQuery query) {
        UserTokenDTO userToken = ssoService.ssoSystemDoLoginToken(query);
        return userToken;
    }

    /**
     * 根据token获取用户信息
     *
     * @return
     */
    @PostMapping("/system/userInfo")
    @ApiOperation("子系统根据token获取用户信息")
    public LoginUserAuthInfoDTO systemUserInfo(@Validated @RequestBody SsoUserTokenQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            token = StpUtil.getTokenValue();
        }
        LoginUserAuthInfoDTO loginUserAuthInfo = ssoService.ssoSystemUserInfo(token);
        return loginUserAuthInfo;
    }

    @PostMapping("/system/tokenUserAuthInfo")
    @ApiOperation("根据token获取token缓存数据")
    public SsoUserAuthInfoDTO tokenUserAuthInfo(@Validated @RequestBody SsoUserTokenQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            token = StpUtil.getTokenValue();
        }
        SsoUserAuthInfoDTO info = SsoUserUtil.getLoginUserAuthInfoFormToken(token);
        if (Objects.nonNull(info)) {
            info.setFrozenStatus(SsoUserUtil.isTokenFrozenStatus(token));
        } else {
            log.info("getLoginUserAuthInfoFormToken is null,token={}", token);
            return null;
        }
        // 判断超级管理员
        if (StrUtil.isNotEmpty(info.getUsername())) {
            log.info("super admin list is: {}", superAdminUserName);
            info.setSuperAdmin(isSuperAdmin(info.getUsername()));
        }
        return info;
    }

    @PostMapping("/system/updateTokenUserAuthInfo")
    @ApiOperation("根据token更新token缓存数据")
    public SsoUserAuthInfoDTO updateTokenUserAuthInfo(@Validated @RequestBody SsoUserAuthInfoDTO dto) {
        String token = dto.getToken();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(MessageCode.TOKEN_GET_ERROR);
        }
        SsoUserUtil.setLoginUserAuthInfo(token, dto);
        return dto;
    }

    private boolean isSuperAdmin(String username) {
        return Splitter.on(",").splitToList(superAdminUserName).contains(username);
    }

    /**
     * 按设备查询同一个账号下的所有会话 并注销
     *
     * @return
     */
    @PostMapping("/account/logout")
    @ApiOperation("登出")
    public Boolean logout(@Validated @RequestBody SsoUserTokenQuery query) {
        ssoService.ssoAccountLogout(query.getToken(), query.getUserDeviceIdentify());
        return true;
    }


    /**
     * token续约
     *
     * @return
     */
    @PostMapping("/system/refreshToken")
    @ApiOperation("子系统token续约")
    public Boolean refreshToken(@Validated @RequestBody SsoUserTokenQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            token = StpUtil.getTokenValue();
        }
        ssoService.refreshToken(token);
        return true;
    }

    /**
     * 校验用户是否拥有uri的操作权限
     *
     * @param query
     * @return
     */
    @PostMapping("/user/checkUriPermission")
    public Boolean checkUriPermission(@Validated @RequestBody UpmCheckUriPermissionQuery query) {
        return ssoService.checkUriPermission(query);
    }

    @PostMapping("/user/generateToken")
    @ApiOperation("根据用户id生成token")
    public String generateToken(@RequestParam(value = "userId") Long userId) {
        String userToken = ssoService.generateTokenByUserId(userId);
        return userToken;
    }

    /**
     * 密保手机修改密码-未登录使用
     */
    @PostMapping("/changePdByPhone")
    @ApiOperation(value = "密保手机修改密码")
    public Boolean changePdByPhone(@RequestBody @Validated PdChangeByPhoneCommand command){
        return upmUserActionService.changePdByPhone(command);
    }

    @PostMapping("/checkVerificationCode")
    @ApiOperation(value = "校验验证码")
    public void checkVerificationCode(CheckVerificationCodeCommand command){
        ssoService.checkVerificationCode(command);
    }
}
