package cn.genn.trans.upm.infrastructure.utils;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.infrastructure.constant.CacheConstants;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/4
 */
@Slf4j
@Component
public class LoginFailUtil {

    private static UpmSeverProperties upmSeverProperties;

    @Autowired
    public void setUpmSeverProperties(UpmSeverProperties upmSeverProperties) {
        LoginFailUtil.upmSeverProperties = upmSeverProperties;
    }

    /**
     * 判断当前账号是否冻结
     */
    public static boolean isFreeze(String source) {
        // 获取冻结标记
        String loginFreezeKey = CacheConstants.LOCK_LOGIN_FREEZE + source;
        return RedisUtils.hasKey(loginFreezeKey);
    }

    /**
     * 记录登录失败
     *
     * @return 登录失败次数
     */
    public static long recordLoginFail(String source) {
        long failNum = upmSeverProperties.getLogin().getFailNum();
        long failEffectiveTime = upmSeverProperties.getLogin().getFailEffectiveTime();
        long freezeTime = upmSeverProperties.getLogin().getFreezeTime();
        // 已冻结的直接返回
        String loginFreezeKey = CacheConstants.LOCK_LOGIN_FREEZE + source;
        if (RedisUtils.hasKey(loginFreezeKey)) {
            return failNum;
        }
        // 获取登录失败记录
        long newCurrentSeconds = DateUtil.currentSeconds();
        String loginFailKey = CacheConstants.CACHE_LOGIN_FAIL + source;
        Set<String> timestampSet = RedisUtils.sMembers(loginFailKey);
        // 记录数小于失败次数，增加失败记录
        if (failNum - timestampSet.size() > 1) {
            RedisUtils.sAdd(loginFailKey, String.valueOf(newCurrentSeconds), failEffectiveTime, TimeUnit.MINUTES);
            return timestampSet.size()+1;
        }
        // 记录数大于失败次数，过滤掉已超时的失败记录，再比一次
        long endTime = newCurrentSeconds - failEffectiveTime * 60;
        List<String> outTimeList = timestampSet.stream().filter(time -> Long.parseLong(time) < endTime).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(outTimeList)) {
            RedisUtils.sRemove(loginFailKey, outTimeList.toArray(new String[0]));
        }
        long failSize = timestampSet.size() - outTimeList.size();
        if (failNum - failSize > 1) {
            // 增加失败记录
            RedisUtils.sAdd(loginFailKey, String.valueOf(newCurrentSeconds), failEffectiveTime, TimeUnit.MINUTES);
            return failSize+1;
        } else {
            // 如果已经是最后一次,删除全部失败记录，添加冻结标记
            RedisUtils.set(loginFreezeKey, String.valueOf(DateUtil.currentSeconds()), freezeTime, TimeUnit.MINUTES);
            RedisUtils.delete(loginFailKey);
            log.warn("账号：{}已冻结", source);
            return failNum;
        }
    }

    public static void loginFailException(long failSize, String source) {
        long failNum = upmSeverProperties.getLogin().getFailNum();
        long freezeTime = upmSeverProperties.getLogin().getFreezeTime();
        if (failSize <= 1) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST.getCode(), "用户名或密码输入错误，请重新输入");
        } else if (failSize < failNum) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST.getCode(), "已输入错误" + failSize + "次，超过" + failNum + "次账户将被锁定" + freezeTime + "分钟!");
        } else {
            String loginFreezeKey = CacheConstants.LOCK_LOGIN_FREEZE + source;
            long seconds = Long.parseLong(RedisUtils.get(loginFreezeKey));
            long remainTime = freezeTime - (DateUtil.currentSeconds() - seconds) / 60;
            throw new BusinessException(MessageCode.USER_NOT_EXIST.getCode(), "账户已锁定，请" + remainTime + "分钟后重试");
        }
    }

    public static void smsLoginFailException(long failSize, String source) {
        long failNum = upmSeverProperties.getLogin().getFailNum();
        long freezeTime = upmSeverProperties.getLogin().getFreezeTime();
        if (failSize <= 1) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST.getCode(), "验证码输入错误，请重新输入！");
        } else if (failSize < failNum) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST.getCode(), "已输入错误" + failSize + "次，超过" + failNum + "次账户将被锁定" + freezeTime + "分钟!");
        } else {
            String loginFreezeKey = CacheConstants.LOCK_LOGIN_FREEZE + source;
            long seconds = Long.parseLong(RedisUtils.get(loginFreezeKey));
            long remainTime = freezeTime - (DateUtil.currentSeconds() - seconds) / 60;
            throw new BusinessException(MessageCode.USER_NOT_EXIST.getCode(), "账户已锁定，请" + remainTime + "分钟后重试");
        }
    }

    public static void loginFreezeException(String source){
        long freezeTime = upmSeverProperties.getLogin().getFreezeTime();
        String loginFreezeKey = CacheConstants.LOCK_LOGIN_FREEZE + source;
        long seconds = Long.parseLong(RedisUtils.get(loginFreezeKey));
        long remainTime = freezeTime - (DateUtil.currentSeconds() - seconds) / 60;
        throw new BusinessException(MessageCode.USER_NOT_EXIST.getCode(), "账户已锁定，请" + remainTime + "分钟后重试");
    }

    /**
     * 解冻账号
     */
    public static void unFreeze(String source) {
        // 冻结标记
        String loginFreezeKey = CacheConstants.LOCK_LOGIN_FREEZE + source;
        // 登录失败记录
        String loginFailKey = CacheConstants.CACHE_LOGIN_FAIL + source;
        RedisUtils.delete(loginFreezeKey);
        RedisUtils.delete(loginFailKey);
    }

    /**
     * 清空失败记录
     */
    public static void clearFailLog(String source){
        // 登录失败记录
        String loginFailKey = CacheConstants.CACHE_LOGIN_FAIL + source;
        RedisUtils.delete(loginFailKey);
    }
}
