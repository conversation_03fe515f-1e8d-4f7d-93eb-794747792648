package cn.genn.trans.upm.application.service.query;

import cn.genn.trans.upm.application.assembler.UpmUserThirdAssembler;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.interfaces.dto.UpmUserThirdDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmUserThirdQueryService {

    @Resource
    private UpmUserThirdMapper mapper;
    @Resource
    private UpmUserThirdAssembler assembler;


    /**
     * 根据id查询
     *
     * @param id
     * @return UpmUserThirdDTO
     */
    public UpmUserThirdDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }



}

