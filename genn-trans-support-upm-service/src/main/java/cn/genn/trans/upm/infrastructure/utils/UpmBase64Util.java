package cn.genn.trans.upm.infrastructure.utils;

import org.springframework.util.Base64Utils;

/**
 * <AUTHOR>
 */
public class UpmBase64Util extends Base64Utils {
    public static final byte BASE64_PAD_CHAR = 61;

    public static byte[] encodeUrlSafe(byte[] data) {
        int padLen = 0;
        byte[] bytes = Base64Utils.encodeUrlSafe(data);
        int len = bytes.length;
        if (bytes[len - 1] == BASE64_PAD_CHAR) {
            ++padLen;
        }

        if (bytes[len - 1 - 1] == BASE64_PAD_CHAR) {
            ++padLen;
        }

        if (padLen == 0) {
            return bytes;
        } else {
            int dstLen = len - padLen;
            byte[] dst = new byte[dstLen];
            System.arraycopy(bytes, 0, dst, 0, dstLen);
            return dst;
        }
    }

    public static String encodeToUrlSafeString(byte[] data) {
        return new String(encodeUrlSafe(data));
    }
}
