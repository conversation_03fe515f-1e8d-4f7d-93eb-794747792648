package cn.genn.trans.upm.interfaces;

import cn.genn.trans.upm.application.service.action.UpmOriginActionService;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户下业务子系统管理
 * <AUTHOR>
 */
@Api(tags = "租户下业务子系统管理")
@RestController
@RequestMapping("/upmOrigin")
public class UpmOriginController {

    @Resource
    private UpmOriginActionService actionService;


    /**
     * 业务子系统添加管理员（包含创建多系统、角色、账号、用户、资源关联）
     * @param command
     * @return
     */
    @PostMapping("/createOriginAdmin")
    @ApiOperation(value = "业务子系统添加管理员（包含创建多系统、角色、账号、用户、资源关联）")
    public List<UpmUserDTO> createOriginAdministrator(@ApiParam(value = "操作类") @RequestBody @Validated OriginCreateAdminCommand command) {
        return actionService.createOriginAdministrator(command);
    }

}

