package cn.genn.trans.upm.domain.upm.factory;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.interfaces.command.UpmChangeStatusCommand;
import cn.genn.trans.upm.interfaces.command.UpmSystemOperateCommand;
import cn.genn.trans.upm.interfaces.command.UpmSystemSaveCommand;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SystemFactory {

    @Resource
    private SystemRepository systemRepository;
    @Resource
    private ResourceRepository resourceRepository;

    public UpmSystem createUpmSystem(UpmSystemSaveCommand command) {
        UpmSystem upmSystem = systemRepository.find(command.getCode());
        if (Objects.nonNull(upmSystem)) {
            throw new BusinessException(MessageCode.SYSTEM_CODE_EXIST_ERROR);
        }
        return new UpmSystem(command.getCode(), command.getName(), command.getPattern(), command.getRemark());
    }

    public UpmSystem updateUpmSystem(UpmSystemOperateCommand command) {
        UpmSystem upmSystem = systemRepository.find(command.getId());
        if (Objects.isNull(upmSystem)) {
            throw new BusinessException(MessageCode.SYSTEM_CODE_NOT_EXIST_ERROR);
        }

        return new UpmSystem(command.getId(), command.getCode(), command.getName(), command.getPattern(), command.getRemark());
    }

    public List<UpmSystem> changeUpmSystemStatus(UpmChangeStatusCommand command) {
        List<Long> systemIdList = command.getIdList();
        List<UpmSystem> upmSystemList = systemRepository.find(command.getIdList());
        if (CollectionUtils.isEmpty(upmSystemList)) {
            throw new BusinessException(MessageCode.SYSTEM_CODE_NOT_EXIST_ERROR);
        }

        Map<Long, UpmSystem> systemIdMap = upmSystemList.stream().collect(Collectors.toMap(UpmSystem::getId, id -> id));

        systemIdList.forEach(systemId -> {
            if (!systemIdMap.containsKey(systemId)) {
                throw new BusinessException(MessageCode.SYSTEM_CODE_NOT_EXIST_ERROR);
            }
        });

        List<UpmResource> upmResourceList = resourceRepository.findListBySystemId(systemIdList);
        Map<Long, List<UpmResource>> systemUpmResourceMap = upmResourceList.stream()
            .collect(Collectors.groupingBy(resource -> resource.getUpmSystem().getId()));

        List<UpmSystem> systemList = new ArrayList<>();
        upmSystemList.forEach(upmSystem -> {
            List<UpmResource> upmResources = new ArrayList<>();
            if (StatusEnum.ENABLE.equals(command.getStatus())) {
                // todo 待定 资源同步变更
//                if (systemUpmResourceMap.containsKey(upmSystem.getId())) {
//                    upmResources = systemUpmResourceMap.get(upmSystem.getId());
//                    upmResources.forEach(UpmResource::enable);
//                }
                upmSystem.enable();
            } else {
//                if (systemUpmResourceMap.containsKey(upmSystem.getId())) {
//                    upmResources = systemUpmResourceMap.get(upmSystem.getId());
//                    upmResources.forEach(UpmResource::disable);
//                }
                upmSystem.disable();
            }

            systemList.add(new UpmSystem(upmSystem, upmResources));
        });

        return systemList;
    }

    public List<UpmSystem> deleteUpmSystem(List<Long> systemIdList) {
        List<UpmSystem> upmSystemList = systemRepository.find(systemIdList);
        if (CollectionUtils.isEmpty(upmSystemList)) {
            throw new BusinessException(MessageCode.SYSTEM_CODE_NOT_EXIST_ERROR);
        }
        Map<Long, UpmSystem> systemIdMap = upmSystemList.stream().collect(Collectors.toMap(UpmSystem::getId, id -> id));

        systemIdList.forEach(systemId -> {
            if (!systemIdMap.containsKey(systemId)) {
                throw new BusinessException(MessageCode.SYSTEM_CODE_NOT_EXIST_ERROR);
            }
        });
        List<UpmResource> upmResourceList = resourceRepository.findListBySystemId(systemIdList);
        Map<Long, List<UpmResource>> systemUpmResourceMap = upmResourceList.stream()
            .collect(Collectors.groupingBy(resource -> resource.getUpmSystem().getId()));

        List<UpmSystem> systemList = new ArrayList<>();
        upmSystemList.forEach(upmSystem -> {
            List<UpmResource> upmResources = new ArrayList<>();
            if (systemUpmResourceMap.containsKey(upmSystem.getId())) {
                upmResources = systemUpmResourceMap.get(upmSystem.getId());
                upmResources.forEach(UpmResource::doDelete);
            }
            upmSystem.doDelete();
            systemList.add(new UpmSystem(upmSystem, upmResources));
        });

        return systemList;
    }

}
