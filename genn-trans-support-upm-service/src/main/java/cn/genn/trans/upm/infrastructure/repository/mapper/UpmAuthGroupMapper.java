package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthGroupPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmAuthGroupMapper extends BaseMapper<UpmAuthGroupPO> {


    default List<UpmAuthGroupPO> selectByAuthKey(List<String> authKey){
        LambdaQueryWrapper<UpmAuthGroupPO> wrapper = Wrappers.lambdaQuery(UpmAuthGroupPO.class)
            .in(UpmAuthGroupPO::getAuthKey, authKey);
        return selectList(wrapper);
    }
}
