package cn.genn.trans.upm.infrastructure.config;

import cn.genn.trans.upm.interfaces.base.web.filter.SsoAuthFilter;
import cn.genn.trans.upm.interfaces.base.web.filter.SsoLoginFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

@Configuration
public class WebConfig extends WebMvcConfigurerAdapter {

    @Autowired
    @Lazy
    private SsoLoginFilter ssoLoginFilter;
    @Autowired
    @Lazy
    private SsoAuthFilter ssoAuthFilter;

    /*@Bean
    @ConfigurationProperties(prefix = "sso.ignore-login", ignoreInvalidFields = true)
    public SsoIgnoreLoginProperties ignoreLoginProperties() {
        return new SsoIgnoreLoginProperties();
    }*/
    /**
     * 拦截白名单
     */
    private static final String[] IGNORE_PATHS = {"/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**","/doc.html","/favicon.ico","/error"};

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(ssoLoginFilter).excludePathPatterns(IGNORE_PATHS);
        registry.addInterceptor(ssoAuthFilter).excludePathPatterns(IGNORE_PATHS);
    }
}
