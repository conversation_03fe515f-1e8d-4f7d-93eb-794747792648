package cn.genn.trans.upm.interfaces.official;

import cn.genn.trans.upm.application.service.action.SsoAccountOfficialActionService;
import cn.genn.trans.upm.interfaces.command.official.SsoAccountOfficialWXTempLoginCommand;
import cn.genn.trans.upm.interfaces.dto.official.UserOfficialInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@Slf4j
@RequestMapping("/sso/official")
@Api(tags = "SSO用户登录管理")
@RestController
public class OfficialSsoController {

    @Resource
    private SsoAccountOfficialActionService ssoService;

    @PostMapping("/login")
    @ApiOperation("微信公众号授权登录")
    public UserOfficialInfoDTO officialWxLogin(@Validated @RequestBody SsoAccountOfficialWXTempLoginCommand command) {
        return ssoService.officialWxLogin(command);
    }


}

