package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleResourceDTO;
import cn.genn.trans.upm.interfaces.enums.DefaultSelectEnum;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusBooleanEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmResourcePO对象
 *
 * <AUTHOR>
 * @desc 资源表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_resource", autoResultMap = true)
public class UpmResourcePO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 资源系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 链接地址
     */
    @TableField("url")
    private String url;

    /**
     * 编号
     */
    @TableField("code")
    private String code;

    /**
     * 资源类型（1: 菜单，2：按钮）
     */
    @TableField("type")
    private ResourceTypeEnum type;

    /**
     * 首字母大写，一定要与vue文件的name对应起来，用于noKeepAlive缓存控制（该项特别重要）
     */
    @TableField("name")
    private String name;

    /**
     * 排序
     */
    @TableField("resource_sort")
    private Integer resourceSort;

    /**
     * 上级菜单
     */
    @TableField("pid")
    private Long pid;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 菜单、面包屑、多标签页显示的名称
     */
    @TableField("title")
    private String title;

    /**
     * 前端path
     */
    @TableField("path")
    private String path;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 前端组件路径
     */
    @TableField("component")
    private String component;

    /**
     * 路由重定向
     */
    @TableField("redirect")
    private String redirect;

    /**
     * 菜单激活（指定激活菜单的path）
     */
    @TableField("active_path")
    private String activePath;

    /**
     * 按钮权限标识
     */
    @TableField("auths")
    private String auths;

    /**
     * 需要内嵌的iframe链接地址
     */
    @TableField("frame_src")
    private String frameSrc;

    /**
     * 选中状态:0不选中,1选中
     */
    @TableField("default_select")
    private DefaultSelectEnum defaultSelect;

    /**
     * 是否在菜单中显示（默认值：true）
     */
    @TableField("show_link")
    private StatusBooleanEnum showLink;

    /**
     * 是否显示父级菜单（默认值：true）
     */
    @TableField("show_parent")
    private StatusBooleanEnum showParent;

    /**
     * 当前路由是否不缓存（默认值：false）
     */
    @TableField("keep_alive")
    private StatusBooleanEnum keepAlive;

    /**
     * 当前路由是否固定标签页（默认值：false）
     */
    @TableField("fixed_tag")
    private StatusBooleanEnum fixedTag;

    /**
     * 当前菜单名称或自定义信息禁止添加到标签页（默认值：false）
     */
    @TableField("hidden_tag")
    private StatusBooleanEnum hiddenTag;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    public UpmRoleResourceDTO toUpmRoleResourceDTO() {
        return new UpmRoleResourceDTO()
            .setId(this.id)
            .setSystemId(this.systemId)
            .setType(this.type)
            .setPid(this.pid)
            .setTitle(this.title)
            .setName(this.name)
            .setPath(this.path)
            .setComponent(this.component)
            .setResourceSort(this.resourceSort)
            .setRedirect(this.redirect)
            .setIcon(this.icon)
            .setActivePath(this.activePath)
            .setAuths(this.auths)
            .setFrameSrc(this.frameSrc)
            .setShowLink(this.showLink )
            .setShowParent(this.showParent)
            .setKeepAlive(this.keepAlive)
            .setFixedTag(this.fixedTag)
            .setHiddenTag(this.hiddenTag)
            .setRemark(this.remark)
            .setStatus(this.status);
    }

    public UpmResourceDTO toUpmResourceDTO() {
        return new UpmResourceDTO()
            .setId(this.id)
            .setSystemId(this.systemId)
//            .setCode(this.code)
            .setUrl(this.url)
            .setDefaultSelect(this.defaultSelect)
            .setType(this.type)
            .setPid(this.pid)
            .setTitle(this.title)
            .setName(this.name)
            .setPath(this.path)
            .setComponent(this.component)
            .setResourceSort(this.resourceSort)
            .setRedirect(this.redirect)
            .setIcon(this.icon)
            .setActivePath(this.activePath)
            .setAuths(this.auths)
            .setFrameSrc(this.frameSrc)
            .setShowLink(this.showLink)
            .setShowParent(this.showParent)
            .setKeepAlive(this.keepAlive)
            .setFixedTag(this.fixedTag)
            .setHiddenTag(this.hiddenTag)
            .setRemark(this.remark)
            .setStatus(this.status);
    }

}

