package cn.genn.trans.upm.application.service.action;

import cn.genn.core.exception.CheckException;
import cn.genn.trans.upm.application.assembler.UpmUserLoginAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.repository.UserRepository;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.utils.RSAUtil;
import cn.genn.trans.upm.infrastructure.utils.UpmHmacDigestUtil;
import cn.genn.trans.upm.interfaces.dto.UserAccountBindUserDTO;
import cn.genn.trans.upm.interfaces.dto.UserAccountInfoDTO;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 单点SSO登录账号操作服务
 *
 * @Date: 2024/4/16
 * @Author: kangjian
 */

@Slf4j
@Service
public class SsoAccountActionService {

    @Resource
    private UserRepository userRepository;
    @Resource
    private SystemRepository systemRepository;

    /**
     * 调用upm根据用户名查询用户信息
     *
     * @param userName
     * @return
     */

    public UserAccountInfoDTO queryAccountInfoByUserName(String userName, String systemCode) {
        UpmSystem upmSystem = systemRepository.find(systemCode);
        UpmAccount upmAccount = userRepository.queryAccountByUserName(userName, upmSystem.getId());
        if (Objects.isNull(upmAccount)) {
            return null;
        }
        return UserAccountInfoDTO.builder()
            .accountId(upmAccount.getId())
            .username(upmAccount.getUsername())
            .password(upmAccount.getPassword())
            .salt(upmAccount.getSalt())
            .telephone(upmAccount.getTelephone())
            .build();
    }

    public UserAccountInfoDTO queryAccountInfoByTelephone(String telephone, String systemCode) {
        UpmSystem upmSystem = systemRepository.find(systemCode);
        UpmAccount upmAccount = userRepository.queryAccountByTelephone(telephone, upmSystem.getId());
        if (Objects.isNull(upmAccount)) {
            return null;
        }
        return UserAccountInfoDTO.builder()
            .accountId(upmAccount.getId())
            .username(upmAccount.getUsername())
            .password(upmAccount.getPassword())
            .salt(upmAccount.getSalt())
            .telephone(upmAccount.getTelephone())
            .build();
    }


    /**
     * 比较账号密码 判断是否登录成功
     *
     * @param password
     * @param accountInfo
     * @return
     */
    public boolean checkPassword(String password, UserAccountInfoDTO accountInfo) {
        // 补充对于明文传输的密码的解密
        if (Objects.isNull(accountInfo)) {
            return false;
        }
        String decryptPassword = null;
        try {
            decryptPassword = RSAUtil.decryptBase64(password);
        } catch (Exception e) {
            log.error("password 解密失败", e);
            throw new CheckException(MessageCode.USER_NOT_EXIST);
        }
        // md5加密
        String handledPassword = UpmHmacDigestUtil.hmacSha256AsHex(accountInfo.getSalt(), decryptPassword);
        log.info("handledPassword={}", handledPassword);
        return handledPassword.equals(accountInfo.getPassword());
    }

    /**
     * 通过账号id查询用户信息
     *
     * @param accountId
     * @return
     */

    public List<UserAccountBindUserDTO> queryUserInfoByAccountId(Long accountId) {
        List<UpmUser> upmUserList = userRepository.queryListByAccountId(accountId);
        if (CollectionUtil.isEmpty(upmUserList)) {
            return Lists.newArrayList();
        }
        return UpmUserLoginAssembler.INSTANCE.upmUserList2UserAccountBindUserDTOList(upmUserList);
    }

}
