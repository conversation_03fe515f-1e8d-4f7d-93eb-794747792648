// package cn.genn.trans.upm.infrastructure.config;
//
// import lombok.Data;
// import org.springframework.boot.context.properties.ConfigurationProperties;
// import org.springframework.cloud.context.config.annotation.RefreshScope;
// import org.springframework.stereotype.Component;
//
// /**
//  * 描述
//  *
//  * <AUTHOR>
//  * @date 2024/6/6
//  */
// @Data
// @RefreshScope
// @Component
// @ConfigurationProperties(prefix = "genn.upm")
// public class UpmConfig {
//
//
//
// }
