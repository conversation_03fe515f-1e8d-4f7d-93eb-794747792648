package cn.genn.trans.upm.infrastructure.converter;

import cn.genn.core.model.converter.POConverter;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmUserConverter extends POConverter<UpmUser, UpmUserPO> {
}
