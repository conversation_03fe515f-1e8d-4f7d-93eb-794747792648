package cn.genn.trans.upm.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmRoleAuthResourceRelPO对象
 *
 * <AUTHOR>
 * @desc 角色与权限组资源关联表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_role_auth_resource_rel", autoResultMap = true)
public class UpmRoleAuthResourceRelPO {

    /**
     * 主键自增
     */
    @TableId
    private Long id;

    /**
     * 权限组资源关联id
     */
    @TableField("auth_resource_id")
    private Long authResourceId;

    /**
     * 角色id
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

