package cn.genn.trans.upm.domain.upm.repository;

import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthResourceRelPO;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
public interface AuthResourceRelRepository {

    boolean saveBatch(List<UpmAuthResourceRelPO> list);

    /**
     * 查询关联角色
     */
    List<UpmAuthResourceRelPO> selectByResourceIds(List<Long> resourceIdList,Long tenantId);

    List<UpmAuthResourceRelPO>  selectByResourceIdsAndAuthKey(List<Long> resourceIdList,String authKey);


    boolean deleteTenantId(Long tenantId);

    boolean insertAuthResourceRel(UpmSystem upmSystem, Long resourceId);
}
