package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.TenantTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmTenantPO对象
 *
 * <AUTHOR>
 * @desc 租户表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_tenant", autoResultMap = true)
public class UpmTenantPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 类型（platform：平台方, op:运营方，bp:）
     */
    @TableField("type")
    private TenantTypeEnum type;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 上级id
     */
    @TableField("pid")
    private Long pid;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 集群分组(default)
     */
    @TableField("vpc_group")
    private String vpcGroup;

    /**
     * 集群域名
     */
    @TableField("domain")
    private String domain;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

