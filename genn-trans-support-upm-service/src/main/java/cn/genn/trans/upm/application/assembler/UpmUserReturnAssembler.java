package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.DTOAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import org.mapstruct.*;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UpmUserReturnAssembler extends DTOAssembler<UpmUser, UpmUserDTO> {


    @Mappings({
        @Mapping(source = "id", target = "id"),
        @Mapping(source = "accountId", target = "accountId"),
        @Mapping(source = "tenantId", target = "tenantId"),
        @Mapping(source = "systemId", target = "systemId"),
        @Mapping(source = "status", target = "status"),
        @Mapping(source = "nick", target = "nick"),
        @Mapping(source = "telephone", target = "telephone"),
        @Mapping(source = "deleted", target = "deleted"),
        @Mapping(source = "createTime", target = "createTime"),
        @Mapping(source = "createUserId", target = "createUserId"),
        @Mapping(source = "updateTime", target = "updateTime"),
        @Mapping(source = "updateUserId", target = "updateUserId"),
        @Mapping(source = "updateUserName", target = "updateUserName"),
        @Mapping(source = "createUserName", target = "createUserName"),
        // 手动映射 roleList 字段
        @Mapping(target = "roleList", ignore = true)
    })
    @Override
    UpmUserDTO entity2DTO(UpmUser upmUser);

    @Mappings({
        @Mapping(source = "id", target = "id"),
        @Mapping(source = "accountId", target = "accountId"),
        @Mapping(source = "tenantId", target = "tenantId"),
        @Mapping(source = "systemId", target = "systemId"),
        @Mapping(source = "status", target = "status"),
        @Mapping(source = "nick", target = "nick"),
        @Mapping(source = "telephone", target = "telephone"),
        @Mapping(source = "deleted", target = "deleted"),
        @Mapping(source = "createTime", target = "createTime"),
        @Mapping(source = "createUserId", target = "createUserId"),
        @Mapping(source = "updateTime", target = "updateTime"),
        @Mapping(source = "updateUserId", target = "updateUserId"),
        @Mapping(source = "updateUserName", target = "updateUserName"),
        @Mapping(source = "createUserName", target = "createUserName"),
        @Mapping(target = "roleList", ignore = true)
    })
    @Override
    UpmUser DTO2Entity(UpmUserDTO upmUserDTO);

}

