package cn.genn.trans.upm.interfaces.feishu;

import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.application.service.action.SsoAccountFeishuActionService;
import cn.genn.trans.upm.application.service.action.SsoTokenActionService;
import cn.genn.trans.upm.interfaces.base.web.exception.MessageCode;
import cn.genn.trans.upm.interfaces.command.feishu.UpmFSUserCommand;
import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.feishu.FeishuLoginUserDTO;
import cn.genn.trans.upm.interfaces.query.SsoUserTokenQuery;
import cn.genn.trans.upm.interfaces.query.feishu.SsoAccountFeishuLoginQuery;
import cn.genn.trans.upm.interfaces.query.feishu.SsoAccountFeishuSmsLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoUserTokenMiniQuery;
import cn.hutool.core.util.StrUtil;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RequestMapping("/sso/feishu")
@Api(tags = "飞书sso用户登录管理")
@RestController
public class FeishuSsoController {

    @Resource
    private SsoAccountFeishuActionService ssoService;
    @Resource
    private SsoTokenActionService ssoTokenActionService;

    @PostMapping("/smsLogin")
    @ApiOperation("小程序短信验证码登录")
    public FeishuLoginUserDTO smsLogin(@Validated @RequestBody SsoAccountFeishuSmsLoginQuery query) {
        FeishuLoginUserDTO feishuLoginUserDTO;
        try{
            feishuLoginUserDTO = ssoService.smsLogin(query);
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("feishu smsLogin error,query:{}" ,new Gson().toJson(query),e);
            throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
        return feishuLoginUserDTO;
    }

    @PostMapping("/accountLogin")
    @ApiOperation("小程序账号登录")
    public FeishuLoginUserDTO accountLogin(@Validated @RequestBody SsoAccountFeishuLoginQuery query) {
        FeishuLoginUserDTO feishuLoginUserDTO;
        try{
            feishuLoginUserDTO = ssoService.accountLogin(query);
        } catch (BusinessException e){
            throw e;
        } catch (Exception e){
            log.error("feishu accountLogin error,query:{}" ,new Gson().toJson(query),e);
            throw new BusinessException(MessageCode.LOGIN_ERROR);
        }
        return feishuLoginUserDTO;
    }

    /**
     * 根据token获取用户信息
     *
     * @return
     */
    @PostMapping("/userInfo")
    @ApiOperation("子系统根据token获取用户信息")
    public LoginUserAuthInfoDTO feishuUserInfo(@Validated @RequestBody SsoUserTokenMiniQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            token = StpUtil.getTokenValue();
        }
        LoginUserAuthInfoDTO loginUserAuthInfo = ssoService.feishuUserInfo(token);
        return loginUserAuthInfo;
    }

    /**
     * 按设备查询同一个账号下的所有会话 并注销
     *
     * @return
     */
    @PostMapping("/logout")
    @ApiOperation("登出")
    public Boolean logout(@Validated @RequestBody SsoUserTokenQuery query) {
        ssoTokenActionService.ssoAccountLogout(query.getToken(), query.getUserDeviceIdentify());
        return true;
    }

    /**
     * token续约
     *
     * @return
     */
    @PostMapping("/refreshToken")
    @ApiOperation("子系统token续约")
    public Boolean refreshToken(@Validated @RequestBody SsoUserTokenQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            token = StpUtil.getTokenValue();
        }
        ssoTokenActionService.refreshToken(token);
        return true;
    }

    /**
     * 智能体登录
     *
     * @return
     */
    @PostMapping("/agentLogin")
    @ApiOperation("飞书三方登录")
    public LoginUserAccountDTO getLoginUserAccount(@Validated @RequestBody UpmFSUserCommand command) {
        return ssoTokenActionService.getLoginUserAccount(command);
    }


    /**
     *
     * @param command
     * @return
     */
    @PostMapping("/agentLoginWithoutRegister")
    @ApiOperation(value = "飞书三方登录",notes = "只能登录系统绑定过飞书账号，有可能飞书手机号与系统绑定手机号不相同,不注册账号")
    public LoginUserAccountDTO getFsLoginUserAccount(@Validated @RequestBody UpmFSUserCommand command) {
        return ssoTokenActionService.getFsLoginUserAccount(command);
    }

}
