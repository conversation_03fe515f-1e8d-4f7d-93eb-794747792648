package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmTenantSystemPO对象
 *
 * <AUTHOR>
 * @desc 租户系统关联表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_tenant_system", autoResultMap = true)
public class UpmTenantSystemPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 租户系统样式（为空则默认系统样式）
     */
    @TableField("pattern")
    private String pattern;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @TableField(value = "check_rule",typeHandler = JacksonTypeHandler.class)
    private CheckRulePO checkRule;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

