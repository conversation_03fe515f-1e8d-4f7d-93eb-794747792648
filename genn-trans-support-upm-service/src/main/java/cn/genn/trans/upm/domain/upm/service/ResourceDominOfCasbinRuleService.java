package cn.genn.trans.upm.domain.upm.service;

import cn.genn.cache.redis.component.RedisService;
import cn.genn.cache.redis.component.RedisServiceImpl;
import cn.genn.monitor.health.HealthHolder;
import cn.genn.monitor.health.SystemHealthStatusEnum;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.domain.upm.repository.UserRepository;
import cn.genn.trans.upm.infrastructure.config.RedisOfCasbinAdapter;
import cn.genn.trans.upm.infrastructure.constant.CacheConstants;
import cn.genn.trans.upm.infrastructure.dto.RoleUserRelDTO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.casbin.jcasbin.main.Enforcer;
import org.casbin.jcasbin.model.Model;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 资源相关的casbin规则服务
 *
 * @Date: 2024/4/19
 * @Author: kangjian
 */
@Service
@Slf4j
public class ResourceDominOfCasbinRuleService implements ApplicationRunner {

    public static final String USER_ID_PRE = "user_id_";
    public static final String ROLE_ID_PRE = "role_id_";
    public static final String AUTH_KEY_PRE = "auth_key_";
    public static final String ALLOW = "ALLOW";
    public static final String DENY = "DENY";
    public static final long LOAD_TIMEOUT = 30 * 60 * 1000;
    private RedisOfCasbinAdapter redisAdapter = null;
    private Enforcer enforcer = null;

    @Resource
    private RoleRepository roleRepository;
    @Resource
    private ResourceRepository resourceRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private RedisTemplate redisTemplate;

    private RedisService redisService;

    private Set<String> urlGlobalSet = new HashSet<>();

    @PostConstruct
    public void resourceDominOfCasbinRuleService() {
        String conf = ResourceUtil.readUtf8Str("casbin_model/rbac_domain_model.conf");
        Model model = Model.newModelFromString(conf);
        redisService = new RedisServiceImpl(redisTemplate);
        this.redisAdapter = new RedisOfCasbinAdapter(redisService);
        this.enforcer = new Enforcer(model, redisAdapter);
    }

    /**
     * Callback used to run the bean.
     *
     * @param args incoming application arguments
     * @throws Exception on error
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始加载全量csbin规则");
        // 启动时查询如果是30分钟内构造的 就没必要再去全量构造一遍数据
        initUrlGlobalSet();
        Object value = redisService.get(CacheConstants.CASBIN_RULE_URL_RESOURCE_ALL_LOAD, Long.class);
        if (Objects.nonNull(value)) {
            log.info("上次全量casbin规则的加载时间为: {}", value);
            long currentTimeMillis = System.currentTimeMillis();
            if (currentTimeMillis - (Long) value < LOAD_TIMEOUT) {
                log.info("本次全量casbin规则的加载时间小于30分钟, 不需要重新加载");
                HealthHolder.setStatus(SystemHealthStatusEnum.UP);
                return;
            }
        }
        convertUrlOfResourceToCasbinRule();
        log.info("加载全量csbin规则done");
        HealthHolder.setStatus(SystemHealthStatusEnum.UP);
    }

    public void initUrlGlobalSet() {
        List<UpmResource> upmResourceList = resourceRepository.findAllOfUrlNotNull();
        urlGlobalSet = upmResourceList.stream().map(UpmResource::getUrl).collect(Collectors.toSet());
        log.info("urlGlobalSet: {} ", urlGlobalSet);
    }


    /**
     * casbin存储的规则为  p 角色id 资源url allow  g 用户id 角色id
     * p, role_id, resource_url, allow
     * g, user_id, role_id
     */

    /**
     * 将upm的资源转变为casbin规则
     * 1. bean初始化完成后进行调用
     * 2. 修改 用户-角色 角色-权限 绑定关系后全量刷新
     * 3. 按系统、租户的组合维度进行casbin规则的redis存储
     */
    public void convertUrlOfResourceToCasbinRule() {
        Table<Long, Long, List<UpmRole>> table = getAllRoleAndResourceTable();
        Table<Long, Long, List<RoleUserRelDTO>> userRoleTable = getAllUserAndRoleTable();
        table.cellSet()
            .forEach(cell -> storeCasbinRuleWithSystemAndTenant(cell.getRowKey(), cell.getColumnKey(), cell.getValue(), userRoleTable.get(cell.getRowKey(), cell.getColumnKey())));
        // 记录下全量的更新时间
        redisService.set(CacheConstants.CASBIN_RULE_URL_RESOURCE_ALL_LOAD, String.valueOf(System.currentTimeMillis()), 3, TimeUnit.DAYS);
    }


    private void storeCasbinRuleWithSystemAndTenant(Long systemId, Long tenantId, List<UpmRole> upmRoleList, List<RoleUserRelDTO> roleUserRelDTOS) {
        redisAdapter.generateKeyData(systemId, tenantId);
        enforcer.clearPolicy();
        List<List<String>> policyDefinitions = generateAllPolicyDefinition(upmRoleList);
        if (CollectionUtil.isNotEmpty(policyDefinitions)) {
            policyDefinitions.forEach(policy -> enforcer.addPolicy(policy.get(0), policy.get(1), policy.get(2), policy.get(3)));
        }
        List<List<String>> rules = generateAllRoleDefinition(roleUserRelDTOS);
        if (CollectionUtil.isNotEmpty(rules)) {
            rules.forEach(rule -> enforcer.addRoleForUserInDomain(rule.get(0), rule.get(1), rule.get(2)));
        }
        // 更新casbin规则
        redisAdapter.savePolicy(enforcer.getModel());
    }

    /**
     * 按系统维度和租户维度重新加载casbin规则
     * 发一条redis消息在消息中进行处理
     */
    public void reloadCasbinRule(Long systemId, Long tenantId, List<UpmRole> upmRoleList) {
        redisAdapter.generateKeyData(systemId, tenantId);
        Table<Long, Long, List<RoleUserRelDTO>> userRoleTable = getAllUserAndRoleTable();
        storeCasbinRuleWithSystemAndTenant(systemId, tenantId, upmRoleList, userRoleTable.get(systemId, tenantId));
    }

    /**
     * 获取所有的角色和资源的关系
     * row: systemId celumn: tenantId value: upmRoleList
     *
     * @return
     */
    public Table<Long, Long, List<UpmRole>> getAllRoleAndResourceTable() {
        List<UpmRole> upmRoleList = roleRepository.queryAllRoleAndResource();
        // 按系统 租户的维度进行规则的拆分
        Table<Long, Long, List<UpmRole>> employeeTable = HashBasedTable.create();
        upmRoleList.forEach(upmRole -> {
            if (employeeTable.contains(upmRole.getSystemId(), upmRole.getTenantId())) {
                List<UpmRole> roleList = employeeTable.get(upmRole.getSystemId(), upmRole.getTenantId());
                roleList.add(upmRole);
            } else {
                List<UpmRole> roleList = new ArrayList<>();
                roleList.add(upmRole);
                employeeTable.put(upmRole.getSystemId(), upmRole.getTenantId(), roleList);
            }
        });
        return employeeTable;
    }

    /**
     * 获取所有角色绑定的用户 包含用户的租户和系统
     * row: systemId celumn: tenantId value: 用户->角色列表id
     *
     * @return
     */
    private Table<Long, Long, List<RoleUserRelDTO>> getAllUserAndRoleTable() {
        List<RoleUserRelDTO> roleUserRelDTOS = roleRepository.getAllUserAndRole();
        Table<Long, Long, List<RoleUserRelDTO>> roleUserTable = HashBasedTable.create();
        roleUserRelDTOS.forEach(roleUserRelDTO -> {
            if (roleUserTable.contains(roleUserRelDTO.getSystemId(), roleUserRelDTO.getTenantId())) {
                List<RoleUserRelDTO> roleUserRelDTOList = roleUserTable.get(roleUserRelDTO.getSystemId(), roleUserRelDTO.getTenantId());
                roleUserRelDTOList.add(roleUserRelDTO);
            } else {
                List<RoleUserRelDTO> roleUserRelDTOList = new ArrayList<>();
                roleUserRelDTOList.add(roleUserRelDTO);
                roleUserTable.put(roleUserRelDTO.getSystemId(), roleUserRelDTO.getTenantId(), roleUserRelDTOList);
            }
        });
        return roleUserTable;
    }

    /**
     * 生成所有的policy规则,用户修改任一资源 角色权限时触发全量更新
     * p 角色id  权限组 资源uri
     *
     * @return
     */
    public List<List<String>> generateAllPolicyDefinition(List<UpmRole> upmRoleList) {
        enforcer.clearPolicy();
        List<List<String>> policys = new ArrayList<>();
        // 查询url资源存在于哪些角色下 添加p规则  注意用户id需要加前缀
        upmRoleList.forEach(upmRole -> {
            upmRole.getResourceList().stream()
                .filter(upmResource -> StrUtil.isNotBlank(upmResource.getPath()))
                .forEach(upmResource -> policys.add(Arrays.asList(ROLE_ID_PRE + upmRole.getId().toString(),
                    AUTH_KEY_PRE + upmRole.getAuthKey(), upmResource.getPath(), ALLOW)));
        });

        // p, *, *, DENY 意思是禁止不在p规则的url通过
        policys.add(Arrays.asList("*", "*", "*", DENY));
        return policys;
    }

    /**
     * 生成所有的g规则 获取所有用户绑定的角色id
     * g 用户id 角色id 权限组
     *
     * @return
     */
    public List<List<String>> generateAllRoleDefinition(List<RoleUserRelDTO> upmRoleList) {
        if (CollectionUtil.isEmpty(upmRoleList)) {
            return new ArrayList<>();
        }
        List<List<String>> gRules = new ArrayList<>();
        upmRoleList.forEach(userRole -> {
            gRules.add(Arrays.asList(USER_ID_PRE + userRole.getUserId(), ROLE_ID_PRE + userRole.getRoleId(), AUTH_KEY_PRE + userRole.getAuthKey()));
        });
        return gRules;
    }

    /**
     * 校验用户是否有权限访问url
     *
     * @param url
     * @return
     */
    public boolean checkPermission(UpmUserPO user, String url) {
        // 增加全局url资源 如果在全局中 就正常casbin校验，如果不在全局中是非核心接口，就直接放过不进行校验
        if (!matchesAnyUrl(url, urlGlobalSet)) {
            log.info("url: {} 不存在于全局url资源 跳过鉴权 默认通过", url);
            return true;
        }
        Long userId = user.getId();
        redisAdapter.generateKeyData(user.getSystemId(), user.getTenantId());
        enforcer.loadPolicy();
        return enforcer.enforce(USER_ID_PRE + userId.toString(), user.getAuthKey(), url, ALLOW);
    }

    // 检查给定的URL是否匹配任何通配符URL
    public static boolean matchesAnyUrl(String url, Set<String> wildcardUrls) {
        for (String wildcardUrl : wildcardUrls) {
            String regex = wildcardToRegex(wildcardUrl);
            Pattern pattern = Pattern.compile(regex);
            if (pattern.matcher(url).matches()) {
                return true;
            }
        }
        return false;
    }

    // 将通配符转换为正则表达式
    private static String wildcardToRegex(String pattern) {
        return pattern.replace("**", ".*?");
    }

}
