package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.ThridTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmUserThirdPO对象
 *
 * <AUTHOR>
 * @desc 用户与三方绑定关系表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_user_third", autoResultMap = true)
public class UpmUserThirdPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 类型（例如：wx代表微信）
     */
    @TableField("type")
    private ThridTypeEnum type;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 微信union_id
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 微信open_id
     */
    @TableField("open_id")
    private String openId;

    @TableField("app_id")
    private String appId;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 额外信息
     */
    @TableField("extra_info")
    private String extraInfo;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

