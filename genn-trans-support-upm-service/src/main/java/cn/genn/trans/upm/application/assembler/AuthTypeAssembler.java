package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeTemplatePO;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleSaveCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleUpdateCommand;
import cn.genn.trans.upm.interfaces.dto.UpmAuthTypeRoleTemplateDTO;
import cn.genn.trans.upm.interfaces.dto.UpmAuthTypeTemplateDTO;
import cn.genn.trans.upm.interfaces.query.AuthTypeQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/26
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AuthTypeAssembler extends QueryAssembler<AuthTypeQuery, UpmAuthTypeTemplatePO, UpmAuthTypeTemplateDTO> {


    @Mappings({@Mapping(
        target = "pageNo",
        source = "current"
    ), @Mapping(
        target = "pageSize",
        source = "size"
    ), @Mapping(
        target = "list",
        source = "records"
    ), @Mapping(
        target = "totalPages",
        source = "pages"
    )})
    PageResultDTO<UpmAuthTypeRoleTemplateDTO> rolePO2DTO(IPage<UpmAuthTypeRoleTemplatePO> po);

    UpmAuthTypeRoleTemplatePO roleSaveCommand2PO(UpmAuthRoleSaveCommand command);

    UpmAuthTypeRoleTemplatePO roleUpdateCommand2PO(UpmAuthRoleUpdateCommand command);
}
