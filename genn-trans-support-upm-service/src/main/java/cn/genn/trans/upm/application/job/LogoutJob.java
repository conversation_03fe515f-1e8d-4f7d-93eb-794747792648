package cn.genn.trans.upm.application.job;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.genn.trans.upm.application.dto.LogoutDTO;
import cn.genn.trans.upm.application.service.action.SsoTokenActionService;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class LogoutJob extends AbstractJobHandler {

    @Resource
    private SsoTokenActionService ssoTokenActionService;

    @Override
    public void doExecute() {
        String jobParam = XxlJobHelper.getJobParam();
        LogoutDTO logoutDTO = JsonUtils.parse(jobParam, LogoutDTO.class);
        //按账号退出
        if(CollUtil.isNotEmpty(logoutDTO.getAccountIds())){
            logoutDTO.getAccountIds().forEach(ssoTokenActionService::ssoAccountLogout);
        }
        //按用户退出
        if(CollUtil.isNotEmpty(logoutDTO.getUserIds())){
            ssoTokenActionService.logoutByUserIdList(logoutDTO.getUserIds());
        }
        //按系统类型退出
        if(StrUtil.isNotBlank(logoutDTO.getSystemType())){
            SystemTypeEnum systemTypeEnum = SystemTypeEnum.of(logoutDTO.getSystemType());
            ssoTokenActionService.logoutBySystemType(systemTypeEnum);
        }
        //按系统id退出
        if(CollUtil.isNotEmpty(logoutDTO.getSystemIds())){
            ssoTokenActionService.logoutBySystemIds(logoutDTO.getSystemIds());
        }
        //按AuthKey
        if(StrUtil.isNotBlank(logoutDTO.getAuthKey())){
            ssoTokenActionService.logoutByAuthKey(logoutDTO.getAuthKey());
        }

    }
}
