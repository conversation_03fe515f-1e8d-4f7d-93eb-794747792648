package cn.genn.trans.upm.application.dto;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserAccountDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 账号名
     */
    private String username;

    /**
     * 账号类型(system,default,unity)
     */
    private String type;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 逻辑删除
     */
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private Long updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;

    /**
     * 创建用户名
     */
    private String createUserName;

}
