package cn.genn.trans.upm.interfaces;

import cn.genn.trans.upm.application.service.action.UpmUnityAccountActionService;
import cn.genn.trans.upm.application.service.query.UpmUnityAccountQueryService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "统一账号管理")
@RestController
@RequestMapping("/upmUnityAccount")
public class UpmUnityAccountController {

    @Resource
    private UpmUnityAccountQueryService queryService;
    @Resource
    private UpmUnityAccountActionService actionService;

    //分页查询统一账号

    //添加统一账号

    //编辑绑定关系

    //删除统一账号
}
