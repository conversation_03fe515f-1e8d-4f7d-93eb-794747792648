package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.page.SortOrder;
import cn.genn.trans.upm.application.assembler.UpmSystemAssembler;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmSystemPO;
import cn.genn.trans.upm.interfaces.dto.UpmSystemDTO;
import cn.genn.trans.upm.interfaces.query.SystemQuery;
import cn.genn.trans.upm.interfaces.query.UpmSystemQuery;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmSystemQueryService {

    @Resource
    private UpmSystemMapper mapper;
    @Resource
    private SystemRepository repository;
    @Resource
    private UpmSystemAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return UpmSystemDTO分页对象
     */
    public PageResultDTO<UpmSystemDTO> page(UpmSystemQuery query) {
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), getQueryWrapperByQuery(query)));
    }

    /**
     * 查询列表
     *
     * @param query 查询条件
     * @return UpmResourceDTO对象
     */
    public List<UpmSystemDTO> queryList(SystemQuery query) {
        return assembler.PO2DTO(repository.selectList(query));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return UpmSystemDTO
     */
    public UpmSystemDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }

    public UpmSystemDTO getByCode(String code) {
        return assembler.PO2DTO(mapper.selectByCode(code));
    }

    /**
     * 根据id查询
     *
     * @param idList
     * @return UpmSystemDTO
     */
    public List<UpmSystemDTO> getByIdList(List<Long> idList) {
        return assembler.PO2DTO(mapper.selectBatchIds(idList));
    }

    public List<UpmSystemDTO> list(List<Long> idList){
       return assembler.PO2DTO(mapper.selectBatchIds(idList));
    }

    public List<UpmSystemDTO> listAll(){
        return assembler.PO2DTO(mapper.selectList(null));
    }

    QueryWrapper<UpmSystemPO> getQueryWrapperByQuery(UpmSystemQuery query) {
        QueryWrapper<UpmSystemPO> wrapper = new QueryWrapper<>();
        wrapper.eq(Objects.nonNull(query.getStatus()), "status", query.getStatus());
        wrapper.like(StringUtils.isNotBlank(query.getName()), "name", query.getName());
        wrapper.like(StringUtils.isNotBlank(query.getCode()), "code", query.getCode());

        //排序条件
        if (query.getSort() != null && query.getSort().getOrderBy() != null) {
            Map<String, SortOrder> orderBy = query.getSort().getOrderBy();
            orderBy.forEach((k, v) -> {
                if (v == SortOrder.ASC) {
                    wrapper.orderByAsc(k);
                } else {
                    wrapper.orderByDesc(k);
                }
            });
        }

        return wrapper;
    }


}

