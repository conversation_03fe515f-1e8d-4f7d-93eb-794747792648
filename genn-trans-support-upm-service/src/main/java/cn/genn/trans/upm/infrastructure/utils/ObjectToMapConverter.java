package cn.genn.trans.upm.infrastructure.utils;

/**
 * 对象转Map
 *
 * <AUTHOR>
 * @date 2024/7/1
 */
import lombok.SneakyThrows;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class ObjectToMapConverter {

    @SneakyThrows
    public static Map<String, Object> convert(Object obj){
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            map.put(field.getName(), field.get(obj));
        }
        return map;
    }
}
