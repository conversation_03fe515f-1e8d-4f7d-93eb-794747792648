package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountUnityRelationPO;
import cn.genn.trans.upm.interfaces.dto.UpmAccountUnityRelationDTO;
import cn.genn.trans.upm.interfaces.query.UpmAccountUnityRelationQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmAccountUnityRelationAssembler extends QueryAssembler<UpmAccountUnityRelationQuery, UpmAccountUnityRelationPO, UpmAccountUnityRelationDTO>{
}

