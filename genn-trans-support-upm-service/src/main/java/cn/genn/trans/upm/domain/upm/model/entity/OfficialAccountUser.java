package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.trans.upm.interfaces.command.official.SsoAccountOfficialWXTempLoginCommand;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import cn.genn.trans.upm.interfaces.enums.AccountTypeEnum;
import cn.genn.trans.upm.interfaces.enums.LoginTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信公众号用户实体
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfficialAccountUser {

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 系统code
     */
    private String systemCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 密码
     */
    private String password;

    /**
     * 账号
     */
    private String username;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 登录类型（phone:手机，normal:普通账号）
     */
    private LoginTypeEnum loginType;

    /**
     * 类型（system:系统账号，default:默认账号；unity:统一登录账号）
     */
    private AccountTypeEnum type;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 微信union_id
     */
    private String unionId;

    /**
     * 微信open_id
     */
    private String openId;

    /**
     * 微信appId
     */
    private String appId;

    /**
     * authKey
     */
    private String authKey;

    public static OfficialAccountUser create(SsoAccountOfficialWXTempLoginCommand command, Long systemId) {
        return OfficialAccountUser.builder()
            .systemId(systemId)
            .systemCode(command.getSystemCode())
            .tenantId(command.getTenantId())
            .status(StatusEnum.ENABLE)
            .loginType(LoginTypeEnum.PHONE)
            .type(AccountTypeEnum.DEFAULT)
            .telephone(command.getTelephone())
            .username(command.getTelephone())
            .openId(command.getOpenId())
            .appId(command.getAppId())
            .build();
    }

    public static void fillWxInfo(UserWxInfoDTO userWxInfoDTO, OfficialAccountUser officialAccountUser) {
        officialAccountUser.setUserId(userWxInfoDTO.getUserId());
        officialAccountUser.setOpenId(userWxInfoDTO.getOpenId());
        officialAccountUser.setAppId(userWxInfoDTO.getAppId());
    }

    public void fillAuthKey() {
        this.authKey = AuthKeyUtil.getPlatformKey(this.getSystemId(), this.getTenantId());
    }
}
