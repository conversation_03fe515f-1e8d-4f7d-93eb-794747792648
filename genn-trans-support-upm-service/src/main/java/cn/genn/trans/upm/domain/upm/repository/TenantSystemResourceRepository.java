// package cn.genn.trans.upm.domain.upm.repository;
//
// import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
// import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemResourcePO;
//
// import java.util.List;
//
// public interface TenantSystemResourceRepository {
//
//     /**
//      * 查询关联角色
//      */
//     List<UpmTenantSystemResourcePO> selectByResourceIds(List<Long> resourceIdList, Long tenantId);
//
//
//     boolean deleteTenantId(Long tenantId);
//
//     /**
//      * 关联
//      */
//     boolean insertTenantSystemResource(UpmSystem upmSystem, Long resourceId);
// }
