package cn.genn.trans.upm.infrastructure.repository.mapper;


import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeTemplatePO;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.query.AuthTypeQuery;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmAuthTypeTemplateMapper extends BaseMapper<UpmAuthTypeTemplatePO> {

    default List<UpmAuthTypeTemplatePO> query(AuthTypeQuery query,Long systemId){
        LambdaQueryWrapper<UpmAuthTypeTemplatePO> wrapper = Wrappers.lambdaQuery(UpmAuthTypeTemplatePO.class)
            .eq(UpmAuthTypeTemplatePO::getSystemId, systemId)
            .eq(ObjUtil.isNotNull(query.getAuthType()), UpmAuthTypeTemplatePO::getAuthType, query.getAuthType());
        return selectList(wrapper);
    }

    default UpmAuthTypeTemplatePO selectBySystemIdAndType(Long systemId, AuthGroupEnum authGroup){
        LambdaQueryWrapper<UpmAuthTypeTemplatePO> wrapper = Wrappers.lambdaQuery(UpmAuthTypeTemplatePO.class)
            .eq(UpmAuthTypeTemplatePO::getSystemId, systemId)
            .eq(UpmAuthTypeTemplatePO::getAuthType, authGroup);
        return selectOne(wrapper);
    }
}
