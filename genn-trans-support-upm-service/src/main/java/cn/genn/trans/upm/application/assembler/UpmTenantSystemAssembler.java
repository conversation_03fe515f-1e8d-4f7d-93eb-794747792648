package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
import cn.genn.trans.upm.interfaces.dto.UpmTenantSystemDTO;
import cn.genn.trans.upm.interfaces.query.UpmTenantSystemQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmTenantSystemAssembler extends QueryAssembler<UpmTenantSystemQuery, UpmTenantSystemPO, UpmTenantSystemDTO>{
}

