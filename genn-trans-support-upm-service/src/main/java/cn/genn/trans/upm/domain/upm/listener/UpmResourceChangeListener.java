package cn.genn.trans.upm.domain.upm.listener;

import cn.genn.spring.boot.starter.event.spring.component.SpringEventAsyncListener;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEvent;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEventDimensionEnum;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.service.ResourceDomainService;
import cn.genn.trans.upm.domain.upm.service.SystemDomainService;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmResourceMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthResourceRelPO;
import cn.genn.trans.upm.interfaces.command.SystemResourceCommand;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统资源变更后，站端管理员权限变更，暂时的解决方案，站端和服务产品现在没有关系
 */
@Component
@Slf4j
public class UpmResourceChangeListener extends SpringEventAsyncListener<UpmAuthChangeEvent> {

    @Resource
    private ResourceRepository resourceRepository;


    @Resource
    private SystemRepository systemRepository;

    @Resource
    private ResourceDomainService resourceDomainService;

    @Resource
    private SystemDomainService systemDomainService;

    @Resource
    private UpmAuthResourceRelMapper authResourceRelMapper;
    @Autowired
    private UpmResourceMapper upmResourceMapper;


    @Override
    protected void onMessage(UpmAuthChangeEvent event) {
        log.info("UpmResourceChangeListener.processing.begin body={}", JSONUtil.toJsonStr(event));
        if (event.getDimension() != UpmAuthChangeEventDimensionEnum.RESOURCE) {
            return;
        }

        if (Objects.isNull(event.getResourceId())) {
            return;
        }
        try {
            changeResource(event.getResourceId());
            log.info("UpmResourceChangeListener.processing.end body={}", JSONUtil.toJsonStr(event));
        } catch (Exception e) {
            log.error("UpmResourceChangeListener.processing.error : ", e);
        }

    }

    public void changeResource (Long resourceId) {
        UpmResource upmResource = resourceRepository.find(resourceId);
        if (Objects.isNull(upmResource) || Objects.isNull(upmResource.getSystemId())) {
            return;
        }
        UpmSystem upmSystem = systemRepository.find(upmResource.getSystemId());
        if (Objects.isNull(upmSystem)) {
            return;
        }
        if (!SystemTypeEnum.STATION.equals(upmSystem.getType())) {
            return;
        }
        /**
         * 更新站端所有管理员的资源列表
         */
        // 获取"指定系统"下所有系统id 的所有资源
        List<UpmResource> resourceList = resourceRepository.findListBySystemId(Collections.singletonList(upmSystem.getId()));
        Map<Long, Set<Long>> systemResourceByAuthKey = resourceList.stream().collect(Collectors.groupingBy(UpmResource::getSystemId, Collectors.mapping(UpmResource::getId, Collectors.toSet())));

        // 补充资源ID列表
        List<SystemResourceCommand> systemResourceCommands = systemResourceByAuthKey.entrySet().stream().map(entry -> {
            SystemResourceCommand systemResourceCommand = new SystemResourceCommand();
            systemResourceCommand.setSystemId(entry.getKey());
            systemResourceCommand.setResourceIdList(new ArrayList<>(entry.getValue()));
            return systemResourceCommand;
        }).collect(Collectors.toList());


        List<Long> systemIdList = Optional.ofNullable(systemResourceCommands)
            .orElse(new ArrayList<>(0))
            .stream().map(SystemResourceCommand::getSystemId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(systemIdList)) {
            return;
        }
        // 获取已经存在的所有 租户、站点信息
        List<UpmAuthResourceRelPO> upmAuthResourceRelPOS = authResourceRelMapper.selectByAuthGroupAndSystemId(AuthGroupEnum.STATION, upmSystem.getId());
        if (CollectionUtils.isEmpty(upmAuthResourceRelPOS)) {
            return;
        }
        Map<Long, Set<Long>> tenantIdOriginIdMap = upmAuthResourceRelPOS.stream()
            .collect(Collectors.groupingBy(UpmAuthResourceRelPO::getTenantId,
                Collectors.mapping(item -> AuthKeyUtil.getOriginId(item.getAuthKey()), Collectors.toSet())));

        tenantIdOriginIdMap.entrySet().forEach(entry -> {
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                entry.getValue().forEach(stationId -> {
                    resourceDomainService.batchSystemReplaceAuthResource(systemResourceCommands,entry.getKey(), stationId ,AuthGroupEnum.STATION);
                });
            }
        });
    }

}
