package cn.genn.trans.upm.domain.upm.service;


import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEventDimensionEnum;
import cn.genn.trans.upm.application.service.action.SpringEventPublishService;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.AuthResourceRelRepository;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.specification.ResourceUpdateSpecification;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthGroupMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthGroupPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthResourceRelPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeTemplatePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.command.SystemResourceCommand;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ResourceDomainService {


    @Resource
    private ResourceRepository resourceRepository;
    @Resource
    private AuthResourceRelRepository authResourceRelRepository;
    @Resource
    private UpmAuthResourceRelMapper authResourceRelMapper;
    @Resource
    private SpringEventPublishService eventPublishService;
    @Resource
    private UpmAuthResourceRelMapper upmAuthResourceRelMapper;
    @Autowired
    private UpmTenantSystemMapper upmTenantSystemMapper;
    @Resource
    private UpmRoleAuthResourceRelMapper upmRoleAuthResourceRelMapper;
    @Resource
    private UpmAuthGroupMapper authGroupMapper;

    @Resource
    private SystemRepository systemRepository;


    public UpmResource find(String code) {
        return resourceRepository.find(code);
    }

    public UpmResource find(Long id) {
        return resourceRepository.find(id);
    }

    public UpmResource find(Long systemId, String code) {
        return resourceRepository.find(systemId, code);
    }

    public Boolean create(UpmResource upmResource) {
        Long id = resourceRepository.store(upmResource);

        // 平台级系统直接关联权限组资源表
        UpmSystem upmSystem = upmResource.getUpmSystem();
        if (upmSystem.getType().equals(SystemTypeEnum.PLATFORM)) {
            authResourceRelRepository.insertAuthResourceRel(upmSystem, id);
        }
        eventPublishService.publishUpmAuthChangeEvent(UpmAuthChangeEventDimensionEnum.RESOURCE, id);
        return true;
    }

    public void change(UpmResource upmResource) {
        ResourceUpdateSpecification updateSpecification = new ResourceUpdateSpecification(resourceRepository);
        updateSpecification.isSatisfiedBy(upmResource);
        resourceRepository.update(Collections.singletonList(upmResource));
        eventPublishService.publishUpmAuthChangeEvent(UpmAuthChangeEventDimensionEnum.RESOURCE, upmResource.getId());
    }

    public Boolean changeStatus(List<UpmResource> upmResourceList) {
        return resourceRepository.update(upmResourceList);
    }

    public void remove(List<UpmResource> upmResourceList) {
        resourceRepository.delete(upmResourceList);
    }


    /**
     * 权限组资源关联
     */
    public void saveAuthResourceRel(List<UpmTenantSystemPO> systemPOList, List<Long> operatorSystemIdList,
                                    List<SystemResourceCommand> systemResourceList,
                                    Long tenantId, Long originId, AuthGroupEnum authGroup) {
        List<UpmAuthResourceRelPO> insertDataList = new ArrayList<>();
        // key:systemId-tenantId,value:tenantSystemId;
        Map<String, Long> tenantSystemMap = systemPOList.stream().collect(Collectors.toMap(
            tenantSystemPO -> tenantSystemPO.getSystemId() + "-" + tenantSystemPO.getTenantId(), UpmTenantSystemPO::getId));

        for (SystemResourceCommand systemResourceCommand : systemResourceList) {
            if(operatorSystemIdList.contains(systemResourceCommand.getSystemId())){
                Long systemId = systemResourceCommand.getSystemId();
                for (Long resourceId : systemResourceCommand.getResourceIdList()) {
                    UpmAuthResourceRelPO authResourceRelPO = new UpmAuthResourceRelPO();
                    authResourceRelPO.setTenantSystemId(tenantSystemMap.get(systemId + "-" + tenantId));
                    authResourceRelPO.setResourceId(resourceId);
                    authResourceRelPO.setTenantId(tenantId);
                    authResourceRelPO.setSystemId(systemId);
                    switch(authGroup){
                        case OPERATOR:
                            authResourceRelPO.setAuthKey(AuthKeyUtil.getOperatorKey(systemId, tenantId, originId));
                            break;
                        case CARRIER:
                            authResourceRelPO.setAuthKey(AuthKeyUtil.getCarrierKey(systemId, tenantId, originId));
                    }
                    insertDataList.add(authResourceRelPO);
                }
            }
        }
        authResourceRelRepository.saveBatch(insertDataList);

    }

    public List<UpmResource> saveBatch(List<UpmResource> resources) {
        return resourceRepository.storeBatch(resources);
    }

    public Boolean syncSourceDelBySystemId(Long systemId) {
        UpmSystem upmSystem = systemRepository.find(systemId);
        if (Objects.isNull(upmSystem) || !SystemTypeEnum.STATION.equals(upmSystem.getType())) {
            throw new BusinessException(MessageCode.NOT_YZG_SYNC_RESOURCE_CANT_DEL_RESOURCE);
        }
        return resourceRepository.deleteBySystemId(systemId);
    }

    public void saveCommonAuthResourceRel(List<UpmTenantSystemPO> systemPOList, List<Long> systemIdList,
                                    List<SystemResourceCommand> systemResourceList,
                                    Long tenantId, Long originId, AuthGroupEnum authGroup) {
        // key:systemId-tenantId,value:tenantSystemId;
        Map<String, Long> tenantSystemMap = systemPOList.stream().collect(Collectors.toMap(
            tenantSystemPO -> tenantSystemPO.getSystemId() + "-" + tenantSystemPO.getTenantId(), UpmTenantSystemPO::getId));

        List<UpmAuthResourceRelPO> insertDataList = Optional.ofNullable(systemResourceList).orElse(new ArrayList<>(0))
            .stream()
            .filter(systemResourceCommand -> systemIdList.contains(systemResourceCommand.getSystemId()))
            .map(systemResourceCommand -> {
                Long systemId = systemResourceCommand.getSystemId();
                return systemResourceCommand.getResourceIdList().stream().map(resourceId -> {
                    UpmAuthResourceRelPO authResourceRelPO = new UpmAuthResourceRelPO();
                    authResourceRelPO.setTenantSystemId(tenantSystemMap.get(systemId + "-" + tenantId));
                    authResourceRelPO.setResourceId(resourceId);
                    authResourceRelPO.setTenantId(tenantId);
                    authResourceRelPO.setSystemId(systemId);
                    authResourceRelPO.setAuthKey(AuthKeyUtil.getAuthKey(authGroup, systemId, tenantId, originId));
                    return authResourceRelPO;
                }).collect(Collectors.toList());
            }).flatMap(Collection::stream).collect(Collectors.toList());
        authResourceRelRepository.saveBatch(insertDataList);
    }

    public void saveAuthResourceRel(List<Long> resourceIdList,String authKey,Long systemId,Long tenantId){
        UpmTenantSystemPO tenantSystemPO = upmTenantSystemMapper.selectByTenantIdAndSystemId(tenantId,systemId);
        if(ObjUtil.isNull(tenantSystemPO)){
            throw new BusinessException(MessageCode.SYSTEM_TENANT_REL_NOT_EXIST);
        }
        List<UpmAuthResourceRelPO> insertDataList = new ArrayList<>();
        for (Long resourceId : resourceIdList) {
            UpmAuthResourceRelPO authResourceRelPO = new UpmAuthResourceRelPO()
            	.setAuthKey(authKey)
            	.setResourceId(resourceId)
            	.setSystemId(systemId)
            	.setTenantId(tenantId)
            	.setTenantSystemId(tenantSystemPO.getId());
            insertDataList.add(authResourceRelPO);
        }
        authResourceRelRepository.saveBatch(insertDataList);
    }

    /**
     * 更新资源权限组关联
     * @param systemResourceList
     * @param tenantId
     * @param originId
     * @param authGroup
     */
    public void batchSystemReplaceAuthResource(List<SystemResourceCommand> systemResourceList,Long tenantId,Long originId,AuthGroupEnum authGroup){
        List<Long> systemIds = systemResourceList.stream().map(SystemResourceCommand::getSystemId).distinct().collect(Collectors.toList());
        List<UpmTenantSystemPO> systemTenantList = upmTenantSystemMapper.selectByTenantIdAndSystemIds(tenantId,systemIds);
        Map<Long,UpmTenantSystemPO> systemTenantMap = systemTenantList.stream().collect(Collectors.toMap(UpmTenantSystemPO::getSystemId, Function.identity()));
        for (SystemResourceCommand command : systemResourceList) {
            UpmTenantSystemPO tenantSystemPO = systemTenantMap.get(command.getSystemId());
            if(ObjUtil.isNull(tenantSystemPO)){
                log.warn("更新菜单资源未取到tenantSystemPO,tenantId:{},systemId:{}",tenantId,command);
            }
            this.replaceAuthResourceRel(command,tenantId,originId,authGroup,tenantSystemPO);
        }
    }

    private void replaceAuthResourceRel(SystemResourceCommand command,Long tenantId,Long originId,AuthGroupEnum authGroup,UpmTenantSystemPO tenantSystemPO){
        Long systemId = command.getSystemId();
        List<Long> newResourceIdList = command.getResourceIdList();
        String authKey = null;
        switch(authGroup){
            case OPERATOR:
                authKey = AuthKeyUtil.getOperatorKey(systemId, tenantId, originId); break;
            case CARRIER:
                authKey = AuthKeyUtil.getCarrierKey(systemId, tenantId, originId); break;
            case STATION:
                authKey = AuthKeyUtil.getAuthKey(authGroup, systemId, tenantId, originId); break;
        }
        List<UpmAuthResourceRelPO> authResourceRelPOS = upmAuthResourceRelMapper.selectByAuthKey(authKey);
        List<Long> oldResourceIdList = authResourceRelPOS.stream().map(UpmAuthResourceRelPO::getResourceId).distinct().collect(Collectors.toList());
        //待添加资源
        if(CollUtil.isNotEmpty(newResourceIdList)){
            List<Long> addResourceIdList = newResourceIdList;
            if(CollUtil.isNotEmpty(oldResourceIdList)){
                addResourceIdList = newResourceIdList.stream().filter(id ->!oldResourceIdList.contains(id)).distinct().collect(Collectors.toList());
            }
            if(CollUtil.isNotEmpty(addResourceIdList)){
                List<UpmAuthResourceRelPO> insertDataList = new ArrayList<>();
                for (Long resourceId : addResourceIdList) {
                    UpmAuthResourceRelPO authResourceRelPO = new UpmAuthResourceRelPO();
                    authResourceRelPO.setTenantSystemId(Objects.nonNull(tenantSystemPO) ? tenantSystemPO.getId() : null);
                    authResourceRelPO.setResourceId(resourceId);
                    authResourceRelPO.setTenantId(tenantId);
                    authResourceRelPO.setSystemId(systemId);
                    authResourceRelPO.setAuthKey(authKey);
                    insertDataList.add(authResourceRelPO);
                }
                authResourceRelRepository.saveBatch(insertDataList);
            }
        }
        //待删除资源
        if(CollUtil.isNotEmpty(authResourceRelPOS)){
            List<Long> deletedRelIdList = authResourceRelPOS.stream().map(UpmAuthResourceRelPO::getId).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(newResourceIdList)){
                deletedRelIdList = authResourceRelPOS.stream().filter(oldResource -> !newResourceIdList.contains(oldResource.getResourceId()))
                    .map(UpmAuthResourceRelPO::getId)
                    .collect(Collectors.toList());
            }
            if(CollUtil.isNotEmpty(deletedRelIdList)){
                authResourceRelMapper.deleteBatchIds(deletedRelIdList);
                upmRoleAuthResourceRelMapper.deleteByAuthResourceIds(deletedRelIdList);
            }
        }
    }

    // 单租户、系统、业务子系统资源列表
    public Map<Long, Set<Long>> getSystemResourceByAuthKey(String authKey) {
        List<UpmAuthResourceRelPO> authResourceRelPOS = upmAuthResourceRelMapper.selectByAuthKey(authKey);
        Map<Long, Set<Long>> systemResourceMap = Optional.ofNullable(authResourceRelPOS).orElse(new ArrayList<>(0))
            .stream().collect(Collectors.groupingBy(UpmAuthResourceRelPO::getSystemId,
                Collectors.mapping(UpmAuthResourceRelPO::getResourceId, Collectors.toSet())));
        return systemResourceMap;
    }

    /**
     * 权限组类型模板更新
     * @param oldTemplatePO
     * @param newResourceIds
     */
    public void refreshAuthResource(List<Long> originIdList,UpmAuthTypeTemplatePO oldTemplatePO, List<Long> newResourceIds){
        List<Long> oldResourceJson = oldTemplatePO.getResourceJson();
        Long systemId = oldTemplatePO.getSystemId();
        Long tenantId = CurrentUserHolder.getTenantId();
        //添加
        List<Long> insertIds =  newResourceIds.stream().filter(resourceId -> !oldResourceJson.contains(resourceId)).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(insertIds)){
            List<String> authKeyList = originIdList.stream().map(id->AuthKeyUtil.getAuthKey(oldTemplatePO.getAuthType(),systemId,tenantId,id)).distinct().collect(Collectors.toList());
            List<UpmAuthGroupPO> upmAuthGroupPOS = authGroupMapper.selectByAuthKey(authKeyList);
            if(CollUtil.isNotEmpty(upmAuthGroupPOS)){
                UpmTenantSystemPO tenantSystemPO = upmTenantSystemMapper.selectByTenantIdAndSystemId(upmAuthGroupPOS.get(0).getTenantId(),systemId);
                if(ObjUtil.isNull(tenantSystemPO)){
                    throw new BusinessException(MessageCode.SYSTEM_TENANT_REL_NOT_EXIST);
                }
                List<UpmAuthResourceRelPO> insertDataList = new ArrayList<>();
                for (UpmAuthGroupPO upmAuthGroupPO : upmAuthGroupPOS) {
                    String authKey = upmAuthGroupPO.getAuthKey();
                    for (Long resourceId : insertIds) {
                        UpmAuthResourceRelPO authResourceRelPO = new UpmAuthResourceRelPO();
                        authResourceRelPO.setTenantSystemId(tenantSystemPO.getId());
                        authResourceRelPO.setResourceId(resourceId);
                        authResourceRelPO.setTenantId(tenantId);
                        authResourceRelPO.setSystemId(systemId);
                        authResourceRelPO.setAuthKey(authKey);
                        insertDataList.add(authResourceRelPO);
                    }
                }
                authResourceRelRepository.saveBatch(insertDataList);
            }
        }
        //删除
        List<Long> deleteIds =  oldResourceJson.stream().filter(resourceId -> !newResourceIds.contains(resourceId)).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(deleteIds)){
            authResourceRelMapper.deleteByAuthResources(systemId,oldTemplatePO.getAuthType(),deleteIds);
        }
    }

    public List<UpmResource> selectBatchByIds(List<Long> resourceIdList) {
        if (CollectionUtils.isEmpty(resourceIdList)) {
            return Collections.emptyList();
        }
        List<UpmResource> upmResources = resourceRepository.find(resourceIdList);
        return Optional.ofNullable(upmResources).orElse(Collections.EMPTY_LIST);
    }

}
