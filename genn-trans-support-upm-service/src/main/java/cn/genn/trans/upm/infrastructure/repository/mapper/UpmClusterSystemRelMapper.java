package cn.genn.trans.upm.infrastructure.repository.mapper;


import cn.genn.trans.upm.infrastructure.repository.po.UpmClusterSystemRelPO;
import cn.genn.trans.upm.interfaces.dto.ClusterDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.genn.trans.upm.interfaces.dto.ClusterSystemDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface UpmClusterSystemRelMapper extends BaseMapper<UpmClusterSystemRelPO> {

    default List<ClusterDTO> groupAllCluster(){
        List<UpmClusterSystemRelPO> upmClusterSystemRelPOS = selectList(Wrappers.lambdaQuery(UpmClusterSystemRelPO.class).groupBy(UpmClusterSystemRelPO::getVpcGroup));
        return upmClusterSystemRelPOS.stream().map(po->new ClusterDTO(po.getVpcGroup(),po.getClusterName(),po.getDomain(),po.getInternalDomain())).collect(Collectors.toList());
    }

    default UpmClusterSystemRelPO getCluster(String vpcGroup,Long systemId){
        LambdaQueryWrapper<UpmClusterSystemRelPO> wrapper = Wrappers.lambdaQuery(UpmClusterSystemRelPO.class)
            .eq(UpmClusterSystemRelPO::getVpcGroup, vpcGroup)
            .eq(UpmClusterSystemRelPO::getClusterName, systemId);
        return selectOne(wrapper);
    }

    default List<UpmClusterSystemRelPO> selectBySystemId(Long systemId){
        return selectList(Wrappers.lambdaQuery(UpmClusterSystemRelPO.class)
            .eq(UpmClusterSystemRelPO::getSystemId,systemId)
            .groupBy(UpmClusterSystemRelPO::getVpcGroup));
    }

    ClusterSystemDTO queryByTenantIdAndSystemId(@Param("tenantId") Long tenantId, @Param("systemId") Long systemId);
}
