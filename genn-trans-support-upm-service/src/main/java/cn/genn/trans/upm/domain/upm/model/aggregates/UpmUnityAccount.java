package cn.genn.trans.upm.domain.upm.model.aggregates;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.interfaces.enums.AccountTypeEnum;
import cn.genn.trans.upm.interfaces.enums.LoginTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UpmUnityAccount implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 账号
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐
     */
    private String salt;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 关联用户
     */
    private List<UpmUser> userList;

    /**
     * 关联用户id
     */
    private List<Long> userIdList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 登录类型（phone:手机，normal:普通账号）
     */
    private LoginTypeEnum loginType;

    /**
     * 类型（default:默认账号；unity:统一登录账号）
     */
    private AccountTypeEnum type;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 逻辑删除
     */
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private Long updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;
}
