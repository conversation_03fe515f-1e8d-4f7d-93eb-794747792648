package cn.genn.trans.upm.interfaces.fsp;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.service.action.FspAuthActionService;
import cn.genn.trans.upm.application.service.query.FspAuthQueryService;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.command.fsp.ReviewAfterCommand;
import cn.genn.trans.upm.interfaces.command.fsp.SaveAfterCommand;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.dto.fsp.SuperUserDTO;
import cn.genn.trans.upm.interfaces.query.fsp.SuperUserPageQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 保理系统相关接口
 *
 * <AUTHOR>
 * @date 2024/7/26
 */
@Api(tags = "保理系统相关")
@RestController
@RequestMapping("/fsp")
public class FspAuthController {

    @Resource
    private FspAuthQueryService queryService;

    @Resource
    private FspAuthActionService actionService;

    /**
     * 超管用户分页
     */
    @PostMapping("/page")
    @ApiOperation("查询超管用户")
    public PageResultDTO<SuperUserDTO> page(@RequestBody SuperUserPageQuery query){
        query.setSystemId(CurrentUserHolder.getSystemId());
        return queryService.page(query);
    }

    /**
     * 新增组织后续
     */
    @PostMapping("/saveAfter")
    @ApiOperation("新增组织后续处理")
    public UpmUserDTO saveAfter(@RequestBody @Validated SaveAfterCommand command){
        return actionService.saveCompanyAfter(command);
    }

    /**
     * 审核组织后续
     */
    @PostMapping("/reviewAfter")
    @ApiOperation("审核组织后续")
    public Boolean reviewAfter(@RequestBody @Validated ReviewAfterCommand command){
        return actionService.reviewAfter(command);
    }

    /**
     * 删除接口
     */
    @PostMapping("/delete")
    @ApiOperation("删除")
    public Boolean delete(@RequestBody List<Long> userIdList){
        return actionService.delete(userIdList);
    }

}
