package cn.genn.trans.upm.domain.upm.repository;

import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.infrastructure.repository.po.UpmSystemPO;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.genn.trans.upm.interfaces.query.SystemQuery;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 系统repository
 *
 * <AUTHOR>
 */
public interface SystemRepository {

    /**
     * 查询
     *
     * @param systemId
     * @return
     */
    UpmSystem find(Long systemId);


    /**
     * 查询
     *
     * @param systemIdList
     * @return
     */
    List<UpmSystem> find(List<Long> systemIdList);

    /**
     * 依照系统类型查询系统及资源列表
     * @param systemTypeEnum
     * @return
     */
    List<UpmSystem> findBySystemType(SystemTypeEnum systemTypeEnum);

    /**
     * 依照系统类型查询系统及资源列表
     * @param systemTypeEnum
     * @return
     */
    Map<Long, Set<Long>> findResourceBySystemType(SystemTypeEnum systemTypeEnum);

    /**
     * 查询
     *
     * @param code
     * @return
     */
    UpmSystem find(String code);

    List<UpmSystemPO> selectList(SystemQuery query);

    /**
     * 存储
     *
     * @param system
     * @return
     */
    Long store(UpmSystem system);

    /**
     * 更新
     *
     * @param systemList
     * @return
     */
    Boolean update(List<UpmSystem> systemList);

    /**
     * 删除
     *
     * @param systemList
     * @return
     */
    Boolean delete(List<UpmSystem> systemList);

}
