package cn.genn.trans.upm.infrastructure.converter;

import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.hutool.core.bean.BeanUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class UpmResourceConverter {

    public static UpmResourcePO toPo(UpmResource resource) {
        UpmResourcePO po = new UpmResourcePO();
        BeanUtil.copyProperties(resource, po);
        po.setSystemId(resource.getUpmSystem().getId());
        return po;
    }

    public static List<UpmResourcePO> toPo(List<UpmResource> resourceList) {
        if (resourceList == null) {
            return null;
        }

        List<UpmResourcePO> list = new ArrayList<>(resourceList.size());
        for (UpmResource upmResource : resourceList) {
            list.add(toPo(upmResource));
        }

        return list;
    }

}

