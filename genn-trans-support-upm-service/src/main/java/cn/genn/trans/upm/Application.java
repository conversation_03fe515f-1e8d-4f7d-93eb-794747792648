package cn.genn.trans.upm;

import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.infrastructure.config.feishu.FeishuProperties;
import cn.genn.trans.upm.infrastructure.config.mini.WxMaProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
 * <AUTHOR>
 */
@MapperScan({"cn.genn.trans.upm.infrastructure.repository.mapper"})
@SpringBootApplication(scanBasePackages = {"cn.genn.trans.upm", "cn.genn.trans.base"})
@EnableTransactionManagement
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"cn.genn"})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableConfigurationProperties({UpmSeverProperties.class, WxMaProperties.class, FeishuProperties.class})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
