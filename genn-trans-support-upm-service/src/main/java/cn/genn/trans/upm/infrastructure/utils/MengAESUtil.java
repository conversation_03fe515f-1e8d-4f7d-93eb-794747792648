package cn.genn.trans.upm.infrastructure.utils;


import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.Security;
import java.util.Base64;
import java.util.zip.CRC32;

@Slf4j
public class MengAESUtil {

    /**
     * 加解密用的方式
     */
    public static final String CIPHERMODEPADDING = "AES/CBC/PKCS7Padding";

    static {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }
    /**
     * 获取CRC32校验值的十六进制形式,8字节
     */
    @SneakyThrows
    public static String getHexCRC32Value(String content) {
        CRC32 crc32 = new CRC32();
        log.info(content);
        crc32.update(content.getBytes(StandardCharsets.UTF_8));
        String crcString = Long.toHexString(crc32.getValue()).toUpperCase();
        if (!TextUtils.isEmpty(crcString)) {
            int length = crcString.length();
            if (length < 8) {
                StringBuilder buffer = new StringBuilder();
                for (int i = 0; i < (8 - length); i++) {
                    buffer.append("0");
                }
                buffer.append(crcString);
                return buffer.toString();
            }
        }

        return crcString;
    }

    /**
     * 加密成base64的密文
     */
    public static String encrypt(String plaintext, String key) {
        String base64_ciphertext = "";
        try {
            SecretKeySpec skforAES = new SecretKeySpec(sha256EncodeByte(key), "AES");
            IvParameterSpec IV = new IvParameterSpec(getIvParameter(0));
            byte[] plain = plaintext.getBytes();
            byte[] ciphertext = encryptBase(skforAES, IV, plain);
            if (ciphertext != null && ciphertext.length > 0) {
                base64_ciphertext = encodedByteToBase64String(ciphertext);
            }
        } catch (Exception e) {
            // Log.e(TAG, "encrypt1 key:" + key + ",error:" + e.getMessage());
            log.error(e.getMessage());
        }
        return base64_ciphertext;
    }

    private static byte[] sha256EncodeByte(String input) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            return messageDigest.digest(input.getBytes("UTF-8"));
        } catch (Exception e) {
            // Log.e(TAG, "sha256EncodeByte exception : " + e.getMessage());
            log.error(e.getMessage());
        }
        return null;
    }

    private static byte[] getIvParameter(int index) {
        byte a = 0x0;
        if (index == 1) {
            a = (byte) 0xff;
        }
        byte[] xBytes = new byte[16];
        for (int i = 0; i < 16; i++) {
            xBytes[i] = a;
        }
        return xBytes;
    }

    private static byte[] encryptBase(SecretKey sk, IvParameterSpec IV, byte[] msg) {
        try {
            Cipher c = Cipher.getInstance(CIPHERMODEPADDING);
            c.init(Cipher.ENCRYPT_MODE, sk, IV);
            return c.doFinal(msg);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 加密成base64
     *
     * @param rawStr 明文
     * @return null 当rawStr == null,  非null 加密后的字符串
     */
    public static String encodedByteToBase64String(byte[] rawStr) {
        if (rawStr == null || rawStr.length == 0) {
            return "";
        }

        try {
            String encodeString_default = new String(Base64.getEncoder().encodeToString(rawStr));
            String encodeString = encodeString_default.replace("\n", "");
            return encodeString;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return "";
    }

    /**
     * 解密
     */
    public static String decrypt(String ciphertext_base64, String key) {
        String decrypted = "";
        try {
            SecretKeySpec skforAES = new SecretKeySpec(sha256EncodeByte(key), "AES");
            IvParameterSpec IV = new IvParameterSpec(getIvParameter(0));
            byte[] s = decodedBase64ToByte(ciphertext_base64);
            decrypted = new String(decryptBase(skforAES, IV, s));
        } catch (Exception e) {
            // Log.e("EncryptUtil", "decrypt1 key:" + key + ",error:" + e.getMessage());
        }
        return decrypted;
    }

    /**
     * 解密Base64字符串
     *
     * @param encodedStr 暗文
     */
    public static byte[] decodedBase64ToByte(String encodedStr) {
        if (TextUtils.isEmpty(encodedStr)) {
            return null;
        }
        try {
            return Base64.getDecoder().decode(encodedStr);
        } catch (Exception e) {
            // LogUtil.e(TAG, "decodedBase64ToByte exception: " + e.getMessage());
        }
        return null;
    }

    private static byte[] decryptBase(SecretKey sk, IvParameterSpec IV, byte[] ciphertext) {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
        try {
            Cipher c = Cipher.getInstance(MengAESUtil.CIPHERMODEPADDING);
            c.init(Cipher.DECRYPT_MODE, sk, IV);
            return c.doFinal(ciphertext);
        } catch (Exception e) {
            // Log.e(TAG, " decrypt error:" + e.getMessage());
        }
        return null;
    }

}
