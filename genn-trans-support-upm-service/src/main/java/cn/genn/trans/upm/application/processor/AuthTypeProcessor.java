package cn.genn.trans.upm.application.processor;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAccountMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeRoleTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeTemplatePO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.command.AuthTypeChangeCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleSaveCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleUpdateCommand;
import cn.genn.trans.upm.interfaces.command.fsp.SaveAfterCommand;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Component
public class AuthTypeProcessor {

    @Resource
    private UpmAuthTypeTemplateMapper authTypeTemplateMapper;
    @Resource
    private UpmAuthTypeRoleTemplateMapper authTypeRoleTemplateMapper;
    @Resource
    private UpmAccountMapper accountMapper;
    @Resource
    private UpmRoleMapper upmRoleMapper;
    @Resource
    private UpmSeverProperties upmSeverProperties;

    public UpmAuthTypeTemplatePO checkChange(AuthTypeChangeCommand command) {
        UpmAuthTypeTemplatePO templatePO = authTypeTemplateMapper.selectById(command.getId());
        if (ObjUtil.isNull(templatePO)) {
            throw new BusinessException(MessageCode.AUTH_TYPE_NOT_EXIST);
        }
        if (!templatePO.getSystemId().equals(CurrentUserHolder.getSystemId())) {
            throw new BusinessException(MessageCode.SYSTEM_OTHER_NOT_CHANGE);
        }
        if (AuthGroupEnum.PLATFORM.equals(templatePO.getAuthType())) {
            throw new BusinessException(MessageCode.AUTH_GROUP_PL_NOT_INSET);
        }
        return templatePO;
    }

    public void checkSaveCompanyAfter(SaveAfterCommand command) {
        if (AuthGroupEnum.PLATFORM.equals(command.getAuthGroup())) {
            throw new BusinessException(MessageCode.AUTH_GROUP_PL_NOT_INSET);
        }
        // 账号校验
        UpmAccountPO upmAccountPO = accountMapper.selectByUsernameAndSystemId(command.getUsername(), CurrentUserHolder.getSystemId());
        if (ObjUtil.isNotNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.USER_EXIST);
        }
        // 手机号校验
        if (StrUtil.isNotBlank(command.getTelephone())) {
            upmAccountPO = accountMapper.selectByTelephoneAndSystemId(command.getTelephone(), CurrentUserHolder.getSystemId());
            if (ObjUtil.isNotNull(upmAccountPO)) {
                throw new BusinessException(MessageCode.PHONE_EXIST);
            }
        }
    }

    public UpmAuthTypeTemplatePO checkRoleTemplateSave(UpmAuthRoleSaveCommand command) {
        String roleCode = command.getRoleCode();
        String roleName = command.getRoleName();
        if(roleCode.equals(upmSeverProperties.getNormalRoleCode()) ||roleCode.equals(upmSeverProperties.getSuperRoleCode())){
            throw new BusinessException(MessageCode.ROLE_EXIST_ERROR);
        }
        if(roleName.equals(upmSeverProperties.getSuperRoleName())){
            throw new BusinessException(MessageCode.ROLE_EXIST_ERROR);
        }
        UpmAuthTypeTemplatePO templatePO = authTypeTemplateMapper.selectById(command.getTemplateId());
        if (ObjUtil.isNull(templatePO)) {
            throw new BusinessException(MessageCode.AUTH_TYPE_NOT_EXIST);
        }
        UpmAuthTypeRoleTemplatePO roleTemplatePO = authTypeRoleTemplateMapper.selectByCodeAndTemplateId(roleCode, command.getTemplateId());
        if (ObjUtil.isNotNull(roleTemplatePO)) {
            throw new BusinessException(MessageCode.AUTH_TYPE_ROLE_CODE_EXIST);
        }
        roleTemplatePO = authTypeRoleTemplateMapper.selectByNameAndTemplateId(roleName, command.getTemplateId());
        if (ObjUtil.isNotNull(roleTemplatePO)) {
            throw new BusinessException(MessageCode.AUTH_TYPE_ROLE_NAME_EXIST);
        }
        return templatePO;
    }

    public UpmAuthTypeRoleTemplatePO checkRoleTemplateUpdate(UpmAuthRoleUpdateCommand command) {
        UpmAuthTypeRoleTemplatePO roleTemplatePO = authTypeRoleTemplateMapper.selectById(command.getId());
        if (ObjUtil.isNull(roleTemplatePO)) {
            throw new BusinessException(MessageCode.AUTH_TYPE_ROLE_NOT_EXIST_ERROR);
        }
        if (StrUtil.isNotBlank(command.getRoleCode())) {
            String roleCode = command.getRoleCode();
            if(roleCode.equals(upmSeverProperties.getNormalRoleCode()) ||roleCode.equals(upmSeverProperties.getSuperRoleCode())){
                throw new BusinessException(MessageCode.ROLE_EXIST_ERROR);
            }
            UpmAuthTypeRoleTemplatePO po = authTypeRoleTemplateMapper.selectByCodeAndTemplateId(roleCode, roleTemplatePO.getTemplateId());
            if (ObjUtil.isNotNull(po) && !po.getId().equals(roleTemplatePO.getId())) {
                throw new BusinessException(MessageCode.AUTH_TYPE_ROLE_CODE_EXIST);
            }
        }
        if (StrUtil.isNotBlank(command.getRoleName())) {
            String roleName = command.getRoleName();
            if(roleName.equals(upmSeverProperties.getSuperRoleName())){
                throw new BusinessException(MessageCode.ROLE_EXIST_ERROR);
            }
            UpmAuthTypeRoleTemplatePO po = authTypeRoleTemplateMapper.selectByNameAndTemplateId(roleName, roleTemplatePO.getTemplateId());
            if (ObjUtil.isNotNull(po) && !po.getId().equals(roleTemplatePO.getId())) {
                throw new BusinessException(MessageCode.AUTH_TYPE_ROLE_NAME_EXIST);
            }
        }
        return roleTemplatePO;
    }


}
