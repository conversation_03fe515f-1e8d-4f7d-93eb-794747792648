package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.core.model.enums.BooleanEnum;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.interfaces.command.UpmResourceOperateCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceSaveCommand;
import cn.genn.trans.upm.interfaces.enums.DefaultSelectEnum;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusBooleanEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * UpmResource
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpmResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 资源所属系统id
     */
    private Long systemId;

    /**
     * 资源所属系统实体
     */
    private UpmSystem upmSystem;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 编号
     */
    private String code;

    /**
     * 资源类型（1: 菜单，2：按钮）
     */
    private ResourceTypeEnum type;

    /**
     * 首字母大写，一定要与vue文件的name对应起来，用于noKeepAlive缓存控制（该项特别重要）
     */
    private String name;

    /**
     * 排序
     */
    private Integer resourceSort;

    /**
     * 选中状态
     */
    private DefaultSelectEnum defaultSelect;

    /**
     * 上级菜单
     */
    private Long pid;

    /**
     * 图标
     */
    private String icon;

    /**
     * 是否显示在菜单中显示隐藏路由（默认值：false）
     */
    private BooleanEnum hidden;

    /**
     * 菜单、面包屑、多标签页显示的名称
     */
    private String title;

    /**
     * 前端path
     */
    private String path;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 前端组件路径
     */
    private String component;

    /**
     * 路由重定向
     */
    private String redirect;

    /**
     * 菜单激活（指定激活菜单的path）
     */
    private String activePath;

    /**
     * 按钮权限标识
     */
    private String auths;

    /**
     * 需要内嵌的iframe链接地址
     */
    private String frameSrc;

    /**
     * 是否在菜单中显示（默认值：true）
     */
    private StatusBooleanEnum showLink;

    /**
     * 是否显示父级菜单（默认值：true）
     */
    private StatusBooleanEnum showParent;

    /**
     * 当前路由是否不缓存（默认值：false）
     */
    private StatusBooleanEnum keepAlive;

    /**
     * 当前路由是否固定标签页（默认值：false）
     */
    private StatusBooleanEnum fixedTag;

    /**
     * 当前菜单名称或自定义信息禁止添加到标签页（默认值：false）
     */
    private StatusBooleanEnum hiddenTag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private Long updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;

    /**
     * 启用
     */
    public void enable() {
        this.status = StatusEnum.ENABLE;
    }

    /**
     * 禁用
     */
    public void disable() {
        this.status = StatusEnum.DISABLE;
    }

    /**
     * 禁用
     */
    public void doDelete() {
        this.deleted = DeletedEnum.DELETED;
    }

    /**
     * 创建资源实体
     *
     * @param command
     * @param system
     * @return
     */
    public static UpmResource createUpmResource(UpmResourceSaveCommand command, UpmSystem system) {
        UpmResource.UpmResourceBuilder builder = UpmResource.builder()
            .upmSystem(system)
            .code(command.getCode())
            .url(command.getUrl())
            .type(command.getType())
            .name(command.getName())
            .title(command.getTitle())
            .defaultSelect(command.getDefaultSelect())
            .resourceSort(command.getResourceSort())
            .pid(command.getPid())
            .icon(command.getIcon())
            .path(command.getPath())
            .status(StatusEnum.ENABLE)
            .component(command.getComponent())
            .redirect(command.getRedirect())
            .activePath(command.getActivePath())
            .auths(command.getAuths())
            .frameSrc(command.getFrameSrc())
            .showLink(command.getShowLink())
            .showParent(command.getShowParent())
            .keepAlive(command.getKeepAlive())
            .fixedTag(command.getFixedTag())
            .hiddenTag(command.getHiddenTag())
            .remark(command.getRemark())
            ;
        return builder.build();
    }

    /**
     * 更新资源实体
     *
     * @param command
     * @param system
     * @return
     */
    public static UpmResource updateUpmResource(UpmResourceOperateCommand command, UpmSystem system) {
        UpmResource.UpmResourceBuilder builder = UpmResource.builder()
            .id(command.getId())
            .upmSystem(system)
            .code(command.getCode())
            .url(command.getUrl())
            .type(command.getType())
            .name(command.getName())
            .title(command.getTitle())
            .defaultSelect(command.getDefaultSelect())
            .resourceSort(command.getResourceSort())
            .pid(command.getPid())
            .icon(command.getIcon())
            .path(command.getPath())
            .component(command.getComponent())
            .redirect(command.getRedirect())
            .activePath(command.getActivePath())
            .auths(command.getAuths())
            .frameSrc(command.getFrameSrc())
            .showLink(command.getShowLink())
            .showParent(command.getShowParent())
            .keepAlive(command.getKeepAlive())
            .fixedTag(command.getFixedTag())
            .hiddenTag(command.getHiddenTag())
            .remark(command.getRemark())
            ;
        return builder.build();
    }

    /**
     * 创建资源实体
     *
     * @param po
     * @return
     */
    public static UpmResource fromPo(UpmResourcePO po) {
        UpmResource.UpmResourceBuilder builder = UpmResource.builder()
            .id(po.getId())
            .systemId(po.getSystemId())
            .upmSystem(UpmSystem.builder().id(po.getSystemId()).build())
            .code(po.getCode())
            .url(po.getUrl())
            .type(po.getType())
            .name(po.getName())
            .resourceSort(po.getResourceSort())
            .pid(po.getPid())
            .icon(po.getIcon())
            .path(po.getPath())
            .status(po.getStatus())
            .component(po.getComponent())
            .redirect(po.getRedirect())
            .activePath(po.getActivePath())
            .auths(po.getAuths())
            .frameSrc(po.getFrameSrc())
            .showLink(po.getShowLink())
            .showParent(po.getShowParent())
            .keepAlive(po.getKeepAlive())
            .fixedTag(po.getFixedTag())
            .hiddenTag(po.getHiddenTag())
            .remark(po.getRemark())
            ;
        return builder.build();
    }

}

