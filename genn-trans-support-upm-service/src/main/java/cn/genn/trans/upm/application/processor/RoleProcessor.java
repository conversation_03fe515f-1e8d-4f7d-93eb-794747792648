package cn.genn.trans.upm.application.processor;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.command.UpmRoleSaveBySystemTypeCommand;
import cn.genn.trans.upm.interfaces.command.UpmRoleSaveCommand;
import cn.genn.trans.upm.interfaces.command.UpmRoleUpdateBySystemTypeCommand;
import cn.genn.trans.upm.interfaces.command.UpmRoleUpdateCommand;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/6
 */
@Component
public class RoleProcessor {

    @Resource
    private RoleRepository repository;
    @Resource
    private UpmSeverProperties upmSeverProperties;

    public void checkSave(UpmRoleSaveCommand command){
        List<UpmRolePO> upmRolePOS = repository.selectByNameAndAuthKey(command.getName(), CurrentUserHolder.getAuthKey());
        if(CollectionUtil.isNotEmpty(upmRolePOS)){
            throw new BusinessException(MessageCode.ROLE_EXIST_ERROR);
        }
        //角色Code校验
        if(StrUtil.isNotBlank(command.getCode()) && !upmSeverProperties.getNormalRoleCode().equals(command.getCode())){
            List<UpmRolePO> rolePOList = repository.selectByCodeAndAuthKey(command.getCode(), CurrentUserHolder.getAuthKey());
            if(CollectionUtil.isNotEmpty(rolePOList)){
                throw new BusinessException(MessageCode.ROLE_CODE_EXIST_ERROR);
            }
        }

    }

    public void checkBeforeSaveBySystemType(UpmRoleSaveBySystemTypeCommand command) {

    }

    public void checkBeforeUpdateBySystemType(UpmRoleUpdateBySystemTypeCommand command) {

    }

    public void checkChange(UpmRoleUpdateCommand command){
        UpmRolePO upmRolePO = repository.selectById(command.getId());
        if(Objects.isNull(upmRolePO)){
            throw new BusinessException(MessageCode.ROLE_NOT_EXIST_ERROR);
        }
        if(StrUtil.isNotBlank(command.getName())){
            List<UpmRolePO> upmRolePOS = repository.selectByNameAndAuthKey(command.getName(), upmRolePO.getAuthKey());
            boolean exist = upmRolePOS.stream().noneMatch(role -> role.getId().equals(upmRolePO.getId()));
            if(CollectionUtil.isNotEmpty(upmRolePOS) && exist){
                throw new BusinessException(MessageCode.ROLE_EXIST_ERROR);
            }
        }
        //角色Code校验
        if(StrUtil.isNotBlank(command.getCode()) && !upmSeverProperties.getNormalRoleCode().equals(command.getCode())){
            List<UpmRolePO> rolePOList = repository.selectByCodeAndAuthKey(command.getCode(), CurrentUserHolder.getAuthKey());
            boolean exist = rolePOList.stream().noneMatch(role -> role.getId().equals(upmRolePO.getId()));
            if(CollectionUtil.isNotEmpty(rolePOList) && exist){
                throw new BusinessException(MessageCode.ROLE_CODE_EXIST_ERROR);
            }
        }
    }
}
