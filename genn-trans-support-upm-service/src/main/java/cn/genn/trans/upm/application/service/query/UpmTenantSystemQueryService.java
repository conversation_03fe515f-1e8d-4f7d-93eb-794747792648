package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.assembler.UpmTenantSystemAssembler;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
import cn.genn.trans.upm.interfaces.dto.UpmTenantSystemDTO;
import cn.genn.trans.upm.interfaces.query.UpmTenantSystemQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmTenantSystemQueryService {

    @Resource
    private UpmTenantSystemMapper mapper;

    @Resource
    private UpmTenantSystemAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return UpmTenantSystemDTO分页对象
     */
    public PageResultDTO<UpmTenantSystemDTO> page(UpmTenantSystemQuery query) {
        UpmTenantSystemPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return UpmTenantSystemDTO
     */
    public UpmTenantSystemDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }

    /**
     * 租户id和系统id查询
     */
    public UpmTenantSystemDTO query(Long systemId, Long tenantId) {
        return assembler.PO2DTO(mapper.selectByTenantIdAndSystemId(tenantId,systemId));
    }
}

