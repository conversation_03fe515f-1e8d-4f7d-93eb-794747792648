package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.trans.upm.domain.upm.repository.AuthGroupRepository;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthGroupMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthGroupPO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Repository
public class AuthGroupRepositoryImpl extends ServiceImpl<UpmAuthGroupMapper, UpmAuthGroupPO> implements AuthGroupRepository {

    @Override
    public boolean saveBatch(List<UpmAuthGroupPO> authGroupPOList) {
        return super.saveBatch(authGroupPOList);
    }
}
