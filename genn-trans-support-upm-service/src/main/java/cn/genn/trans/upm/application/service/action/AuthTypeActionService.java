package cn.genn.trans.upm.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.application.assembler.AuthTypeAssembler;
import cn.genn.trans.upm.application.processor.AuthTypeProcessor;
import cn.genn.trans.upm.domain.upm.model.entity.AuthType;
import cn.genn.trans.upm.domain.upm.service.AuthTypeDomainService;
import cn.genn.trans.upm.domain.upm.service.ResourceDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeRoleTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeTemplatePO;
import cn.genn.trans.upm.interfaces.command.AuthTypeChangeCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleDeleteCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleSaveCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleUpdateCommand;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/26
 */
@Slf4j
@Service
public class AuthTypeActionService {

    @Resource
    private AuthTypeProcessor processor;
    @Resource
    private AuthTypeDomainService domainService;
    @Resource
    private ResourceDomainService resourceDomainService;
    @Resource
    private UpmRoleActionService upmRoleActionService;
    @Resource
    private AuthTypeAssembler assembler;
    @Resource
    private UpmAuthTypeTemplateMapper authTypeTemplateMapper;
    @Resource
    private UpmAuthTypeRoleTemplateMapper authTypeRoleTemplateMapper;


    /**
     * 维护组织菜单模板
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeResource(AuthTypeChangeCommand command) {
        UpmAuthTypeTemplatePO po = processor.checkChange(command);
        List<Long> resourceJson = command.getResourceJson().stream().distinct().collect(Collectors.toList());
        AuthType authType = new AuthType()
            .setId(command.getId())
            .setResourceJson(resourceJson);
        domainService.update(authType);
        // 刷新现有组织资源
        if (CollUtil.isEmpty(command.getCompanyIdList())) {
            return true;
        }
        resourceDomainService.refreshAuthResource(command.getCompanyIdList(), po, resourceJson);
        return true;
    }

    /**
     * 添加角色模板
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean roleTemplateSave(UpmAuthRoleSaveCommand command) {
        UpmAuthTypeTemplatePO templatePO = processor.checkRoleTemplateSave(command);
        UpmAuthTypeRoleTemplatePO po = assembler.roleSaveCommand2PO(command);
        domainService.roleTemplateSave(po);
        // 为现有组织添加角色
        if (CollUtil.isEmpty(command.getCompanyIdList())) {
            return true;
        }
        upmRoleActionService.insertAuthRoleBatch(command.getCompanyIdList(), command.getResourceJson(),po,templatePO);
        return true;
    }

    /**
     * 编辑角色模板
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean roleTemplateUpdate(UpmAuthRoleUpdateCommand command) {
        UpmAuthTypeRoleTemplatePO oldRoleTemPO = processor.checkRoleTemplateUpdate(command);
        UpmAuthTypeTemplatePO templatePO = authTypeTemplateMapper.selectById(oldRoleTemPO.getTemplateId());
        if (ObjUtil.isNull(templatePO)) {
            throw new BusinessException(MessageCode.AUTH_TYPE_NOT_EXIST);
        }
        UpmAuthTypeRoleTemplatePO newRoleTemPO = assembler.roleUpdateCommand2PO(command);
        domainService.roleTemplateUpdate(newRoleTemPO);
        // 修改现有角色
        upmRoleActionService.updateAuthRoleBatch(oldRoleTemPO, newRoleTemPO,templatePO);
        return true;
    }

    /**
     * 删除角色模板
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean roleTemplateDeletes(UpmAuthRoleDeleteCommand command) {
        // 删除组织内对应角色
        if (CollUtil.isNotEmpty(command.getCompanyIdList())) {
            UpmAuthTypeRoleTemplatePO roleTemplatePO = authTypeRoleTemplateMapper.selectById(command.getId());
            if (ObjUtil.isNull(roleTemplatePO)) {
                throw new BusinessException(MessageCode.AUTH_TYPE_ROLE_NOT_EXIST_ERROR);
            }
            UpmAuthTypeTemplatePO templatePO = authTypeTemplateMapper.selectById(roleTemplatePO.getTemplateId());
            if (ObjUtil.isNull(templatePO)) {
                throw new BusinessException(MessageCode.AUTH_TYPE_NOT_EXIST);
            }
            upmRoleActionService.deleteAuthRoleBatch(command.getCompanyIdList(), roleTemplatePO,templatePO);
        }
        domainService.roleTemplateDeletes(command.getId());
        return true;
    }
}
