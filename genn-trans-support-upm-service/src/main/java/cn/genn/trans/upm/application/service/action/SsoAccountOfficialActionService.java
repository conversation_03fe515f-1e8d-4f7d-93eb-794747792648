package cn.genn.trans.upm.application.service.action;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.lock.base.annotation.Lock;
import cn.genn.trans.upm.domain.upm.model.entity.OfficialAccountUser;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.service.UserDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.utils.SsoTokenUtil;
import cn.genn.trans.upm.interfaces.command.official.SsoAccountOfficialWXTempLoginCommand;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.official.UserOfficialInfoDTO;
import cn.genn.trans.upm.interfaces.enums.ClientTypeEnum;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 微信公众号相关操作
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SsoAccountOfficialActionService {

    private final SystemRepository systemRepository;
    private final UpmUserThirdMapper userThirdMapper;
    private final UserDomainService userDomainService;
    private final SsoTokenActionService ssoTokenActionService;


    @Transactional(rollbackFor = Exception.class)
    @Lock(fieldKey = "#command.telephone")
    public UserOfficialInfoDTO officialWxLogin(SsoAccountOfficialWXTempLoginCommand command) {
        String systemCode = command.getSystemCode();
        UpmSystem upmSystem = systemRepository.find(systemCode);
        // 注册用户流程
        OfficialAccountUser officialAccountUser = userDomainService.createOfficialUser(OfficialAccountUser.create(command, upmSystem.getId()));
        //自动登录
        return loginOfficialUser(officialAccountUser, upmSystem);
    }

    private UserOfficialInfoDTO loginOfficialUser(OfficialAccountUser officialAccountUser, UpmSystem upmSystem) {
        // token生成
        UserOfficialInfoDTO officialInfoDTO = this.generateUserToken(officialAccountUser);
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, officialAccountUser, officialInfoDTO);
        userAuthInfoDTO.setToken(officialInfoDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(officialInfoDTO.getToken(), userAuthInfoDTO);
        return officialInfoDTO;
    }

    private UserOfficialInfoDTO generateUserToken(OfficialAccountUser officialAccountUser) {
        String userDeviceIdentify = SsoTokenUtil.generateUserDeviceIdentify(officialAccountUser.getSystemCode());
        SaLoginModel saLoginModel = ssoTokenActionService.buildOfficialSaLoginModel(officialAccountUser.getSystemCode());
        StpUtil.login(SsoTokenUtil.generateUserLoginId(officialAccountUser.getAccountId(), officialAccountUser.getUserId()), saLoginModel);
        String token = StpUtil.getTokenValue();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(MessageCode.USER_GENERATE_TOKEN_FAIL);
        }
        UserOfficialInfoDTO userOfficialInfoDTO = new UserOfficialInfoDTO();
        userOfficialInfoDTO.setUserId(officialAccountUser.getUserId());
        userOfficialInfoDTO.setToken(token);
        userOfficialInfoDTO.setTelephone(officialAccountUser.getTelephone());
        userOfficialInfoDTO.setUserDeviceIdentify(userDeviceIdentify);
        return userOfficialInfoDTO;
    }

    private LoginUserAuthInfoDTO generateTokenSessionToken(UpmSystem upmSystem, OfficialAccountUser officialAccountUser, UserOfficialInfoDTO officialInfoDTO) {
        return LoginUserAuthInfoDTO.builder()
            .clientType(ClientTypeEnum.OFFICIAL_ACCOUNT)
            .accountId(officialAccountUser.getAccountId())
            .userId(officialAccountUser.getUserId())
            .userDeviceIdentify(officialInfoDTO.getUserDeviceIdentify())
            .authKey(officialAccountUser.getAuthKey())
            .tenantId(officialAccountUser.getTenantId())
            .systemId(upmSystem.getId())
            .systemType(upmSystem.getType())
            .systemCode(upmSystem.getCode())
            .systemName(upmSystem.getName())
            .telephone(officialAccountUser.getTelephone())
            .username(officialAccountUser.getUsername())
            .unionId(officialAccountUser.getUnionId())
            .appId(officialAccountUser.getAppId())
            .openId(officialAccountUser.getOpenId())
            .build();
    }
}
