package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.trans.upm.domain.upm.repository.TenantSystemRepository;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/13
 */
@Repository
public class TenantSystemRepositoryImpl extends ServiceImpl<UpmTenantSystemMapper, UpmTenantSystemPO> implements TenantSystemRepository {


    @Override
    public boolean deleteTenantId(Long tenantId) {
        LambdaQueryWrapper<UpmTenantSystemPO> wrapper = Wrappers.lambdaQuery(UpmTenantSystemPO.class)
            .eq(UpmTenantSystemPO::getTenantId, tenantId);
        baseMapper.delete(wrapper);
        return true;
    }
}
