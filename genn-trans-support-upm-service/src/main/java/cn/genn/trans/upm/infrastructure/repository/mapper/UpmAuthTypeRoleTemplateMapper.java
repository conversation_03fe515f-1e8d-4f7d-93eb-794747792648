package cn.genn.trans.upm.infrastructure.repository.mapper;


import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmAuthTypeRoleTemplateMapper extends BaseMapper<UpmAuthTypeRoleTemplatePO> {


    default UpmAuthTypeRoleTemplatePO selectByCodeAndTemplateId(String roleCode, Long templateId) {
        return selectOne(new LambdaQueryWrapper<>(UpmAuthTypeRoleTemplatePO.class)
            .eq(UpmAuthTypeRoleTemplatePO::getRoleCode, roleCode)
            .eq(UpmAuthTypeRoleTemplatePO::getTemplateId, templateId));
    }

    default UpmAuthTypeRoleTemplatePO selectByNameAndTemplateId(String roleName, Long templateId) {
        return selectOne(new LambdaQueryWrapper<>(UpmAuthTypeRoleTemplatePO.class)
            .eq(UpmAuthTypeRoleTemplatePO::getRoleName, roleName)
            .eq(UpmAuthTypeRoleTemplatePO::getTemplateId, templateId));
    }

    default List<UpmAuthTypeRoleTemplatePO> selectByTemplateId(Long templateId) {
        return selectList(new LambdaQueryWrapper<>(UpmAuthTypeRoleTemplatePO.class)
            .in(UpmAuthTypeRoleTemplatePO::getTemplateId, templateId));
    }

    List<UpmAuthTypeRoleTemplatePO> selectByAuthTypeAndSystemId(@Param("authGroup") AuthGroupEnum authGroup,@Param("systemId") Long systemId);


}
