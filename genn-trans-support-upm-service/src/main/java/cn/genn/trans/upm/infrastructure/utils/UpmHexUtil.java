package cn.genn.trans.upm.infrastructure.utils;

/**
 * <AUTHOR>
 */
public class UpmHexUtil {
    private static final char[] HEX_CHARS = "0123456789abcdef".toCharArray();

    public static String encodeHex(byte[] bytes) {
        StringBuilder buf = new StringBuilder(bytes.length * 2);
        byte[] var2 = bytes;
        int var3 = bytes.length;

        for (int var4 = 0; var4 < var3; ++var4) {
            byte b = var2[var4];
            buf.append(HEX_CHARS[b >>> 4 & 15]).append(HEX_CHARS[b & 15]);
        }

        return buf.toString();
    }

    public static byte[] decodeHex(String data) {
        if ((data.length() & 1) == 1) {
            throw new RuntimeException("不是合法的16进制数据！" + data);
        } else {
            data = data.toUpperCase();
            byte[] bytes = new byte[data.length() >>> 1];

            int b;
            for (int i = 0; i < data.length(); bytes[i >>> 1] = (byte) (b & 15)) {
                b = 0;

                for (int j = 0; j < 2; ++j) {
                    char c1 = data.charAt(i + j);
                    if (c1 >= '0' && c1 <= '9') {
                        b = b << 4 & c1 - 48;
                    } else {
                        if (c1 < 'A' || c1 > 'F') {
                            throw new RuntimeException("不是合法的16进制数据！" + data);
                        }

                        b = b << 4 & c1 - 65 + 10;
                    }

                    ++i;
                }
            }

            return bytes;
        }
    }
}
