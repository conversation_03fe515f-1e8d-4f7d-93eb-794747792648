package cn.genn.trans.upm.infrastructure.utils;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 提供统一的sso单点登录相关的工具
 *
 * @Date: 2024/4/18
 * @Author: kangjian
 */
@Slf4j
public class SsoTokenUtil {


    public static final String ACCOUNT_PREFIX = "account";
    public static final String USER_PREFIX = "user";
    public static final String SEPARATOR = "_";
    public static final String DEFAULT_PRODUCT_CODE = "WEB";
    public static final String LOGIN_USER_AUTH_INFO_KEY = "loginUserAuthInfo";

    /**
     * 生成用户设备标识 默认web端
     *
     * @return
     */
    public static String generateUserDeviceIdentify() {
        return generateUserDeviceIdentify(DEFAULT_PRODUCT_CODE);
    }

    /**
     * 生成用户设备标识
     *
     * @param systemCode
     * @return
     */
    public static String generateUserDeviceIdentify(String systemCode) {
        if (StrUtil.isBlank(systemCode)) {
            systemCode = DEFAULT_PRODUCT_CODE;
        }
        return Joiner.on(SEPARATOR).join(systemCode, IdUtil.fastUUID());
    }

    /**
     * 生成账号token登录id
     *
     * @param accountId
     * @param userDeviceIdentify
     * @return
     */
    public static String generateAccountLoginId(String accountId, String userDeviceIdentify) {
        return Joiner.on(SEPARATOR).join(ACCOUNT_PREFIX, accountId, userDeviceIdentify);
    }

    /**
     * 生成用户token登录id
     *
     * @param accountId
     * @param userId
     * @return
     */
    public static String generateUserLoginId(Long accountId, Long userId) {
        return Joiner.on(SEPARATOR).join(ACCOUNT_PREFIX, USER_PREFIX, accountId, userId);
    }

    /**
     * 从loginId获取账号id
     *
     * @param loginId
     * @return
     */
    public static Long getAccountIdByLoginId(String loginId) {
        return Long.parseLong(Splitter.on(SEPARATOR).splitToList(loginId).get(1));
    }


    public static Long getTokenTimeout() {
        return getTokenTimeout(DEFAULT_PRODUCT_CODE);
    }

    public static Long getTokenActiveTimeOut() {
        return getTokenActiveTimeOut(DEFAULT_PRODUCT_CODE);
    }

    /**
     * token整体的过期时间7天
     *
     * @param systemCode
     * @return
     */
    public static Long getTokenTimeout(String systemCode) {
        return 7L * 24 * 60 * 60;
    }

    /**
     * 小程序整体的过期时间
     *
     * @param systemCode
     * @return
     */
    public static Long getMiniTokenTimeout(String systemCode) {
        return 30L * 24 * 60 * 60;
    }

    /**
     * 公众号整体的过期时间
     * @param systemCode
     * @return
     */
    public static Long getOfficialTokenTimeout(String systemCode) {
        //10小时
        return 10L * 60 * 60;
    }

    /**
     * 会话活跃时间3天
     *
     * @param systemCode
     * @return
     */
    public static Long getTokenActiveTimeOut(String systemCode) {
        return 3L * 24 * 60 * 60;
    }

    public static LoginUserAuthInfoDTO getLoginUserAuthInfoFormToken(String tokenValue) {
        SaSession saSession = StpUtil.getStpLogic().getTokenSessionByToken(tokenValue, false);
        log.info("用token获取session result={}", JsonUtils.toJson(saSession));
        if (saSession == null) {
            return null;
        }
        Object authInfo = saSession.getDataMap().get(LOGIN_USER_AUTH_INFO_KEY);
        if (Objects.isNull(authInfo)) {
            return null;
        }
        String loginUserAuthInfoJson = String.valueOf(authInfo);
        if (StrUtil.isBlank(loginUserAuthInfoJson)) {
            return null;
        }
        LoginUserAuthInfoDTO loginUserAuthInfoDTO = JsonUtils.parse(loginUserAuthInfoJson, LoginUserAuthInfoDTO.class);
        return loginUserAuthInfoDTO;
    }

    public static void setLoginUserAuthInfo(String tokenValue, LoginUserAuthInfoDTO loginUserAuthInfoDTO) {
        SaSession saSession = StpUtil.getStpLogic().getTokenSessionByToken(tokenValue, true);
        if (null == saSession) {
            return;
        }
        saSession.set(LOGIN_USER_AUTH_INFO_KEY, JsonUtils.toJson(loginUserAuthInfoDTO));
    }
}
