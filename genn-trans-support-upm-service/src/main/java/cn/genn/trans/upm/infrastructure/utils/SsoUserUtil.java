package cn.genn.trans.upm.infrastructure.utils;

import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * sso用户信息相关工具类
 *
 * @Date: 2024/4/18
 * @Author: kangjian
 */
@Slf4j
public class SsoUserUtil {
    public static final String LOGIN_USER_AUTH_INFO_KEY = "loginUserAuthInfo";

    /**
     * 从token中获取secretKey
     *
     * @return
     */
    public static String getsecretKeyFromToken(String tokenValue) {
        SsoUserAuthInfoDTO loginUserAuthInfoDTO = getLoginUserAuthInfoFormToken(tokenValue);
        if (Objects.isNull(loginUserAuthInfoDTO)) {
            return null;
        }
        return loginUserAuthInfoDTO.getSecretKey();
    }


    /**
     * 从token中获取缓存的用户相关信息
     *
     * @return
     */
    public static SsoUserAuthInfoDTO getLoginUserAuthInfoFormToken(String tokenValue) {
        if (StrUtil.isBlank(tokenValue)) {
            return null;
        }
        StpUtil.getStpLogic().updateLastActiveToNow(tokenValue);
        SaSession saSession = StpUtil.getStpLogic().getTokenSessionByToken(tokenValue, false);
        if (Objects.isNull(saSession)) {
            return null;
        }

        String loginUserAuthInfoJson = (String) saSession.getDataMap().get(LOGIN_USER_AUTH_INFO_KEY);

        if (StrUtil.isBlank(loginUserAuthInfoJson)) {
            return null;
        }

        SsoUserAuthInfoDTO loginUserAuthInfoDTO = null;

        try {
            loginUserAuthInfoDTO = JsonUtils.parse(loginUserAuthInfoJson, SsoUserAuthInfoDTO.class);
        } catch (Exception e) {
            log.error("getLoginUserAuthInfoFormToken error", e);
        }
        return loginUserAuthInfoDTO;
    }

    /**
     * 更新缓存的用户相关信息
     *
     * @return
     */
    public static void setLoginUserAuthInfo(String tokenValue, SsoUserAuthInfoDTO ssoUserAuthInfoDTO) {
        SaSession saSession = StpUtil.getStpLogic().getTokenSessionByToken(tokenValue, false);
        if (null == saSession) {
            return;
        }
        StpUtil.getTokenSessionByToken(tokenValue).set(LOGIN_USER_AUTH_INFO_KEY, JsonUtils.toJson(ssoUserAuthInfoDTO));
    }

    /**
     * 为token续签
     *
     * @param tokenValue
     */
    public static void updateLastActiveToNow(String tokenValue) {
        StpUtil.getStpLogic().updateLastActiveToNow(tokenValue);
    }

    /**
     * 查询token是否已经是冻结状态
     *
     * @param tokenValue
     */
    public static boolean isTokenFrozenStatus(String tokenValue) {
        /**
         * 获取指定 token 剩余活跃有效期：这个 token 距离被冻结还剩多少时间（单位: 秒，返回 -1 代表永不冻结，-2 代表没有这个值或 token 已被冻结了）
         */
        long tokenActiveTimeout = StpUtil.getStpLogic().getTokenActiveTimeoutByToken(tokenValue);
        if (SaTokenDao.NOT_VALUE_EXPIRE == tokenActiveTimeout) {
            return true;
        }
        if (SaTokenDao.NEVER_EXPIRE == tokenActiveTimeout) {
            return false;
        }
        return tokenActiveTimeout < 0;
    }
}
