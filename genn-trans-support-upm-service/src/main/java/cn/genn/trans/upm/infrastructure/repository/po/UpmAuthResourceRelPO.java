package cn.genn.trans.upm.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmAuthResourceRelPO对象
 *
 * <AUTHOR>
 * @desc 租户系统资源关联表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_auth_resource_rel", autoResultMap = true)
public class UpmAuthResourceRelPO {

    /**
     * 主键自增
     */
    @TableId
    private Long id;

    /**
     * 权限组key
     */
    @TableField("auth_key")
    private String authKey;

    /**
     * 资源id
     */
    @TableField("resource_id")
    private Long resourceId;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long tenantId;

    /**
     * (冗余)租户系统关联表id
     */
    @TableField("tenant_system_id")
    private Long tenantSystemId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

