package cn.genn.trans.upm.application.dto;

import cn.genn.spring.boot.starter.event.spring.model.SpringBaseEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * upm变更用户 角色 系统 发送消息体
 * 触发casbin规则重新加载
 *
 * @Date: 2024/4/26
 * @Author: ka<PERSON><PERSON><PERSON>
 */
@Getter
@Setter
public class UpmAuthChangeEvent extends SpringBaseEvent {

    /**
     * 按什么维度进行更新 系统 租户 用户 角色 资源
     */
    private UpmAuthChangeEventDimensionEnum dimension;
    private Long systemId;
    private Long tenantId;
    private Long userId;
    private Long roleId;
    private Long resourceId;

    public UpmAuthChangeEvent(Object source, UpmAuthChangeEventDimensionEnum dimension, Long value) {
        super(source);
        this.dimension = dimension;
        switch (dimension) {
            case SYSTEM:
                this.systemId = value;
                break;
            case TENANT:
                this.tenantId = value;
                break;
            case USER:
                this.userId = value;
                break;
            case ROLE:
                this.roleId = value;
                break;
            case RESOURCE:
                this.resourceId = value;
            default:
        }
    }
}
