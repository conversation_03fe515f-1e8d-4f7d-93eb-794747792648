package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmUserRolePO对象
 *
 * <AUTHOR>
 * @desc 用户角色关联表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_user_role", autoResultMap = true)
public class UpmUserRolePO {

    /**
     * 主键自增
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 角色id
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

