package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmAccountMapper extends BaseMapper<UpmAccountPO> {

    default UpmAccountPO selectByUsernameAndSystemId(String username, Long systemId) {
        LambdaQueryWrapper<UpmAccountPO> wrapper = Wrappers.lambdaQuery(UpmAccountPO.class)
            .eq(UpmAccountPO::getUsername, username)
            .eq(UpmAccountPO::getSystemId, systemId);
        return selectOne(wrapper);
    }

    default UpmAccountPO selectByTelephoneAndSystemId(String telephone, Long systemId) {
        LambdaQueryWrapper<UpmAccountPO> wrapper = Wrappers.lambdaQuery(UpmAccountPO.class)
            .eq(UpmAccountPO::getTelephone, telephone)
            .eq(UpmAccountPO::getSystemId, systemId);
        return selectOne(wrapper);
    }

    default List<UpmAccountPO> selectByUsernameAndSystemIds(String username, List<Long> systemId) {
        LambdaQueryWrapper<UpmAccountPO> wrapper = Wrappers.lambdaQuery(UpmAccountPO.class)
            .eq(UpmAccountPO::getUsername, username)
            .in(UpmAccountPO::getSystemId, systemId);
        return selectList(wrapper);
    }

    UpmAccountPO selectByOpenIdAndAppId(@Param("openId") String openId, @Param("appId") String appId, @Param("systemId") Long systemId);
}
