package cn.genn.trans.upm.infrastructure.utils;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 图形验证码
 *
 * <AUTHOR>
 * @date 2024/7/13
 */
@Slf4j
@Component
public class CaptchaUtil {

    private static CaptchaService captchaService;

    @Autowired
    public void setCaptchaService(CaptchaService captchaService) {
        CaptchaUtil.captchaService = captchaService;
    }
    /**
     * 提供滑动拼图
     *
     * @param data
     * @return
     */
    public static CaptchaVO getCaptcha(CaptchaVO data) {
        return (CaptchaVO) captchaService.get(data).getRepData();
    }

    /**
     * 验证码校验
     *
     * @param data
     * @return
     */
    public static CaptchaVO checkCaptcha(CaptchaVO data) {
        ResponseModel model = captchaService.check(data);
        if (model.isSuccess()) {
            return (CaptchaVO) model.getRepData();
        }
        log.error("响应：{}", JsonUtils.toJson( model));
        log.error("验证码校验失败,code:{}, reason:{}", model.getRepCode(),model.getRepMsg());
        throw new BusinessException(MessageCode.USER_CAPTCHA_FAIL, model.getRepMsg());
    }

    /**
     * 验证码二次验证
     *
     * @param verificationCode
     * @return
     */
    public static Boolean verification(String verificationCode) {
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(verificationCode);
        ResponseModel verification = captchaService.verification(captchaVO);
        return verification.isSuccess();
    }
}
