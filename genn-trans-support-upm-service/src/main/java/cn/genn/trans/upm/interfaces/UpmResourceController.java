package cn.genn.trans.upm.interfaces;

import cn.genn.core.exception.CheckException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.application.service.action.UpmResourceActionService;
import cn.genn.trans.upm.application.service.query.UpmResourceQueryService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.interfaces.command.UpmChangeStatusCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceOperateCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceSaveCommand;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.dto.UpmResourceTreeDTO;
import cn.genn.trans.upm.interfaces.query.UpmResourceBySystemTypeQuery;
import cn.genn.trans.upm.interfaces.query.UpmResourceQuery;
import cn.genn.trans.upm.interfaces.query.UpmResourceTreeQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资源管理
 * <AUTHOR>
 */
@Api(tags = "资源管理")
@RestController
@RequestMapping("/upmResource")
public class UpmResourceController {

    @Resource
    private UpmResourceQueryService queryService;
    @Resource
    private UpmResourceActionService actionService;
    @Resource
    private UpmSeverProperties upmSeverProperties;


    /**
     * 查询用户权限组范围内的所有资源,用于对应系统的角色管理
     * @return
     */
    @PostMapping("/queryListByAuthKey")
    @ApiOperation(value = "查询用户权限组范围内的所有资源")
    public List<UpmResourceDTO> queryListByAuthKey(){
        return queryService.queryListByAuthKey();
    }

    @PostMapping("/queryListBySystemType")
    @ApiOperation(value = "依照系统类型 查询用户权限组范围内的所有资源")
    public List<UpmResourceDTO> queryListBySystemType(@ApiParam(value = "查询类") @Validated @RequestBody UpmResourceBySystemTypeQuery query){
        return queryService.queryListBySystemType(query);
    }

    @PostMapping("/queryListByRoleIds")
    @ApiOperation(value = "依照角色列表查询资源列表")
    public List<UpmResourceDTO> queryListByRoleIds(@ApiParam(value = "查询类") @RequestBody List<Long> roleIds) {
        return queryService.queryListByRoleIds(roleIds);
    }

    /**
     * 系统，租户，角色列表，资源类型查询资源列表,分三类查询
     * type: 默认查所有资源,类型包括:菜单,按钮等
     * systemId: 查询系统下所有资源
     * roleIdList:查询角色列表下的资源
     */
    @Deprecated
    @PostMapping("/queryList")
    @ApiOperation(value = "系统，角色列表，资源类型查询资源列表")
    public List<UpmResourceDTO> queryList(@ApiParam(value = "查询类") @Validated @RequestBody UpmResourceQuery query) {
        return queryService.queryList(query);
    }

    @PostMapping("/queryNoChild")
    @ApiOperation(value = "查询没有下级的资源")
    public List<Long> queryNoChild(@RequestBody List<Long> idList){
        return queryService.queryNoChild(idList);
    }

    /**
     * 查询资源树
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/treeList")
    @ApiOperation(value = "查询列表-树")
    public List<UpmResourceTreeDTO> treeList(@ApiParam(value = "查询类") @Validated @RequestBody UpmResourceTreeQuery query) {
        return queryService.treeList(query);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public UpmResourceDTO get(@ApiParam(name = "id", required = true) @Validated @RequestParam Long id) {
        return queryService.get(id);
    }

    /**
     * 添加资源
     *
     * @param command
     * @return Long
     */
    @PostMapping("/save")
    @ApiOperation(value = "添加资源")
    public Boolean save(@ApiParam(value = "资源操作类") @Validated @RequestBody UpmResourceSaveCommand command) {
        return actionService.save(command);
    }

    /**
     * 修改资源
     *
     * @param command
     * @return Boolean
     */
    @PostMapping("/change")
    @ApiOperation(value = "修改资源")
    public Boolean change(@ApiParam(value = "资源操作类") @Validated @RequestBody UpmResourceOperateCommand command) {
        if(upmSeverProperties.getDisableEditResources().contains(command.getId())){
            throw new CheckException(MessageCode.RESOURCE_STOP_EDIT);
        }
        actionService.change(command);
        return true;
    }

    /**
     * 批量启用停用
     *
     * @param command
     * @return
     */
    @PostMapping("/change/status")
    @ApiOperation(value = "批量启用停用")
    public Boolean changeStatus(@ApiParam(value = "批量启用禁用") @RequestBody @Validated UpmChangeStatusCommand command) {
        if(upmSeverProperties.getDisableEditResources().stream().anyMatch(id ->command.getIdList().contains(id))){
            throw new CheckException(MessageCode.RESOURCE_STOP_EDIT);
        }
        return actionService.changeStatus(command);
    }


    /**
     * 删除资源
     *
     * @param resourceIdList
     * @return Boolean
     */
    @PostMapping("/remove")
    @ApiOperation(value = "删除资源")
    public Boolean remove(@ApiParam(name = "resourceIdList", required = true) @Validated @RequestBody List<Long> resourceIdList) {
        if(upmSeverProperties.getDisableEditResources().stream().anyMatch(resourceIdList::contains)){
            throw new CheckException(MessageCode.RESOURCE_STOP_EDIT);
        }
        actionService.remove(resourceIdList);
        return true;
    }

}

