package cn.genn.trans.upm.domain.upm.repository;

import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.infrastructure.dto.RoleUserRelDTO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleUserDTO;
import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;

import java.util.List;

/**
 * 角色Repository
 *
 * <AUTHOR>
 */
public interface RoleRepository {

    /**
     * 查询所有角色及资源列表
     *
     * @return
     */
    List<UpmRole> queryAllRoleAndResource();

    /**
     * id查询角色
     */
    UpmRolePO selectById(Long id);

    UpmRole findById(Long id);

    List<UpmRolePO> selectBatchIds(List<Long> roleIds);

    /**
     * name重复校验
     */
    List<UpmRolePO> selectByNameAndAuthKey(String name, String authKey);

    /**
     * code重复校验
     */
    List<UpmRolePO> selectByCodeAndAuthKey(String code, String authKey);

    /**
     * 过滤系统角色
     */
    List<UpmRolePO> selectSystemRoleList(List<Long> idList);

    /**
     * 修改角色
     */
    Long update(UpmRole upmRole);

    /**
     * 批量删除
     * @param idList
     * @return
     */
    Long batchDelete(List<Long> idList);

    /**
     * 批量修改状态
     */
    Long updateStatus(List<Long> idList, StatusEnum status);

    /**
     * 用户ids查询启用关联角色
     *
     * @param userIdList
     * @return
     */
    List<UpmRoleUserDTO> queryByUserIds(List<Long> userIdList);


    boolean deleteByTenantId(Long tenantId);

    boolean deleteByTenantIdAndSystemIds(Long tenantId, List<Long> systemIds);

    List<UpmRolePO> recoverTenantIdAndSystemIds(Long tenantId, List<Long> systemIds);

    List<UpmRolePO> selectByTenantAndSystem(Long systemId, Long tenantId, RoleTypeEnum roleType);


    List<RoleUserRelDTO> getAllUserAndRole();

    List<UpmRole> saveBatchEntity(List<UpmRole> roleList);

    Boolean updateBatchEntity(List<UpmRole> roleList);
}
