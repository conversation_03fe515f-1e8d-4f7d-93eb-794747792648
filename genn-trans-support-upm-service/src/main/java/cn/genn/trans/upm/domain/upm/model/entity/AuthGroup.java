package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Data
@Accessors(chain = true)
public class AuthGroup {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "权限组key,根据定义决定业务含义")
    private String authKey;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "类型：pl平台,op运营,ca承运")
    private AuthGroupEnum type;

    @ApiModelProperty(value = "删除：0未删除，1删除")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

}
