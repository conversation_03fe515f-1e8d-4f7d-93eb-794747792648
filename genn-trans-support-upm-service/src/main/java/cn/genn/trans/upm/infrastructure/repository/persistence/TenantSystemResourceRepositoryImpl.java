// package cn.genn.trans.upm.infrastructure.repository.persistence;
//
// import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
// import cn.genn.trans.upm.domain.upm.repository.TenantSystemResourceRepository;
// import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
// import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemResourceMapper;
// import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
// import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemResourcePO;
// import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
// import cn.hutool.core.util.ObjUtil;
// import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
// import com.baomidou.mybatisplus.core.toolkit.Wrappers;
// import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
// import org.springframework.stereotype.Repository;
//
// import javax.annotation.Resource;
// import java.util.List;
//
// @Repository
// public class TenantSystemResourceRepositoryImpl extends ServiceImpl<UpmTenantSystemResourceMapper, UpmTenantSystemResourcePO> implements TenantSystemResourceRepository {
//
//
//     @Resource
//     private UpmTenantSystemMapper upmTenantSystemMapper;
//
//     /**
//      * 查询关联角色
//      *
//      * @param resourceIdList
//      * @param tenantId
//      */
//     @Override
//     public List<UpmTenantSystemResourcePO> selectByResourceIds(List<Long> resourceIdList, Long tenantId) {
//         LambdaQueryWrapper<UpmTenantSystemResourcePO> wrapper = Wrappers.lambdaQuery(UpmTenantSystemResourcePO.class)
//             .in(UpmTenantSystemResourcePO::getResourceId,resourceIdList)
//             .eq(UpmTenantSystemResourcePO::getTenantId,tenantId);
//         return baseMapper.selectList(wrapper);
//     }
//
//
//     @Override
//     public boolean deleteTenantId(Long tenantId) {
//         LambdaQueryWrapper<UpmTenantSystemResourcePO> wrapper = Wrappers.lambdaQuery(UpmTenantSystemResourcePO.class)
//             .eq(UpmTenantSystemResourcePO::getTenantId, tenantId);
//         baseMapper.delete(wrapper);
//         return true;
//     }
//
//     @Override
//     public boolean insertTenantSystemResource(UpmSystem upmSystem, Long resourceId) {
//         Long tenantId = CurrentUserHolder.getTenantId();
//         Long systemId = upmSystem.getId();
//         UpmTenantSystemPO upmTenantSystemPO = upmTenantSystemMapper.selectByTenantIdAndSystemId(tenantId, systemId);
//         if (ObjUtil.isNull(upmTenantSystemPO)) {
//             UpmTenantSystemPO insertPO = new UpmTenantSystemPO()
//                 .setSystemId(systemId)
//                 .setTenantId(tenantId);
//             upmTenantSystemMapper.insert(insertPO);
//             upmTenantSystemPO = insertPO;
//         }
//         UpmTenantSystemResourcePO tenantSystemResourcePO = new UpmTenantSystemResourcePO();
//         tenantSystemResourcePO.setTenantSystemId(upmTenantSystemPO.getId());
//         tenantSystemResourcePO.setSystemId(systemId);
//         tenantSystemResourcePO.setResourceId(resourceId);
//         tenantSystemResourcePO.setTenantId(tenantId);
//         baseMapper.insert(tenantSystemResourcePO);
//         return true;
//     }
// }
