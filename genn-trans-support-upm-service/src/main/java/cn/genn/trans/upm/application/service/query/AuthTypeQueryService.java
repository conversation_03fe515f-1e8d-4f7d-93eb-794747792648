package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.assembler.AuthTypeAssembler;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeRoleTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthTypeTemplateMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.dto.UpmAuthTypeRoleTemplateDTO;
import cn.genn.trans.upm.interfaces.dto.UpmAuthTypeTemplateDTO;
import cn.genn.trans.upm.interfaces.query.AuthRolePageQuery;
import cn.genn.trans.upm.interfaces.query.AuthTypeQuery;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/26
 */
@Slf4j
@Service
public class AuthTypeQueryService {

    @Resource
    private UpmAuthTypeTemplateMapper upmAuthTypeTemplateMapper;
    @Resource
    private AuthTypeAssembler assembler;
    @Resource
    private UpmAuthTypeRoleTemplateMapper upmAuthTypeRoleTemplateMapper;

    public List<UpmAuthTypeTemplateDTO> query(AuthTypeQuery query) {
        Long systemId = CurrentUserHolder.getSystemId();
        return assembler.PO2DTO(upmAuthTypeTemplateMapper.query(query,systemId));
    }

    /**
     * 分页查询角色模板
     * @param query
     * @return
     */
    public PageResultDTO<UpmAuthTypeRoleTemplateDTO> page(AuthRolePageQuery query) {
        UpmAuthTypeRoleTemplatePO roleTemplatePO = new UpmAuthTypeRoleTemplatePO().setTemplateId(query.getTemplateId()).setResourceJson(null);
        return assembler.rolePO2DTO(upmAuthTypeRoleTemplateMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(roleTemplatePO)));
    }
}
