package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.application.dto.UserAccountDTO;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import cn.genn.trans.upm.interfaces.dto.fsp.SuperUserDTO;
import cn.genn.trans.upm.interfaces.query.UpmUserPageQuery;
import cn.genn.trans.upm.interfaces.query.UpmUserQuery;
import cn.genn.trans.upm.interfaces.query.UpmUserRolePageQuery;
import cn.genn.trans.upm.interfaces.query.UserRoleQuery;
import cn.genn.trans.upm.interfaces.query.fsp.SuperUserPageQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmUserMapper extends BaseMapper<UpmUserPO> {

    UpmUser findUserRoleResource(Long userId);

    List<UpmUser> findAllUserAndRole();

    List<UpmUserPO> selectByUsernameAndSystemId(@Param("username") String username,@Param("systemId") Long systemId);

    List<UpmUserPO> selectByTelephoneAndSystemId(@Param("telephone") String telephone,@Param("systemId") Long systemId);

    default UpmUserPO selectByAccountIdAndTenantId(Long accountId,Long tenantId){
        return selectOne(Wrappers.lambdaQuery(UpmUserPO.class)
            .eq(UpmUserPO::getAccountId,accountId).eq(UpmUserPO::getTenantId,tenantId));
    }
    default UpmUserPO selectByAccountIdAndSystemId(Long accountId,Long systemId){
        return selectOne(Wrappers.lambdaQuery(UpmUserPO.class)
            .eq(UpmUserPO::getAccountId,accountId)
            .eq(UpmUserPO::getSystemId,systemId));
    }

    List<UserAccountDTO> selectUserList(List<Long> idList);

    List<UpmUserPO> selectByAccountIdAndVpcGroup(@Param("accountId")Long accountId,@Param("vpcGroup")String vpcGroup);

    Page<UpmUserDTO> selectByPage(Page<UpmUserDTO> page, @Param("query") UpmUserPageQuery query,@Param("authKey")String authKey);

    Page<UpmUserDTO> selectUserRoleByPage(Page<UpmUserDTO> page, @Param("query") UpmUserRolePageQuery query, @Param("authKey")String authKey);

    List<UpmUserDTO> selectByQuery(@Param("query") UpmUserQuery query);

    List<UpmUserDTO> queryUserByUsernamesAndAuthKey(@Param("list") List<String> usernameList,@Param("authKey") String authKey,@Param("systemId") Long systemId);

    List<UpmUserDTO> searchName(@Param("search") String search,@Param("authKey") String authKey,@Param("systemId") Long systemId);

    List<UpmUserDTO> queryByRoleCode(@Param("query") UserRoleQuery query);

    UpmUserDTO selectByUserId(Long id);

    int saveBatch(List<UpmUserPO> list);

    UserWxInfoDTO selectByPhone(@Param("telephone") String telephone, @Param("systemId") Long systemId);

    UserWxInfoDTO selectByUsername(@Param("username") String username, @Param("systemId") Long systemId);

    default UpmUserPO selectByAccountIdAndAuthKey(Long accountId,String authKey){
        LambdaQueryWrapper<UpmUserPO> wrapper = Wrappers.lambdaQuery(UpmUserPO.class)
            .eq(UpmUserPO::getAccountId, accountId)
            .eq(UpmUserPO::getAuthKey, authKey);
        return selectOne(wrapper);
    }

    default List<UpmUserPO> selectByAccountId(Long accountId){
        LambdaQueryWrapper<UpmUserPO> wrapper = Wrappers.lambdaQuery(UpmUserPO.class)
            .eq(UpmUserPO::getAccountId, accountId);
        return selectList(wrapper);
    }

    default List<UpmUserPO> selectByAccountIds(List<Long> accountIds){
        LambdaQueryWrapper<UpmUserPO> wrapper = Wrappers.lambdaQuery(UpmUserPO.class)
            .in(UpmUserPO::getAccountId, accountIds);
        return selectList(wrapper);
    }

    Page<SuperUserDTO> selectSuperPage(Page<SuperUserDTO> page, @Param("query") SuperUserPageQuery query);

    default List<UpmUserPO> selectBySystemIds(List<Long> systemIds){
        LambdaQueryWrapper<UpmUserPO> wrapper = Wrappers.lambdaQuery(UpmUserPO.class)
            .in(UpmUserPO::getSystemId, systemIds);
        return selectList(wrapper);
    }
}
