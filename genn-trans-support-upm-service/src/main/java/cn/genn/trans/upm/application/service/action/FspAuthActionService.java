package cn.genn.trans.upm.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.application.assembler.UpmUserAssembler;
import cn.genn.trans.upm.application.processor.AuthTypeProcessor;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.service.ResourceDomainService;
import cn.genn.trans.upm.domain.upm.service.RoleDomainService;
import cn.genn.trans.upm.domain.upm.service.UserDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.*;
import cn.genn.trans.upm.infrastructure.repository.po.*;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.command.fsp.ReviewAfterCommand;
import cn.genn.trans.upm.interfaces.command.fsp.SaveAfterCommand;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Service
public class FspAuthActionService {

    @Resource
    private UpmAuthGroupMapper authGroupMapper;
    @Resource
    private AuthTypeProcessor authTypeProcessor;
    @Resource
    private ResourceDomainService resourceDomainService;
    @Resource
    private UpmAuthTypeTemplateMapper authTypeTemplateMapper;
    @Resource
    private UserDomainService userDomainService;
    @Resource
    private RoleDomainService roleDomainService;
    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private UpmUserAssembler userAssembler;
    @Resource
    private UpmAccountMapper upmAccountMapper;
    @Resource
    private UpmSeverProperties upmSeverProperties;
    @Resource
    private UpmAuthTypeRoleTemplateMapper authTypeRoleTemplateMapper;

    /**
     * 新增组织后续处理
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public UpmUserDTO saveCompanyAfter(SaveAfterCommand command) {
        authTypeProcessor.checkSaveCompanyAfter(command);
        Long systemId = CurrentUserHolder.getSystemId();
        Long tenantId = CurrentUserHolder.getTenantId();
        String authKey = AuthKeyUtil.getAuthKey(command.getAuthGroup(), systemId, tenantId, command.getCompanyId());
        // 添加权限组表
        UpmAuthGroupPO authGroupPO = new UpmAuthGroupPO()
            .setAuthKey(authKey)
            .setSystemId(systemId)
            .setTenantId(tenantId)
            .setType(command.getAuthGroup());
        authGroupMapper.insert(authGroupPO);
        // 权限组和资源关联,这里超管也只给基础信息维护的权限
        resourceDomainService.saveAuthResourceRel(upmSeverProperties.getFspDefaultSuperAdminResources(), authKey, systemId, tenantId);
        // 创建超管角色
        List<Long> systemIdList = Collections.singletonList(systemId);
        List<UpmRolePO> upmRolePOList = roleDomainService.createSuperRole(tenantId, systemIdList, command.getCompanyId(), command.getAuthGroup());
        // 超管账号创建
        List<UpmUserPO> superUser = userDomainService.createSuperUser(systemIdList, upmRolePOList, command.getUsername(), command.getTelephone(), command.getNick());
        return userAssembler.PO2DTO(superUser.get(0));
    }

    /**
     * 审核组织后续
     *
     * @param command
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean reviewAfter(ReviewAfterCommand command) {
        UpmUserPO upmUserPO = upmUserMapper.selectById(command.getUserId());
        if(ObjUtil.isNull(upmUserPO)){
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        String authKey = upmUserPO.getAuthKey();
        Long systemId = CurrentUserHolder.getSystemId();
        Long tenantId = CurrentUserHolder.getTenantId();

        //修改超管角色菜单
        UpmAuthTypeTemplatePO templatePO = authTypeTemplateMapper.selectBySystemIdAndType(systemId, AuthKeyUtil.getAuthGroupEnum(authKey));
        if (ObjUtil.isNull(templatePO)) {
            throw new BusinessException(MessageCode.AUTH_TYPE_RESOURCE_NOT_EXIST);
        }
        resourceDomainService.saveAuthResourceRel(templatePO.getResourceJson(), authKey, systemId, tenantId);
        //添加初始化角色
        List<UpmAuthTypeRoleTemplatePO> roleTemplatePOS =  authTypeRoleTemplateMapper.selectByTemplateId(templatePO.getId());
        if(CollUtil.isNotEmpty(roleTemplatePOS)){
            this.fspSaveBatch(roleTemplatePOS,authKey);
        }
        return true;
    }

    /**
     * 删除
     * 实际仅仅删除了账号和用户,保证无法登录而已
     * @param userIdList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(List<Long> userIdList){
        List<UpmUserPO> upmUserPOList = upmUserMapper.selectBatchIds(userIdList);
        upmUserMapper.deleteBatchIds(userIdList);
        if(CollUtil.isNotEmpty(upmUserPOList)){
            List<Long> accountIdList = upmUserPOList.stream().map(UpmUserPO::getAccountId).distinct().collect(Collectors.toList());
            upmAccountMapper.deleteBatchIds(accountIdList);
        }
        return true;
    }

    private Boolean fspSaveBatch(List<UpmAuthTypeRoleTemplatePO> roleList, String authKey) {
        for (UpmAuthTypeRoleTemplatePO role : roleList) {
            UpmRole upmRole = new UpmRole();
            upmRole.setAuthKey(authKey);
            upmRole.setType(RoleTypeEnum.TENANT);
            upmRole.setSystemId(CurrentUserHolder.getSystemId());
            upmRole.setTenantId(CurrentUserHolder.getTenantId());
            upmRole.setRemark(role.getRemark());
            upmRole.setCode(role.getRoleCode());
            upmRole.setName(role.getRoleName());
            upmRole.setResourceIdList(role.getResourceJson());
            roleDomainService.fspSave(upmRole);
        }
        return true;
    }
}
