package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.assembler.UpmTenantAssembler;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmClusterSystemRelMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmClusterSystemRelPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.dto.UpmTenantDTO;
import cn.genn.trans.upm.interfaces.query.UpmTenantQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmTenantQueryService {

    @Resource
    private UpmTenantMapper mapper;
    @Resource
    private UpmTenantAssembler assembler;
    @Resource
    private UpmClusterSystemRelMapper clusterSystemRelMapper;

    public List<UpmTenantDTO> listAll() {
        List<UpmTenantPO> upmTenantPOS = mapper.selectList(null);
        if (CollUtil.isEmpty(upmTenantPOS)) {
            return Collections.emptyList();
        }
        return assembler.PO2DTO(upmTenantPOS);
    }

    public List<UpmTenantDTO> list() {
        List<UpmTenantPO> upmTenantPOS = mapper.selectList(null);
        List<UpmClusterSystemRelPO> upmClusterSystemRelPOS = clusterSystemRelMapper.selectBySystemId(CurrentUserHolder.getSystemId());
        Map<String, UpmClusterSystemRelPO> relPOMap = upmClusterSystemRelPOS.stream().collect(Collectors.toMap(UpmClusterSystemRelPO::getVpcGroup, Function.identity()));

        List<UpmTenantDTO> resultList = new ArrayList<>();
        for (UpmTenantPO upmTenantPO : upmTenantPOS) {
            UpmTenantDTO tenantDTO = assembler.PO2DTO(upmTenantPO);
            UpmClusterSystemRelPO po = relPOMap.get(upmTenantPO.getVpcGroup());
            if(ObjUtil.isNotNull(po)){
                tenantDTO.setDomain(po.getDomain());
                tenantDTO.setInternalDomain(po.getInternalDomain());
            }
            resultList.add(tenantDTO);
        }
        return resultList;
    }

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return UpmTenantDTO分页对象
     */
    public PageResultDTO<UpmTenantDTO> page(UpmTenantQuery query) {
        return assembler.toPageResult(mapper.selectByPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
    }

    /**
     * 系统id查询租户列表
     *
     * @param query
     * @return
     */
    public List<UpmTenantDTO> selectByList(UpmTenantQuery query) {
        return assembler.PO2DTO(mapper.selectByList(query));
    }


    /**
     * 查询租户信息
     *
     * @param idList
     * @return
     */
    public List<UpmTenantDTO> getByIdList(List<Long> idList) {
        return assembler.PO2DTO(mapper.selectBatchIds(idList));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return UpmTenantDTO
     */
    public UpmTenantDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }

}

