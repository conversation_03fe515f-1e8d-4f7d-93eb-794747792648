package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.AuthResourceRelRepository;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthResourceRelPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemPO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Repository
public class AuthResourceRelRepositoryImpl  extends ServiceImpl<UpmAuthResourceRelMapper, UpmAuthResourceRelPO>  implements AuthResourceRelRepository {

    @Resource
    private UpmTenantSystemMapper upmTenantSystemMapper;

    @Override
    public boolean saveBatch(List<UpmAuthResourceRelPO> list) {
        return super.saveBatch(list);
    }

    /**
     * 查询关联角色
     *
     * @param resourceIdList
     * @param tenantId
     */
    @Override
    public List<UpmAuthResourceRelPO> selectByResourceIds(List<Long> resourceIdList, Long tenantId) {
        LambdaQueryWrapper<UpmAuthResourceRelPO> wrapper = Wrappers.lambdaQuery(UpmAuthResourceRelPO.class)
            .in(UpmAuthResourceRelPO::getResourceId,resourceIdList)
            .eq(UpmAuthResourceRelPO::getTenantId,tenantId);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 查询关联角色
     *
     * @param resourceIdList
     * @param authKey
     */
    @Override
    public List<UpmAuthResourceRelPO> selectByResourceIdsAndAuthKey(List<Long> resourceIdList, String authKey) {
        LambdaQueryWrapper<UpmAuthResourceRelPO> wrapper = Wrappers.lambdaQuery(UpmAuthResourceRelPO.class)
            .in(UpmAuthResourceRelPO::getResourceId,resourceIdList)
            .eq(UpmAuthResourceRelPO::getAuthKey,authKey);
        return baseMapper.selectList(wrapper);
    }


    @Override
    public boolean deleteTenantId(Long tenantId) {
        LambdaQueryWrapper<UpmAuthResourceRelPO> wrapper = Wrappers.lambdaQuery(UpmAuthResourceRelPO.class)
            .eq(UpmAuthResourceRelPO::getTenantId, tenantId);
        baseMapper.delete(wrapper);
        return true;
    }

    @Override
    public boolean insertAuthResourceRel(UpmSystem upmSystem, Long resourceId) {
        Long tenantId = CurrentUserHolder.getTenantId();
        Long systemId = upmSystem.getId();
        UpmTenantSystemPO upmTenantSystemPO = upmTenantSystemMapper.selectByTenantIdAndSystemId(tenantId, systemId);
        if (ObjUtil.isNull(upmTenantSystemPO)) {
            UpmTenantSystemPO insertPO = new UpmTenantSystemPO()
                .setSystemId(systemId)
                .setTenantId(tenantId);
            upmTenantSystemMapper.insert(insertPO);
            upmTenantSystemPO = insertPO;
        }
        UpmAuthResourceRelPO authResourceRelPO = new UpmAuthResourceRelPO();
        authResourceRelPO.setSystemId(systemId);
        authResourceRelPO.setResourceId(resourceId);
        authResourceRelPO.setTenantId(tenantId);
        authResourceRelPO.setAuthKey(AuthKeyUtil.getPlatformKey(systemId,tenantId));
        baseMapper.insert(authResourceRelPO);
        return true;
    }
}
