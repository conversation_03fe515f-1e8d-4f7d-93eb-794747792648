package cn.genn.trans.upm.application.service.query;

import cn.genn.trans.upm.application.assembler.ClusterSystemRelAssembler;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmClusterSystemRelMapper;
import cn.genn.trans.upm.interfaces.dto.ClusterDTO;
import cn.genn.trans.upm.interfaces.dto.UpmClusterSystemRelDTO;
import cn.genn.trans.upm.interfaces.dto.ClusterSystemDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/15
 */
@Service
public class ClusterQueryService {

    @Resource
    private UpmClusterSystemRelMapper upmClusterSystemRelMapper;
    @Resource
    private ClusterSystemRelAssembler clusterSystemRelAssembler;

    public List<ClusterDTO> queryAllClusterList(){
        return upmClusterSystemRelMapper.groupAllCluster();
    }

    public ClusterSystemDTO queryByTenantIdAndSystemId(Long tenantId, Long systemId){
        return upmClusterSystemRelMapper.queryByTenantIdAndSystemId(tenantId,systemId);
    }

    public UpmClusterSystemRelDTO getCluster(String vpcGroup, Long systemId){
        return clusterSystemRelAssembler.PO2DTO(upmClusterSystemRelMapper.getCluster(vpcGroup,systemId));
    }
}
