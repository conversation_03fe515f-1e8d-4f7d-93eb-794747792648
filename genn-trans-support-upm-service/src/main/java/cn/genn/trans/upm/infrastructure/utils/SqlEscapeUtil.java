package cn.genn.trans.upm.infrastructure.utils;

/**
 * sql查询前特殊字符转义工具
 *
 * <AUTHOR>
 * @date 2024/5/22
 */
public class SqlEscapeUtil {

    /**
     * 对sql中使用的特殊字符转义,包括单引号,双引号,反斜杠,百分号,下划线
     * @param str
     * @return
     */
    public static String escape(String str){
        if (str == null) {
            return null;
        }
        return str.replace("'", "\\'")   // 单引号
            .replace("\"", "\\\"")  // 双引号
            .replace("\\", "\\\\") // 反斜杠
            .replace("%", "\\%")    // 百分号
            .replace("_", "\\_");
    }
}
