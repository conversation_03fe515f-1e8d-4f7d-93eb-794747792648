package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.DTOAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.interfaces.command.yzg.YzgPermissionSyncCmd;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.dto.UpmResourceTreeDTO;
import cn.genn.trans.upm.interfaces.enums.DefaultSelectEnum;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusBooleanEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import org.apache.logging.log4j.util.Strings;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmResourceReturnAssembler extends DTOAssembler<UpmResource, UpmResourceDTO> {

    @Override
    default List<UpmResourceDTO> entity2DTO(List<UpmResource> poList) {
        List<UpmResourceDTO> resultList = new ArrayList<>(poList.size());
        sortForPO(poList);
        for (UpmResource po : poList) {
            if (po.getPid() == null || po.getPid().equals(0L)) {
                UpmResourceDTO dto = BeanUtil.copyProperties(po, UpmResourceDTO.class);
                dto.setBaseCode(po.getCode());
                resultList.add(this.buildResourceDTO(dto, poList));
            }
        }
        sortListForDTOList(resultList);
        return resultList;
    }

    default UpmResourceDTO buildResourceDTO(UpmResourceDTO dto, List<UpmResource> allList) {
        List<UpmResourceDTO> children = new ArrayList<>();
        for (UpmResource po : allList) {
            if (po.getPid().equals(dto.getId())) {
                UpmResourceDTO childrenDTO = BeanUtil.copyProperties(po, UpmResourceDTO.class);
                children.add(this.buildResourceDTO(childrenDTO, allList));
            }
        }
        dto.setChildren(children);
        return dto;
    }

    static List<UpmResourceTreeDTO> sortForTreeDTO(List<UpmResourceTreeDTO> dtoList) {
        Collections.sort(dtoList, (o1, o2) -> {
            if ((o1.getMeta().getResourceSort() > o2.getMeta().getResourceSort())) {
                return 1;
            }
            if (o1.getMeta().getResourceSort().equals(o2.getMeta().getResourceSort())) {
                if (o1.getId() != null && o1.getId().compareTo(o2.getId()) == 1) {
                    return -1;
                } else {
                    return 1;
                }
            }
            return -1;
        });
        return dtoList;
    }

    static void sortListForDTOList(List<UpmResourceDTO> dtoList) {
        for (UpmResourceDTO sysMenuDTO : dtoList) {
            sortForDTO(sysMenuDTO);
        }
    }

    static void sortForDTO(UpmResourceDTO sysMenuDTO) {
        List<UpmResourceDTO> children = sysMenuDTO.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        sortForDTO(children);
        for (UpmResourceDTO child : children) {
            sortForDTO(child);
        }
    }

    // 升序排列ForDTO
    static List<UpmResourceDTO> sortForDTO(List<UpmResourceDTO> dtoList) {
        Collections.sort(dtoList, (o1, o2) -> {
            if ((o1.getResourceSort() > o2.getResourceSort())) {
                return 1;
            }
            if (o1.getResourceSort().equals(o2.getResourceSort())) {
                if (o1.getId() != null && o1.getId().compareTo(o2.getId()) == 1) {
                    return -1;
                } else {
                    return 1;
                }
            }
            return -1;
        });
        return dtoList;
    }

    // 升序排列ForPO
    static List<UpmResource> sortForPO(List<UpmResource> poList) {
        Collections.sort(poList, (o1, o2) -> {
            if ((o1.getResourceSort() > o2.getResourceSort())) {
                return 1;
            }
            if (o1.getResourceSort().equals(o2.getResourceSort())) {
                if (o1.getId() != null && o1.getId().compareTo(o2.getId()) == 1) {
                    return -1;
                } else {
                    return 1;
                }
            }
            return -1;
        });
        return poList;
    }

    default UpmResource convertToMenu(Long systemId, Integer groupId, String groupName) {
        UpmResource resource = new UpmResource();
        resource.setSystemId(systemId);
        resource.setType(ResourceTypeEnum.MENU);
        resource.setTitle(groupName);
        resource.setName(groupName);
        resource.setResourceSort(1);
        resource.setUrl(Strings.EMPTY);
        resource.setPath("/#");
        resource.setCode("menu_"+groupId);
        resource.setPid(0L);
        resource.setIcon(Strings.EMPTY);
        resource.setAuths(Strings.EMPTY);
        resource.setComponent(Strings.EMPTY);
        resource.setRedirect(Strings.EMPTY);
        resource.setDefaultSelect(DefaultSelectEnum.FALSE);
        resource.setActivePath(Strings.EMPTY);
        resource.setFrameSrc(Strings.EMPTY);
        resource.setShowLink(StatusBooleanEnum.TRUE);
        resource.setShowParent(StatusBooleanEnum.TRUE);
        resource.setKeepAlive(StatusBooleanEnum.FALSE);
        resource.setFixedTag(StatusBooleanEnum.FALSE);
        resource.setHiddenTag(StatusBooleanEnum.FALSE);
        resource.setRemark("同步");
        return resource;
    }


    default UpmResource convertToButton(Long systemId, YzgPermissionSyncCmd.YzgPermissionItem permission, Long pid) {
        UpmResource resource = new UpmResource();
        resource.setSystemId(systemId);
        resource.setType(ResourceTypeEnum.BUTTON);
        resource.setTitle(permission.getName());
        resource.setName(permission.getName());
        resource.setResourceSort(Integer.valueOf(permission.getSort()));
        resource.setUrl(permission.getUrl());
        resource.setPath(Strings.EMPTY);
        resource.setCode(permission.getEnName());
        resource.setPid(pid);
        resource.setIcon(Strings.EMPTY);
        resource.setAuths(permission.getEnName());
        resource.setComponent(Strings.EMPTY);
        resource.setRedirect(Strings.EMPTY);
        resource.setDefaultSelect(DefaultSelectEnum.FALSE);
        resource.setActivePath(Strings.EMPTY);
        resource.setFrameSrc(Strings.EMPTY);
        resource.setShowLink(StatusBooleanEnum.TRUE);
        resource.setShowParent(StatusBooleanEnum.TRUE);
        resource.setKeepAlive(StatusBooleanEnum.FALSE);
        resource.setFixedTag(StatusBooleanEnum.FALSE);
        resource.setHiddenTag(StatusBooleanEnum.FALSE);
        resource.setRemark("同步");
        return resource;
    }

}

