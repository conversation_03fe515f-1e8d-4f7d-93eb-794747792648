package cn.genn.trans.upm.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.service.action.AuthTypeActionService;
import cn.genn.trans.upm.application.service.query.AuthTypeQueryService;
import cn.genn.trans.upm.interfaces.command.AuthTypeChangeCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleDeleteCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleSaveCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleUpdateCommand;
import cn.genn.trans.upm.interfaces.dto.UpmAuthTypeRoleTemplateDTO;
import cn.genn.trans.upm.interfaces.dto.UpmAuthTypeTemplateDTO;
import cn.genn.trans.upm.interfaces.query.AuthRolePageQuery;
import cn.genn.trans.upm.interfaces.query.AuthTypeQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权限组类型
 *
 * <AUTHOR>
 * @date 2024/7/26
 */
@Api(tags = "权限组类型管理")
@RestController
@RequestMapping("/authType")
public class AuthTypeController {

    @Resource
    private AuthTypeActionService actionService;
    @Resource
    private AuthTypeQueryService queryService;

    /**
     * 查询当前系统权限组类型
     */
    @PostMapping("/query")
    @ApiOperation("查询当前系统权限组类型")
    public List<UpmAuthTypeTemplateDTO> query(@RequestBody AuthTypeQuery query) {
        return queryService.query(query);
    }

    /**
     * 维护组织菜单模板
     */
    @PostMapping("/changeResource")
    @ApiOperation("维护组织菜单模板")
    public Boolean changeResource(@RequestBody @Validated AuthTypeChangeCommand command){
        return actionService.changeResource(command);
    }

    /**
     * 分页查询角色模板
     *
     * @param query
     * @return
     */
    @PostMapping("/roleTemplatePage")
    @ApiOperation(value = "分页查询角色")
    public PageResultDTO<UpmAuthTypeRoleTemplateDTO> roleTemplatePage(@ApiParam(value = "查询类") @RequestBody @Validated AuthRolePageQuery query) {
        return queryService.page(query);
    }

    /**
     * 添加角色模板
     */
    @PostMapping("/roleTemplateSave")
    @ApiOperation(value = "添加角色模板")
    public Boolean roleTemplateSave(@ApiParam(value = "角色") @RequestBody @Validated UpmAuthRoleSaveCommand command) {
        return actionService.roleTemplateSave(command);
    }

    /**
     * 编辑角色模板
     */
    @PostMapping("/roleTemplateUpdate")
    @ApiOperation(value = "编辑角色模板")
    public Boolean roleTemplateUpdate(@ApiParam(value = "角色") @RequestBody @Validated UpmAuthRoleUpdateCommand command) {
        return actionService.roleTemplateUpdate(command);
    }

    /**
     * 删除角色模板
     */
    @PostMapping("/roleTemplateDeletes")
    @ApiOperation(value = "删除角色模板")
    public Boolean roleTemplateDeletes(@ApiParam(value = "删除角色模板") @RequestBody @Validated UpmAuthRoleDeleteCommand command) {
        return actionService.roleTemplateDeletes(command);
    }

}
