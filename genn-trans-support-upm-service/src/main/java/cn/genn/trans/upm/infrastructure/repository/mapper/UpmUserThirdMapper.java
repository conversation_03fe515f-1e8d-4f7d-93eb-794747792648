package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmUserThirdPO;
import cn.genn.trans.upm.interfaces.dto.UpmUserThirdDTO;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.ThridTypeEnum;
import cn.genn.trans.upm.interfaces.query.UserThirdQuery;
import cn.genn.trans.upm.interfaces.query.UserThirdSystemQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmUserThirdMapper extends BaseMapper<UpmUserThirdPO> {

    UserWxInfoDTO selectByPhoneAndOpenId(@Param("telephone") String telephone,
                                         @Param("openId") String openId,
                                         @Param("systemId") Long systemId,
                                         @Param("appId") String appId);

    UserWxInfoDTO selectByUsernameAndOpenId(@Param("username") String username,
                                            @Param("openId") String openId,
                                            @Param("systemId") Long systemId,
                                            @Param("appId") String appId);

    List<UpmUserThirdDTO> getThirdByUserIdsAndSystemType(UserThirdSystemQuery query);

    default List<UpmUserThirdPO> selectByAppIdAndUserIds(ThridTypeEnum thirdType, String appId, List<Long> userIdList) {
        LambdaQueryWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaQuery(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getAppId, appId)
            .eq(UpmUserThirdPO::getType, thirdType)
            .in(UpmUserThirdPO::getUserId, userIdList)
            .eq(UpmUserThirdPO::getStatus, StatusEnum.ENABLE.getCode());
        return selectList(wrapper);
    }

    default List<UpmUserThirdPO> selectByUserIdAndType(Long userId, ThridTypeEnum thirdType) {
        LambdaQueryWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaQuery(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getUserId, userId)
            .eq(UpmUserThirdPO::getType, thirdType);
        return selectList(wrapper);
    }

    default List<UpmUserThirdPO> selectByUserIdAndAppId(Long userId, String appId) {
        LambdaQueryWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaQuery(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getUserId, userId)
            .eq(UpmUserThirdPO::getAppId, appId);
        return selectList(wrapper);
    }

    default void closeStatusByUserIdAndAppId(Long userId, String appId) {
        LambdaUpdateWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaUpdate(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getUserId, userId)
            .eq(UpmUserThirdPO::getAppId, appId)
            .set(UpmUserThirdPO::getStatus, StatusEnum.DISABLE);
        update(wrapper);
    }

    default void openByOpenId(String openId, Long userId) {
        LambdaUpdateWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaUpdate(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getOpenId, openId)
            .eq(UpmUserThirdPO::getUserId, userId)
            .set(UpmUserThirdPO::getStatus, StatusEnum.ENABLE);
        update(wrapper);
    }

    default UpmUserThirdPO selectFSByUserId(Long userId) {
        LambdaQueryWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaQuery(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getUserId, userId)
            .eq(UpmUserThirdPO::getType, ThridTypeEnum.FS)
            .eq(UpmUserThirdPO::getStatus, StatusEnum.ENABLE.getCode());
        return selectOne(wrapper, false);
    }

    default void openByAppIdAndUserId(String appId, Long userId) {
        LambdaUpdateWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaUpdate(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getAppId, appId)
            .eq(UpmUserThirdPO::getUserId, userId)
            .set(UpmUserThirdPO::getStatus, StatusEnum.ENABLE);
        update(wrapper);
    }

    default UpmUserThirdPO createFSThirdInfo(Long userId, String appId, String openId, String extraInfo) {
        UpmUserThirdPO userThirdPO = new UpmUserThirdPO();
        userThirdPO.setType(ThridTypeEnum.FS);
        userThirdPO.setUserId(userId);
        userThirdPO.setOpenId(openId);
        userThirdPO.setAppId(appId);
        userThirdPO.setExtraInfo(extraInfo);
        insert(userThirdPO);
        return userThirdPO;
    }

    default UpmUserThirdPO queryByFsUser(UserThirdQuery query){
        LambdaQueryWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaQuery(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getOpenId, query.getOpenId())
            .eq(UpmUserThirdPO::getAppId, query.getAppId())
            .eq(UpmUserThirdPO::getType,ThridTypeEnum.FS)
            .eq(UpmUserThirdPO::getStatus, StatusEnum.ENABLE.getCode());
        return selectOne(wrapper, false);
    }

    default void closeStatusByUserIdsAndAppId(List<Long> userIdList,String appId){
        LambdaUpdateWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaUpdate(UpmUserThirdPO.class)
            .in(UpmUserThirdPO::getUserId, userIdList)
            .eq(UpmUserThirdPO::getAppId, appId)
            .set(UpmUserThirdPO::getStatus,StatusEnum.DISABLE);
        update(wrapper);
    }


    default void updateExtraInfoById(Long id, String extraInfo) {
        LambdaUpdateWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaUpdate(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getId, id)
            .set(UpmUserThirdPO::getExtraInfo, extraInfo);
        update(wrapper);
    }

    default List<UpmUserThirdPO> selectByAppId(String appId, ThridTypeEnum type) {
        LambdaQueryWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaQuery(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getAppId, appId)
            .eq(UpmUserThirdPO::getType, type);
        return selectList(wrapper);
    }

    default List<UpmUserThirdPO> selectEnableUser(List<Long> userIdList,ThridTypeEnum  type){
        LambdaQueryWrapper<UpmUserThirdPO> wrapper = Wrappers.lambdaQuery(UpmUserThirdPO.class)
            .in(UpmUserThirdPO::getUserId, userIdList)
            .eq(UpmUserThirdPO::getType, type)
            .eq(UpmUserThirdPO::getStatus, StatusEnum.ENABLE);
            return selectList(wrapper);
    }
}
