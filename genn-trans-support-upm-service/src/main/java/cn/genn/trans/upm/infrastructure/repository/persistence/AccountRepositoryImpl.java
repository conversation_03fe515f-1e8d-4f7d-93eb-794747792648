package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.domain.upm.repository.AccountRepository;
import cn.genn.trans.upm.infrastructure.converter.UpmAccountConverter;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAccountMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import cn.genn.trans.upm.interfaces.enums.PasswrodStatusEnum;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/13
 */
@Repository
public class AccountRepositoryImpl extends ServiceImpl<UpmAccountMapper, UpmAccountPO> implements AccountRepository {

    @Resource
    private UpmAccountConverter converter;

    @Override
    public Long createAccount(UpmAccount account) {
        UpmAccountPO upmAccountPO = converter.entity2PO(account);
        baseMapper.insert(upmAccountPO);
        return upmAccountPO.getId();
    }

    @Override
    public List<UpmAccountPO> saveBatch(List<UpmAccountPO> list) {
        super.saveBatch(list);
        return list;
    }

    @Override
    public Boolean updatePassword(UpmAccount account) {
        LambdaUpdateWrapper<UpmAccountPO> wrapper = Wrappers.lambdaUpdate(UpmAccountPO.class)
            .eq(UpmAccountPO::getId, account.getId())
            .set(UpmAccountPO::getPassword, account.getPassword())
            .set(UpmAccountPO::getSalt, account.getSalt())
            .set(UpmAccountPO::getPasswordStatus, PasswrodStatusEnum.NORMAL)
            .set(UpmAccountPO::getPdUpdateTime, LocalDateTime.now());
        return update(wrapper);
    }


}
