package cn.genn.trans.upm.application.feign;

import cn.genn.trans.pms.interfaces.api.IOperatorInfoFeignService;
import cn.genn.trans.pms.interfaces.dto.PmsOperatorDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class OperatorInfoClient {

    @Resource
    private IOperatorInfoFeignService operatorInfoFeignService;

    public PmsOperatorDTO getByTenantId(Long tenantId){
        return operatorInfoFeignService.getByTenantId(tenantId);
    }
}
