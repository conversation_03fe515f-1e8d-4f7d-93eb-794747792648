package cn.genn.trans.upm.interfaces;

import cn.genn.trans.upm.application.service.query.ClusterQueryService;
import cn.genn.trans.upm.interfaces.dto.ClusterDTO;
import cn.genn.trans.upm.interfaces.dto.ClusterSystemDTO;
import cn.genn.trans.upm.interfaces.dto.UpmClusterSystemRelDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户集群相关
 *
 * <AUTHOR>
 * @date 2024/7/15
 */
@Api(tags = "租户集群相关")
@RestController
@RequestMapping("/cluster")
public class ClusterController {

    @Resource
    private ClusterQueryService clusterQueryService;

    @GetMapping("/allClusterList")
    @ApiOperation("获取集群列表")
    public List<ClusterDTO> queryAllClusterList() {
        return clusterQueryService.queryAllClusterList();
    }

    @GetMapping("/queryByTenantIdAndSystemId")
    @ApiOperation("租户id和系统id查指定集群")
    public ClusterSystemDTO queryByTenantIdAndSystemId(@RequestParam("tenantId") Long tenantId, @RequestParam("systemId") Long systemId){
        return clusterQueryService.queryByTenantIdAndSystemId(tenantId,systemId);
    }

    @GetMapping("/getCluster")
    @ApiOperation("查询单个集群")
    public UpmClusterSystemRelDTO getCluster(@RequestParam("vpcGroup") String vpcGroup, @RequestParam("systemId") Long systemId) {
        return clusterQueryService.getCluster(vpcGroup, systemId);
    }


}
