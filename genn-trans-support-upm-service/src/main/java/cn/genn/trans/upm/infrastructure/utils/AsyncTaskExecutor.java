package cn.genn.trans.upm.infrastructure.utils;

import cn.hutool.core.thread.NamedThreadFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 自定义线程池,将不同任务归类
 * <AUTHOR>
 */
public class AsyncTaskExecutor {

    public final static ExecutorService demoExecutor;

    static {
        demoExecutor = new ThreadPoolExecutor(
                3,
                3,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(Integer.MAX_VALUE),
                new NamedThreadFactory("demo-task", false));
    }

    /**
     * @param task
     */
    public static void demoTask(Runnable task) {
        demoExecutor.execute(task);
    }


}
