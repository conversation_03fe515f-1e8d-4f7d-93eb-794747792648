package cn.genn.trans.upm.application.service.action;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.pms.interfaces.dto.PmsDriverDTO;
import cn.genn.trans.upm.application.feign.DriverInfoClient;
import cn.genn.trans.upm.application.feign.MengSmsClient;
import cn.genn.trans.upm.application.service.query.WxMiniQueryService;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.SystemRepository;
import cn.genn.trans.upm.domain.upm.service.UserDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.utils.LoginFailUtil;
import cn.genn.trans.upm.infrastructure.utils.SmsUtil;
import cn.genn.trans.upm.infrastructure.utils.SsoTokenUtil;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.UserAccountInfoDTO;
import cn.genn.trans.upm.interfaces.dto.mini.MiniLoginUserDTO;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import cn.genn.trans.upm.interfaces.enums.ClientTypeEnum;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppLoginQuery;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppSmsLoginQuery;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppUsernameQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class SsoAccountAppActionService {

    @Resource
    private WxMiniQueryService wxMiniQueryService;
    @Resource
    private UpmUserThirdMapper userThirdMapper;
    @Resource
    private SsoAccountActionService ssoAccountActionService;
    @Resource
    private SystemRepository systemRepository;
    @Autowired
    private UserDomainService userDomainService;
    @Resource
    private SsoTokenActionService ssoTokenActionService;
    @Resource
    private DriverInfoClient driverInfoClient;
    @Resource
    private MengSmsClient mengSmsClient;
    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private WxMinActionService wxMinActionService;
    @Resource
    private UpmSeverProperties upmSeverProperties;

    /**
     * 移动端一键登录
     *
     * @param query
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginUserDTO oneClickLogin(SsoAccountAppLoginQuery query) {
        String systemCode = query.getSystemCode();
        String telephone = mengSmsClient.queryMengSms(query.getQuery());
        UpmSystem upmSystem = systemRepository.find(systemCode);
        String cid = query.getCid();
        String appId = query.getAppId();

        UserWxInfoDTO userAppInfoDTO = userThirdMapper.selectByPhoneAndOpenId(telephone, cid, upmSystem.getId(),appId);
        // 注册用户流程
        Boolean firstLogin = Boolean.FALSE;
        if (ObjUtil.isNull(userAppInfoDTO)) {
            userAppInfoDTO = userDomainService.createAppUser(upmSystem.getId(), telephone,appId,cid);
            firstLogin = Boolean.TRUE;
        }
        // 账号是否冻结
        if (LoginFailUtil.isFreeze(String.valueOf(userAppInfoDTO.getId()))) {
            LoginFailUtil.loginFreezeException(String.valueOf(userAppInfoDTO.getId()));
        }
        // token生成
        MiniLoginUserDTO miniLoginUserDTO = this.generateUserToken(systemCode, userAppInfoDTO.getId(), userAppInfoDTO.getUserId());
        miniLoginUserDTO.setTelephone(telephone);
        miniLoginUserDTO.setFirstLogin(firstLogin);
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, miniLoginUserDTO, userAppInfoDTO);
        userAuthInfoDTO.setToken(miniLoginUserDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(miniLoginUserDTO.getToken(), userAuthInfoDTO);
        // 单点限制
        final Long userId = userAppInfoDTO.getUserId();
        wxMinActionService.wxChangeSingleLogin(appId, userId, cid);
        log.info("userAuthInfoDTO:{}", new Gson().toJson(userAuthInfoDTO));
        return miniLoginUserDTO;

    }

    /**
     * 小程序短信验证码登录
     *
     * @param query
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginUserDTO smsLogin(SsoAccountAppSmsLoginQuery query) {
        String systemCode = query.getSystemCode();
        String telephone = query.getTelephone();
        String cid = query.getCid();
        String appId = query.getAppId();

        UpmSystem upmSystem = systemRepository.find(systemCode);
        UserWxInfoDTO userAppInfoDTO = userThirdMapper.selectByPhoneAndOpenId(telephone,cid,upmSystem.getId(),appId);
        Boolean firstLogin = Boolean.FALSE;
        if (ObjUtil.isNull(userAppInfoDTO)) {
            userAppInfoDTO = userDomainService.createAppUser(upmSystem.getId(), telephone,appId,cid);
            firstLogin = Boolean.TRUE;
        }
        // 账号是否冻结
        if (LoginFailUtil.isFreeze(String.valueOf(userAppInfoDTO.getId()))) {
            LoginFailUtil.loginFreezeException(String.valueOf(userAppInfoDTO.getId()));
        }
        // 校验输入的短信验证码
        SmsUtil.checkLoginTelephone(query.getTelephone(), query.getSmsVerificationCode(), userAppInfoDTO.getId());
        // 清空登录失败记录
        LoginFailUtil.clearFailLog(String.valueOf(userAppInfoDTO.getId()));
        // token生成
        MiniLoginUserDTO miniLoginUserDTO = this.generateUserToken(systemCode, userAppInfoDTO.getId(), userAppInfoDTO.getUserId());
        miniLoginUserDTO.setTelephone(telephone);
        miniLoginUserDTO.setFirstLogin(firstLogin);
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, miniLoginUserDTO, userAppInfoDTO);
        userAuthInfoDTO.setToken(miniLoginUserDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(miniLoginUserDTO.getToken(), userAuthInfoDTO);
        // 单点限制
        final Long userId = userAppInfoDTO.getUserId();
        wxMinActionService.wxChangeSingleLogin(appId, userId, cid);
        log.info("userAuthInfoDTO:{}", new Gson().toJson(userAuthInfoDTO));
        return miniLoginUserDTO;
    }

    /**
     * 小程序账号登录
     *
     * @param query
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginUserDTO accountLogin(SsoAccountAppUsernameQuery query) {
        String username = query.getUsername();
        String password = query.getPassword();
        String systemCode = query.getSystemCode();
        String cid = query.getCid();
        String appId = query.getAppId();

        UpmSystem upmSystem = systemRepository.find(systemCode);
        UserWxInfoDTO userAppInfoDTO = userThirdMapper.selectByUsernameAndOpenId(username, cid, upmSystem.getId(),appId);
        if (ObjUtil.isNull(userAppInfoDTO)) {
            userAppInfoDTO = userDomainService.accountCreateAppUser(upmSystem.getId(), appId, username, cid);
        }
        // 账号是否冻结
        Long accountId = userAppInfoDTO.getId();
        if (LoginFailUtil.isFreeze(String.valueOf(accountId))) {
            LoginFailUtil.loginFreezeException(String.valueOf(userAppInfoDTO.getId()));
        }
        UserAccountInfoDTO accountInfo = new UserAccountInfoDTO()
            .setPassword(userAppInfoDTO.getPassword())
            .setSalt(userAppInfoDTO.getSalt());
        if (!ssoAccountActionService.checkPassword(password, accountInfo)) {
            long failSize = LoginFailUtil.recordLoginFail(String.valueOf(accountId));
            LoginFailUtil.loginFailException(failSize, String.valueOf(accountId));
            // throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 清空登录失败记录
        LoginFailUtil.clearFailLog(String.valueOf(accountId));
        // token生成
        MiniLoginUserDTO miniLoginUserDTO = this.generateUserToken(systemCode, accountId, userAppInfoDTO.getUserId());
        // 用户信息并记录缓存
        LoginUserAuthInfoDTO userAuthInfoDTO = this.generateTokenSessionToken(upmSystem, miniLoginUserDTO, userAppInfoDTO);
        userAuthInfoDTO.setToken(miniLoginUserDTO.getToken());
        SsoTokenUtil.setLoginUserAuthInfo(miniLoginUserDTO.getToken(), userAuthInfoDTO);
        // 单点限制
        final Long userId = userAppInfoDTO.getUserId();
        wxMinActionService.wxChangeSingleLogin(appId, userId, cid);
        log.info("userAuthInfoDTO:{}", new Gson().toJson(userAuthInfoDTO));
        return miniLoginUserDTO;

    }

    private MiniLoginUserDTO generateUserToken(String systemCode, Long accountId, Long userId) {
        // 生成设备标识和ticket码 ticket码的维度 账号+设备码 防止多个web登录同账号的ticket码冲突
        String userDeviceIdentify = SsoTokenUtil.generateUserDeviceIdentify(systemCode);
        SaLoginModel saLoginModel = ssoTokenActionService.buildMiniSaLoginModel(systemCode);
        //登出
        if(upmSeverProperties.getLogin().isAppSingleLogin()){
            List<LoginUserAuthInfoDTO> userAuthInfoDTOS = arrangeTokensByUseId(accountId, Collections.singletonList(userId));
            if(CollUtil.isNotEmpty(userAuthInfoDTOS)) {
                userAuthInfoDTOS.stream().filter(authInfoDTO -> ClientTypeEnum.APP.equals(authInfoDTO.getClientType()))
                    .forEach(authInfoDTO -> StpUtil.logoutByTokenValue(authInfoDTO.getToken()));
            }
        }

        StpUtil.login(SsoTokenUtil.generateUserLoginId(accountId, userId), saLoginModel);
        String token = StpUtil.getTokenValue();
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(MessageCode.USER_GENERATE_TOKEN_FAIL);
        }
        // 增加给前端返回密钥 分配用于验签的密钥 生成64位的密钥
        String secretKey = RandomUtil.randomString(32);

        MiniLoginUserDTO miniLoginUserDTO = new MiniLoginUserDTO();
        miniLoginUserDTO.setUserId(userId);
        miniLoginUserDTO.setToken(token);
        miniLoginUserDTO.setSecretKey(secretKey);
        miniLoginUserDTO.setUserDeviceIdentify(userDeviceIdentify);
        return miniLoginUserDTO;
    }

    private LoginUserAuthInfoDTO generateTokenSessionToken(UpmSystem upmSystem, MiniLoginUserDTO miniLoginUserDTO, UserWxInfoDTO userWxInfoDTO) {
        LoginUserAuthInfoDTO authInfoDTO = new LoginUserAuthInfoDTO();
        authInfoDTO.setClientType(ClientTypeEnum.APP);
        authInfoDTO.setAccountId(userWxInfoDTO.getId());
        authInfoDTO.setUserId(userWxInfoDTO.getUserId());
        authInfoDTO.setSecretKey(miniLoginUserDTO.getSecretKey());
        authInfoDTO.setUserDeviceIdentify(miniLoginUserDTO.getUserDeviceIdentify());
        authInfoDTO.setAuthKey(userWxInfoDTO.getAuthKey());
        authInfoDTO.setTenantId(userWxInfoDTO.getTenantId());
        authInfoDTO.setSystemId(upmSystem.getId());
        authInfoDTO.setSystemType(upmSystem.getType());
        authInfoDTO.setSystemCode(upmSystem.getCode());
        authInfoDTO.setSystemName(upmSystem.getName());
        authInfoDTO.setTelephone(userWxInfoDTO.getTelephone());
        authInfoDTO.setUsername(userWxInfoDTO.getUsername());
        authInfoDTO.setSystemName(upmSystem.getName());
        authInfoDTO.setSystemCode(upmSystem.getCode());
        // 小程序
        // authInfoDTO.setUnionId(wxMaUserInfo.getUnionid());
        // authInfoDTO.setAppId(appId);
        // authInfoDTO.setOpenId(wxMaUserInfo.getOpenid());
        // authInfoDTO.setSessionKey(wxMaUserInfo.getSessionKey());
        // 司机信息
        PmsDriverDTO driverInfo = driverInfoClient.getByUserId(userWxInfoDTO.getUserId());
        if (ObjUtil.isNotNull(driverInfo)) {
            authInfoDTO.setDriverId(driverInfo.getId());
            authInfoDTO.setDriverName(driverInfo.getName());
        }
        return authInfoDTO;
    }

    /**
     * 获取当前用户已登录的所有用户上下文信息
     * @param accountId
     * @param userIdList
     * @return
     */
    private List<LoginUserAuthInfoDTO> arrangeTokensByUseId(Long accountId, List<Long> userIdList){
        List<String> tokens = new ArrayList<>();
        userIdList.forEach(userId->{
            List<String> tokenList = StpUtil.getTokenValueListByLoginId(SsoTokenUtil.generateUserLoginId(accountId, userId));
            if(CollUtil.isNotEmpty(tokenList)){
                tokens.addAll(tokenList);
            }
        });
        List<LoginUserAuthInfoDTO> userAuthInfos= new ArrayList<>();
        tokens.forEach(token -> {
            LoginUserAuthInfoDTO authInfoDTO = SsoTokenUtil.getLoginUserAuthInfoFormToken(token);
            if (ObjUtil.isNull(authInfoDTO)) {
                return;
            }
            userAuthInfos.add(authInfoDTO);
        });
        return userAuthInfos;
    }
}
