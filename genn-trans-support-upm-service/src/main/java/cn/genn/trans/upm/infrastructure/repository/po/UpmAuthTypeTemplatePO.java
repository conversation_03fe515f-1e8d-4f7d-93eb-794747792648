package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.database.mybatisplus.typehandler.ListLongTypeHandler;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;


/**
 * UpmAuthTypeTemplatePO对象
 *
 * <AUTHOR>
 * @desc
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_auth_type_template", autoResultMap = true)
public class UpmAuthTypeTemplatePO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 权限组类型：pl平台,op运营,ca承运,cl客户
     */
    @TableField("auth_type")
    private AuthGroupEnum authType;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 资源树json
     */
    @TableField(value = "resource_json",typeHandler = ListLongTypeHandler.class)
    private List<Long> resourceJson;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}

