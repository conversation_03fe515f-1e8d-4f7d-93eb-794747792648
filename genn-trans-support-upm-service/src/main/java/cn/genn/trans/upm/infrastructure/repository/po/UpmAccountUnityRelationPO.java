package cn.genn.trans.upm.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmAccountUnityRelationPO对象
 *
 * <AUTHOR>
 * @desc 统一账号组
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_account_unity_relation", autoResultMap = true)
public class UpmAccountUnityRelationPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 统一登录账号id
     */
    @TableField("unity_account_id")
    private Long unityAccountId;

    /**
     * 默认账号id
     */
    @TableField("default_account_id")
    private Long defaultAccountId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

}

