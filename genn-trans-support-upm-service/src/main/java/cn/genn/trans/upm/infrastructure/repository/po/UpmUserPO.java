package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.UserTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * UpmUserPO对象
 *
 * <AUTHOR>
 * @desc 用户表
 */
@Data
@Builder
@Accessors(chain = true)
@TableName(value = "upm_user", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
public class UpmUserPO implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 账号id
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 状态（1：启用；2：停用）
     */
    @TableField("status")
    private StatusEnum status;

    /**
     * 昵称
     */
    @TableField("nick")
    private String nick;

    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 权限组key
     */
    @TableField("auth_key")
    private String authKey;

    /**
     * 联系电话
     */
    @TableField("telephone")
    private String telephone;

    /**
     * 联系电话
     */
    @TableField("email")
    private String email;

    /**
     * 用户类型
     */
    @TableField("type")
    private UserTypeEnum type;

    @TableField(value = "effective_time")
    private LocalDateTime effectiveTime;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;



}

