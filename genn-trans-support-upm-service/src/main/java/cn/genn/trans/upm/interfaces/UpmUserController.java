package cn.genn.trans.upm.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.service.action.UpmUserActionService;
import cn.genn.trans.upm.application.service.query.UpmUserQueryService;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.dto.UpmUserThirdDTO;
import cn.genn.trans.upm.interfaces.query.*;
import cn.genn.trans.upm.interfaces.query.mini.UpmUserInfoQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "用户管理")
@RestController
@RequestMapping("/upmUser")
public class UpmUserController {

    @Resource
    private UpmUserQueryService queryService;
    @Resource
    private UpmUserActionService actionService;


    /**
     * 分页查询用户
     *
     * @param query
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<UpmUserDTO> page(@ApiParam(value = "查询类") @RequestBody @Validated UpmUserPageQuery query) {
        return queryService.page(query);
    }

    @PostMapping("/pageUserRole")
    @ApiOperation(value = "分页查询列表")
    public PageResultDTO<UpmUserDTO> pageUserRole(@ApiParam(value = "查询类") @RequestBody @Validated UpmUserRolePageQuery query) {
        return queryService.pageUserRole(query);
    }

    /**
     * 条件查询用户列表
     */
    @PostMapping("/conditionList")
    @ApiOperation(value = "条件查询用户列表")
    public List<UpmUserDTO> conditionList(@ApiParam(value = "查询类") @RequestBody @Validated UpmUserQuery query) {
        return queryService.conditionList(query);
    }

    /**
     * usernameList和authKey查询用户
     * 已删除数据也会查出
     */
    @PostMapping("/queryUserByUsernamesAndAuthKey")
    @ApiOperation(value = "usernameList和authKey查询用户")
    public List<UpmUserDTO> queryUserByUsernamesAndAuthKey(@ApiParam(value = "查询类") @RequestBody @Validated UpmUserInfoQuery query) {
        return queryService.queryUserByUsernamesAndAuthKey(query);
    }

    /**
     * 检索username和nick查询用户列表,仅限当前权限组
     */
    @GetMapping("/searchName")
    @ApiOperation(value = "search检索username和nick查询用户列表")
    public List<UpmUserDTO> searchName(@RequestParam("search") String search,@RequestParam("authKey") String authKey) {
        return queryService.searchName(search,authKey);
    }

    @PostMapping("/getThirdByUserIds")
    @ApiOperation("userIds获取三方用户信息")
    public List<UpmUserThirdDTO> getThirdByUserIds(@Validated @RequestBody UserThirdQuery query){
        return queryService.getThirdByUserIds(query);
    }

    @PostMapping("/getThirdByUserIdsAndSystemType")
    @ApiOperation("userIds和systemType获取三方用户信息")
    public List<UpmUserThirdDTO> getThirdByUserIdsAndSystemType(@Validated @RequestBody UserThirdSystemQuery query){
        return queryService.getThirdByUserIdsAndSystemType(query);
    }


    /**
     * 角色code查询用户,
     * @param query
     * @return
     */
    @PostMapping("/queryByRoleCode")
    @ApiOperation(value = "角色code查询用户")
    public List<UpmUserDTO> queryByRoleCode(@RequestBody @Validated UserRoleQuery query) {
        return queryService.queryByRoleCode(query);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    public UpmUserDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id) {
        return queryService.get(id);
    }

    /**
     * username判断账号是否存在
     */
    @GetMapping("/check/username")
    @ApiOperation(value = "根据username查询,true表示存在")
    public Boolean checkUserName(@RequestParam("username") String username, @RequestParam("systemId") Long systemId) {
        return queryService.checkUserName(username, systemId);
    }

    /**
     * 手机号判断账号是否存在
     */
    @GetMapping("/check/telephone")
    @ApiOperation(value = "手机号判断账号是否存在,true表示存在")
    public Boolean checkTelephone(@RequestParam("telephone") String telephone, @RequestParam("systemId") Long systemId) {
        return queryService.checkTelephone(telephone, systemId);
    }

    /**
     * 添加新用户
     *
     * @param command
     * @return
     */
    @PostMapping("/save")
    @ApiOperation(value = "添加新用户")
    public UpmUserDTO save(@ApiParam(value = "用户信息") @RequestBody @Validated UpmUserSaveCommand command) {
        return actionService.save(command);
    }

    @PostMapping("/saveBySystemType")
    @ApiOperation(value = "依照系统类型添加新用户")
    public List<UpmUserDTO> saveBySystemType(@ApiParam(value = "用户信息") @RequestBody @Validated UpmUserSaveBySystemTypeCommand command) {
        return actionService.saveBySystemType(command);
    }

    @PostMapping("/updateBySystemType")
    @ApiOperation(value = "依照系统类型修改用户")
    public Boolean updateBySystemType(@ApiParam(value = "用户信息") @RequestBody @Validated UpmUserUpdateBySystemTypeCommand command) {
        return actionService.updateBySystemType(command);
    }

    @PostMapping("/change/status/bySystemType")
    @ApiOperation(value = "依照系统类型启用停用用户")
    public Boolean changeStatusBySystemType(@ApiParam(value = "启用禁用用户") @RequestBody @Validated UpmUserChangeStatusBySystemTypeCommand command) {
        return actionService.changeStatusBySystemType(command);
    }

    @PostMapping("/reset/password/bySystemType")
    @ApiOperation(value = "依照系统类型重置密码")
    public Boolean resetPasswordBySystemType(@ApiParam(value = "重置密码") @RequestBody @Validated UpmUserResetPasswordBySystemTypeCommand command) {
        return actionService.resetPasswordBySystemType(command);
    }

    @PostMapping("/delete/bySystemType")
    @ApiOperation(value = "依照系统类型删除用户")
    public Boolean deleteBySystemType(@ApiParam(value = "批量删除用户") @RequestBody UpmUserDelBySystemTypeCommand command) {
        return actionService.deleteBySystemType(command);
    }

    /**
     * 修改用户
     *
     * @param command
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改用户")
    public Boolean change(@ApiParam(value = "用户信息") @RequestBody @Validated UpmUserUpdateCommand command) {
        return actionService.update(command);
    }

    /**
     * 启用禁用用户
     *
     * @param command
     * @return
     */
    @PostMapping("/change/status")
    @ApiOperation(value = "启用停用用户")
    public Boolean changeStatus(@ApiParam(value = "启用禁用用户") @RequestBody @Validated UpmChangeStatusCommand command) {
        return actionService.changeStatus(command);
    }

    /**
     * 重置密码
     *
     * @param command
     * @return
     */
    @PostMapping("/reset/password")
    @ApiOperation(value = "重置密码")
    public Boolean resetPassword(@ApiParam(value = "重置密码") @RequestBody @Validated UpmUserResetPasswordCommand command) {
        return actionService.resetPassword(command);
    }

    /**
     * 关联角色
     */
    @PostMapping("/related/role")
    @ApiOperation(value = "关联角色")
    public Boolean relatedRole(@ApiParam(value = "用户关联角色") @RequestBody @Validated UpmUserRoleRelationCommand command) {
        return actionService.relatedRole(command);
    }

    /**
     * 批量删除用户
     */
    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除用户")
    public Boolean batchRemove(@ApiParam(value = "批量删除用户") @RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        return actionService.batchRemove(idList);
    }

    /**
     * 各系统游客注册
     *
     * @param command
     * @return
     */
    @PostMapping("/userRegister")
    @ApiOperation(value = "各系统游客注册")
    public Boolean userRegister(@RequestBody @Validated UserRegisTerCommand command) {
        return actionService.userRegister(command);
    }

    /**
     * 个人中心-更新用户信息
     */
    @PostMapping("/changeUserInfo")
    @ApiOperation(value = "更新用户信息")
    public Boolean changeUserInfo(@RequestBody @Validated UserInfoChangeCommand command) {
        return actionService.changeUserInfo(command);
    }

    /**
     * 个人中心-用户绑定手机
     */
    @PostMapping("/bindPhone")
    @ApiOperation(value = "用户绑定手机")
    public Boolean bindPhone(@RequestBody @Validated UserBindPhoneCommand command) {
        return actionService.bindPhone(command);
    }

    /**
     * 个人中心-用户更换绑定手机
     */
    @PostMapping("/changeBindPhone")
    @ApiOperation(value = "用户更换绑定手机")
    public Boolean changeBindPhone(@RequestBody @Validated UserChangeBindPhoneCommand command) {
        return actionService.changeBindPhone(command);
    }

    /**
     * 密保手机修改密码-登录后修改密码
     */
    @PostMapping("/changePdByPhone")
    @ApiOperation(value = "密保手机修改密码")
    public Boolean changePdByPhone(@RequestBody @Validated PdChangeByPhoneCommand command) {
        return actionService.changePdByPhone(command);
    }

    /**
     * 个人中心-原密码方式修改密码
     *
     * @param command
     * @return
     */
    @PostMapping("/changePdByPd")
    @ApiOperation(value = "原密码方式修改密码")
    public Boolean changePdByPd(@RequestBody @Validated PdChangeByPdCommand command) {
        return actionService.changePdByPd(command);
    }

    @ApiOperation(value = "飞书登录查询该用户是否存在系统")
    @PostMapping("/queryByFsUser")
    public UpmUserDTO queryByFsUser(@RequestBody UserThirdQuery query) {
        return queryService.queryByFsUser(query);
    }

    @ApiOperation(value = "创建飞书三方用户信息")
    @PostMapping("/createFsThirdUserInfo")
    public Boolean createThirdUserInfo(@RequestBody UpmUserThirdOperateCommand command){
        return actionService.createThirdUserInfo(command);
    }

    @PostMapping("/updateFsThirdInfoStatus")
    @ApiOperation(value = "启用停用三方信息表状态")
    public Boolean updateFsThirdInfoStatus(@RequestBody UpmUserThirdUpdateStatusCommand command){
        return actionService.updateFsThirdInfoStatus(command);
    }


}

