package cn.genn.trans.upm.application.assembler;

import cn.genn.trans.upm.interfaces.dto.captcha.CaptchaDTO;
import com.anji.captcha.model.vo.CaptchaVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CaptchaAssembler {

    CaptchaDTO VO2DTO(CaptchaVO vo);
    CaptchaVO DTO2VO(CaptchaDTO vo);


}
