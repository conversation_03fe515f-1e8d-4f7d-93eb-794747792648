package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.assembler.UpmUserRoleAssembler;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserRoleMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserRolePO;
import cn.genn.trans.upm.interfaces.dto.UpmUserRoleDTO;
import cn.genn.trans.upm.interfaces.query.UpmUserRoleQuery;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmUserRoleQueryService {

    @Resource
    private UpmUserRoleMapper mapper;
    @Resource
    private UpmUserRoleAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return UpmUserRoleDTO分页对象
     */
    public PageResultDTO<UpmUserRoleDTO> page(UpmUserRoleQuery query) {
        UpmUserRolePO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return UpmUserRoleDTO
     */
    public UpmUserRoleDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }
}

