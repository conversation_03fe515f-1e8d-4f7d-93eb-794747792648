package cn.genn.trans.upm.application.service.action;

import cn.genn.trans.upm.application.assembler.UpmResourceReturnAssembler;
import cn.genn.trans.upm.application.processor.ResourceProcessor;
import cn.genn.trans.upm.domain.upm.factory.ResourceFactory;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.service.ResourceDomainService;
import cn.genn.trans.upm.interfaces.command.UpmChangeStatusCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceOperateCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceSaveCommand;
import cn.genn.trans.upm.interfaces.command.yzg.YzgPermissionSyncCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.AbstractMap;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional
public class UpmResourceActionService {

    @Resource
    private ResourceDomainService resourceDomainService;
    @Resource
    private ResourceFactory resourceFactory;
    @Resource
    private ResourceProcessor resourceProcessor;

    @Resource
    private UpmResourceReturnAssembler resourceReturnAssembler;

    /**
     * 添加
     *
     * @return Long
     */
    public Boolean save(UpmResourceSaveCommand command) {
        resourceProcessor.save(command);
        return resourceDomainService.create(resourceFactory.createUpmResource(command));
    }

    /**
     * 修改
     */
    public void change(UpmResourceOperateCommand command) {
        resourceProcessor.change(command);
        resourceDomainService.change(resourceFactory.updateUpmResource(command));
    }

    /**
     * 修改状态
     */
    public boolean changeStatus(UpmChangeStatusCommand command) {
        return resourceDomainService.changeStatus(resourceFactory.changeStatus(command));
    }

    /**
     * 删除
     */
    public void remove(List<Long> resourceIdList) {
        resourceDomainService.remove(resourceFactory.deleteUpmResource(resourceIdList));
    }

    public List<UpmResource> syncResourceByYzg(YzgPermissionSyncCmd cmd) {
        List<YzgPermissionSyncCmd.YzgPermissionItem> list = cmd.getPermissions();
        Map<AbstractMap.SimpleEntry<Integer, String>, List<YzgPermissionSyncCmd.YzgPermissionItem>> map = list.stream()
            .collect(Collectors.groupingBy(permission ->
                new AbstractMap.SimpleEntry<>(permission.getGroupId(), permission.getGroupName()), Collectors.toList()));


        // 转换UpmResource
        List<UpmResource> menu = map.keySet().stream()
            .map(item -> resourceReturnAssembler.convertToMenu(cmd.getSystemId(), item.getKey(), item.getValue()))
            .collect(Collectors.toList());

        resourceDomainService.syncSourceDelBySystemId(cmd.getSystemId());
        List<UpmResource> resourceList = resourceDomainService.saveBatch(menu);

        Map<Integer, Long> groupMenuIdMap = resourceList.stream().collect(Collectors.toMap(k -> Integer.valueOf(k.getCode().split("_")[1]), v -> v.getId()));

        List<UpmResource> subButtons = map.entrySet().stream().map(entry -> {
            Integer groupId = entry.getKey().getKey();
            Long pid = groupMenuIdMap.get(groupId);
            return entry.getValue().stream()
                .map(item -> resourceReturnAssembler.convertToButton(cmd.getSystemId(), item, pid))
                .collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
        List<UpmResource> buttons = resourceDomainService.saveBatch(subButtons);
        buttons.addAll(resourceList);
        return buttons;
    }

}

