// package cn.genn.trans.upm.infrastructure.repository.persistence;
//
// import cn.genn.trans.upm.domain.upm.repository.RoleResourceRepository;
// import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleResourceMapper;
// import cn.genn.trans.upm.infrastructure.repository.po.UpmRoleResourcePO;
// import cn.hutool.core.util.ObjUtil;
// import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
// import org.springframework.stereotype.Repository;
//
// import java.util.List;
//
// @Repository
// public class RoleResourceRepositoryImpl extends ServiceImpl<UpmRoleResourceMapper, UpmRoleResourcePO> implements RoleResourceRepository {
//
//     /**
//      * @param roleResourceList
//      * @return
//      */
//     @Override
//     public Boolean saveOrUpdate(List<UpmRoleResourcePO> roleResourceList, Long roleId) {
//         baseMapper.deleteByRoleId(roleId);
//         if (ObjUtil.isNotNull(roleResourceList)){
//             baseMapper.saveBatch(roleResourceList);
//         }
//         return true;
//     }
// }
