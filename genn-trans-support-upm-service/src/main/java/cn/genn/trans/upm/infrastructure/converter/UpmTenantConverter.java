package cn.genn.trans.upm.infrastructure.converter;

import cn.genn.core.model.converter.POConverter;
import cn.genn.trans.upm.domain.upm.model.entity.UpmTenant;
import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmTenantConverter extends POConverter<UpmTenant, UpmTenantPO> {
}
