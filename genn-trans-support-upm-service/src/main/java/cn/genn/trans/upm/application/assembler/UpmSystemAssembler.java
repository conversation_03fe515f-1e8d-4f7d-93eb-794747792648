package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.infrastructure.repository.po.UpmSystemPO;
import cn.genn.trans.upm.interfaces.dto.UpmSystemDTO;
import cn.genn.trans.upm.interfaces.query.UpmSystemQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmSystemAssembler extends QueryAssembler<UpmSystemQuery, UpmSystemPO, UpmSystemDTO>{

}

