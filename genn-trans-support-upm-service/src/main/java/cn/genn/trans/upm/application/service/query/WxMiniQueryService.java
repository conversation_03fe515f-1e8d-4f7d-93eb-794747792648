package cn.genn.trans.upm.application.service.query;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CheckException;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/19
 */
@Service
@Slf4j
public class WxMiniQueryService {

    @Resource
    private WxMaService wxMaService;

    /**
     * 小程序基础信息
     * @param appid
     * @param code
     * @return
     */
    public WxMaJscode2SessionResult miniUserInfo(String appid, String code) {
        if (!wxMaService.switchover(appid)) {
            throw new CheckException(MessageCode.MINI_APP_ID_NOT_EXIST);
        }
        try {
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
            if (ObjUtil.isNull(session) || StrUtil.isBlank(session.getSessionKey())
                || StrUtil.isBlank(session.getOpenid())) {
                throw new BusinessException(MessageCode.MINI_USER_INFO_EXIST);
            }
            return session;
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(MessageCode.MINI_USER_INFO_EXIST);
        } finally {
            WxMaConfigHolder.remove();
        }
    }

    /**
     * 获取手机号
     * @param appid
     * @param code
     * @return
     */
    public WxMaPhoneNumberInfo getUserTelephone(String appid, String code) {
        if (!wxMaService.switchover(appid)) {
            throw new CheckException(MessageCode.MINI_APP_ID_NOT_EXIST);
        }
        WxMaPhoneNumberInfo phoneNoInfo = null;
        try {
            phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(code);
            if(StrUtil.isBlank(phoneNoInfo.getPhoneNumber())){
                throw new BusinessException(MessageCode.MINI_PHONE_EXIST);
            }
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        if (ObjUtil.isNull(phoneNoInfo) ||StrUtil.isBlank(phoneNoInfo.getPhoneNumber())) {
            throw new BusinessException(MessageCode.MINI_PHONE_EXIST);
        }
        return phoneNoInfo;
    }

    /**
     * 用户敏感数据解密
     * @param encryptedData
     * @param ivStr
     * @return
     */
    public WxMaUserInfo getUserInfo(String sessionKey,String encryptedData, String ivStr) {
        return wxMaService.getUserService().getUserInfo(sessionKey,encryptedData,ivStr);

    }
}
