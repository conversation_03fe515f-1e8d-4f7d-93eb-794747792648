package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.SortOrder;
import cn.genn.trans.upm.application.assembler.UpmResourceAssembler;
import cn.genn.trans.upm.application.assembler.UpmResourceReturnAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.domain.upm.service.SystemDomainService;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmResourceMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmResourcePO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.dto.UpmResourceTreeDTO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleDTO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleUserDTO;
import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.query.UpmResourceBySystemTypeQuery;
import cn.genn.trans.upm.interfaces.query.UpmResourceQuery;
import cn.genn.trans.upm.interfaces.query.UpmResourceTreeQuery;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmResourceQueryService {

    @Resource
    private UpmResourceMapper mapper;
    @Resource
    private UpmResourceAssembler assembler;
    @Resource
    private UpmResourceReturnAssembler resourceReturnAssembler;
    @Resource
    private ResourceRepository resourceRepository;
    @Resource
    private UpmRoleMapper upmRoleMapper;
    @Resource
    private UpmResourceMapper upmResourceMapper;
    @Autowired
    private SystemDomainService systemDomainService;


    /**
     * 查询当前用户所属权限组的所有资源
     * @return
     */
    public List<UpmResourceDTO> queryListByAuthKey(){
        String authKey = CurrentUserHolder.getAuthKey();
        return assembler.PO2DTO(mapper.selectByAuthKey(authKey,null));
    }


    public List<UpmResourceDTO> queryListBySystemType(UpmResourceBySystemTypeQuery query){
        List<String> authKeyList = systemDomainService.getAuthKeysBySystemType(query.getSystemType(), query.getAuthGroup(), query.getOriginId());
        List<UpmResource> upmResources = resourceRepository.selectAllByAuthKeyList(authKeyList);
        List<UpmResourceDTO> upmResourceDTOS = resourceReturnAssembler.entity2DTO(upmResources);
//        List<UpmResourceDTO> upmResourceDTOS1 = TreeBuildUtil.makeTree(upmResourceDTOS, UpmResourceDTO::getPid, UpmResourceDTO::getId, x -> x.getPid() == 0L, UpmResourceDTO::setChildren);
        return upmResourceDTOS;
    }

    /**
     * 系统，租户，角色列表，资源类型查询资源
     *
     * @param query 查询条件
     * @return UpmResourceDTO对象
     */
    public List<UpmResourceDTO> queryList(UpmResourceQuery query) {
        List<UpmResourcePO> poList = new ArrayList<>();
        //角色查资源id
        if (CollUtil.isNotEmpty(query.getRoleIdList())) {
            poList = resourceRepository.roleQueryList(query);
        } else {
            //系统查资源id.
            poList = resourceRepository.queryBySystemId(query.getSystemId(), query.getType());
        }
        return assembler.PO2DTO(poList);
    }


    public List<UpmResourceDTO> queryListByRoleIds(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        List<UpmResourcePO> poList = upmResourceMapper.selectByRoleIds(roleIds, null);
        return CollectionUtils.isEmpty(poList) ? Collections.emptyList() : assembler.PO2DTO(poList);
    }

    /**
     * 查询没有下级的资源
     * @param idList
     * @return
     */
    public List<Long> queryNoChild(List<Long> idList){
        List<UpmResourcePO> resourceList = mapper.queryByPid(idList);
        List<Long> pidList = resourceList.stream().map(UpmResourcePO::getPid).distinct().collect(Collectors.toList());
        return idList.stream().filter(id->!pidList.contains(id)).collect(Collectors.toList());
    }

    /**
     * 查询资源树
     *
     * @param query 查询条件
     * @return tree
     */
    public List<UpmResourceTreeDTO> treeList(UpmResourceTreeQuery query) {
        return assembler.PO2TREEDTO(mapper.selectList(getQueryWrapperByQuery(query)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return UpmResourceDTO
     */
    public UpmResourceDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }

    /**
     * 用户查询资源
     * @param userId
     * @return
     */
    public List<UpmResourcePO> userQueryList(String authKey,Long userId,Long systemId,Long tenantId) {
        //用户查角色
        List<UpmRoleUserDTO> upmRoleUserDTOS = upmRoleMapper.queryByUserIds(Collections.singletonList(userId));
        if(CollUtil.isEmpty(upmRoleUserDTOS)){
            return null;
        }
        if(upmRoleUserDTOS.stream().anyMatch(upmRoleUserDTO -> upmRoleUserDTO.getType().equals(RoleTypeEnum.SYSTEM))){
            return upmResourceMapper.selectByAuthKey(authKey,null);
        }
        List<Long> roleIds = upmRoleUserDTOS.stream().map(UpmRoleDTO::getId).distinct().collect(Collectors.toList());
        return upmResourceMapper.selectByRoleIds(roleIds,null);
    }

    private QueryWrapper<UpmResourcePO> getQueryWrapperByQuery(UpmResourceTreeQuery query) {
        QueryWrapper<UpmResourcePO> wrapper = new QueryWrapper<>();
        wrapper.eq(Objects.nonNull(query.getSystemId()), "system_id", query.getSystemId());
        wrapper.like(StringUtils.isNotBlank(query.getName()), "name", query.getName());
        wrapper.like(StringUtils.isNotBlank(query.getUrl()), "url", query.getUrl());
        wrapper.like(StringUtils.isNotBlank(query.getCode()), "code", query.getCode());
        wrapper.eq(Objects.nonNull(query.getType()), "type", query.getType());
        wrapper.eq(Objects.nonNull(query.getStatus()), "status", query.getStatus());
        wrapper.like(StringUtils.isNotBlank(query.getTitle()), "title", query.getTitle());
        wrapper.like(StringUtils.isNotBlank(query.getPath()), "path", query.getPath());
        //排序条件
        if (query.getSort() != null && query.getSort().getOrderBy() != null) {
            Map<String, SortOrder> orderBy = query.getSort().getOrderBy();
            orderBy.forEach((k, v) -> {
                if (v == SortOrder.ASC) {
                    wrapper.orderByAsc(k);
                } else {
                    wrapper.orderByDesc(k);
                }
            });
        }
        return wrapper;
    }

}

