package cn.genn.trans.upm.domain.upm.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEventDimensionEnum;
import cn.genn.trans.upm.application.service.action.SpringEventPublishService;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.repository.AuthResourceRelRepository;
import cn.genn.trans.upm.domain.upm.repository.RoleAuthResourceRelRepository;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.infrastructure.converter.UpmRoleConverter;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthResourceRelPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRoleAuthResourceRelPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RoleDomainService {

    @Resource
    private RoleRepository roleRepository;
    @Resource
    private SpringEventPublishService eventPublishService;
    @Resource
    private AuthResourceRelRepository authResourceRelRepository;
    @Resource
    private RoleAuthResourceRelRepository roleAuthResourceRelRepository;
    @Resource
    private UpmRoleConverter converter;
    @Resource
    private UpmRoleMapper upmRoleMapper;

    @Resource
    private UpmSeverProperties upmSeverProperties;


    /**
     * 新增
     *
     * @return Long
     */
    public Long save(UpmRole upmRole) {
        UpmRolePO upmRolePO = converter.entity2PO(upmRole);
        return (long) upmRoleMapper.insert(upmRolePO);
    }

    public List<UpmRolePO> saveBatch(List<UpmRole> upmRoleList) {
        List<UpmRolePO> upmRolePOList = converter.entity2PO(upmRoleList);
        upmRoleMapper.saveBatch(upmRolePOList);
        return upmRolePOList;
    }

    public List<UpmRole> saveBatchUpmRolesBySystemType(List<UpmRole> upmRoleList) {
        checkBeforeSaveBatchBySystemType(upmRoleList);
        return roleRepository.saveBatchEntity(upmRoleList);
    }

    private void checkBeforeSaveBatchBySystemType(List<UpmRole> upmRoleList) {
        if (CollectionUtil.isEmpty(upmRoleList)) {
            return;
        }
        // 同一系统级别创建多系统角色的，只校验一个即可
        UpmRole upmRole = upmRoleList.get(0);
        checkRoleNameIsUniqueByAuthKey(upmRole.getName());
//        checkRoleCodeIsUniqueByAuthKey(upmRole.getCode());
    }


    /**
     * 修改
     */
    public Long change(UpmRole upmRole) {
        return roleRepository.update(upmRole);
    }

    public Boolean updateBatchUpmRolesBySystemType(List<UpmRole> upmRoleList) {
        checkBeforeUpdateBatchBySystemType(upmRoleList);
        return roleRepository.updateBatchEntity(upmRoleList);
    }

    private void checkRoleCodeIsUniqueByAuthKey(String newRoleCode) {
        //角色Code校验
        List<UpmRolePO> rolePOList = roleRepository.selectByCodeAndAuthKey(newRoleCode, CurrentUserHolder.getAuthKey());
        if (CollectionUtil.isNotEmpty(rolePOList)) {
            throw new BusinessException(MessageCode.ROLE_CODE_EXIST_ERROR);
        }
    }

    private void checkRoleNameIsUniqueByAuthKey(String newRoleName) {
        List<UpmRolePO> upmRolePOS = roleRepository.selectByNameAndAuthKey(newRoleName, CurrentUserHolder.getAuthKey());
        if(CollectionUtil.isNotEmpty(upmRolePOS)){
            throw new BusinessException(MessageCode.ROLE_NAME_REPEAT);
        }
    }

    private void checkBeforeUpdateBatchBySystemType(List<UpmRole> upmRoleList) {
        if (CollectionUtil.isEmpty(upmRoleList)) {
            return;
        }
        UpmRole upmRole = upmRoleList.get(0);
        if (Objects.isNull(upmRole.getId())) {
            return;
        }
        UpmRole originData = roleRepository.findById(upmRole.getId());
        if(Objects.isNull(originData)){
            throw new BusinessException(MessageCode.ROLE_NOT_EXIST_ERROR);
        }
        if (StringUtils.isNotBlank(upmRole.getCode())
            && !upmSeverProperties.getNormalRoleCode().equals(upmRole.getCode())
            && !upmRole.getCode().equals(originData.getCode())) {
//            checkRoleCodeIsUniqueByAuthKey(upmRole.getCode());
        }
        if (StringUtils.isNotBlank(upmRole.getName()) && !originData.getName().equals(upmRole.getName())) {
            checkRoleNameIsUniqueByAuthKey(upmRole.getName());
        }
    }

    private void checkBeforeUpdate(UpmRole upmRole) {
        if (Objects.isNull(upmRole)) {
            return;
        }
        if (Objects.isNull(upmRole.getId())) {
            return;
        }
        UpmRole originData = roleRepository.findById(upmRole.getId());
        if(Objects.isNull(originData)){
            throw new BusinessException(MessageCode.ROLE_NOT_EXIST_ERROR);
        }
        if (StringUtils.isNotBlank(upmRole.getCode())
            && !upmSeverProperties.getNormalRoleCode().equals(upmRole.getCode())
            && !upmRole.getCode().equals(originData.getCode())) {
            checkRoleCodeIsUniqueByAuthKey(upmRole.getCode());
        }
        if (StringUtils.isNotBlank(upmRole.getName()) && !originData.getName().equals(upmRole.getName())) {
            checkRoleNameIsUniqueByAuthKey(upmRole.getName());
        }
    }

    /**
     * 批量删除
     */
    public Long batchRemove(List<Long> idList) {
        return roleRepository.batchDelete(idList);
    }

    /**
     * 角色关联菜单
     */
    public Boolean changeResource(UpmRole upmRole) {
        List<UpmRoleAuthResourceRelPO> roleResourceList = null;
        if (CollectionUtil.isNotEmpty(upmRole.getResourceIdList())) {
            List<UpmAuthResourceRelPO> relationList = authResourceRelRepository.selectByResourceIds(upmRole.getResourceIdList(), upmRole.getTenantId());
            if (CollectionUtil.isEmpty(relationList)) {
                throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
            }
            roleResourceList = relationList.stream().map(tenantResource -> {
                    return new UpmRoleAuthResourceRelPO().setRoleId(upmRole.getId()).setAuthResourceId(tenantResource.getId());
                }
            ).collect(Collectors.toList());
        }
        boolean result = roleAuthResourceRelRepository.saveOrUpdate(roleResourceList, upmRole.getId());
        eventPublishService.publishUpmAuthChangeEvent(UpmAuthChangeEventDimensionEnum.ROLE, upmRole.getId());
        return result;
    }

    /**
     * fsp角色关联菜单
     */
    public Boolean fspChangeResource(UpmRole upmRole) {
        List<UpmRoleAuthResourceRelPO> roleResourceList = null;
        if (CollectionUtil.isNotEmpty(upmRole.getResourceIdList())) {
            List<UpmAuthResourceRelPO> relationList = authResourceRelRepository.selectByResourceIdsAndAuthKey(upmRole.getResourceIdList(), upmRole.getAuthKey());
            if (CollectionUtil.isEmpty(relationList)) {
                throw new BusinessException(MessageCode.RESOURCE_NOT_EXIST);
            }
            roleResourceList = relationList.stream().map(tenantResource -> {
                    return new UpmRoleAuthResourceRelPO().setRoleId(upmRole.getId()).setAuthResourceId(tenantResource.getId());
                }
            ).collect(Collectors.toList());
        }
        boolean result = roleAuthResourceRelRepository.saveOrUpdate(roleResourceList, upmRole.getId());
        eventPublishService.publishUpmAuthChangeEvent(UpmAuthChangeEventDimensionEnum.ROLE, upmRole.getId());
        return result;
    }

    /**
     * 创建超级管理员角色
     */
    public List<UpmRolePO> createSuperRole(Long tenantId, List<Long> systemIdList, Long originId, AuthGroupEnum authGroup) {
        Long userId = CurrentUserHolder.getUserId();
        String username = CurrentUserHolder.getUserName();
        List<UpmRolePO> rolePOList = new ArrayList<>();
        for (Long systemId : systemIdList) {
            UpmRolePO upmRolePO = new UpmRolePO()
                .setCode(upmSeverProperties.getSuperRoleCode())
                .setName(upmSeverProperties.getSuperRoleName())
                .setSystemId(systemId)
                .setTenantId(tenantId)
                .setType(RoleTypeEnum.SYSTEM)
                .setCreateUserId(userId)
                .setCreateUserName(username)
                .setUpdateUserId(userId)
                .setUpdateUserName(username)
                .setAuthKey(AuthKeyUtil.getAuthKey(authGroup,systemId, tenantId, originId));
            rolePOList.add(upmRolePO);
        }
        upmRoleMapper.saveBatch(rolePOList);
        return rolePOList;
    }

    public Boolean fspSave(UpmRole upmRole){
        UpmRolePO upmRolePO = converter.entity2PO(upmRole);
        upmRoleMapper.insert(upmRolePO);
        upmRole.setId(upmRolePO.getId());
        return this.fspChangeResource(upmRole);
    }

    public boolean deleteByTenantId(Long tenantId) {
        return roleRepository.deleteByTenantId(tenantId);
    }

    public void deleteAuthKeyAndCode(List<String> authkeyList, String roleCode) {
        upmRoleMapper.deleteAuthKeyAndCode(authkeyList,roleCode);
    }

    public List<UpmRole> selectByAuthKeyListAndMainRoleIds(List<String> authKeyList, List<Long> ids) {
        List<UpmRolePO> upmRolePOS = upmRoleMapper.selectBatchIds(ids);
        List<String> roleNameList = Optional.ofNullable(upmRolePOS).orElse(new ArrayList<>(0))
            .stream().map(UpmRolePO::getName).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(roleNameList)) {
            return Collections.EMPTY_LIST;
        }
        List<UpmRolePO> upmRoleList = upmRoleMapper.selectByAuthKeyAndMainRoleNames(authKeyList, roleNameList);
        return Optional.ofNullable(upmRoleList).map(converter::PO2Entity).orElse(Collections.EMPTY_LIST);
    }

    public List<UpmRole> selectByAuthKeyAndMainRoleId(List<String> authKeyList, Long roleId) {
        UpmRolePO upmRolePO = upmRoleMapper.selectById(roleId);
        if (Objects.isNull(upmRolePO)) {
            throw new BusinessException(MessageCode.ROLE_NOT_EXIST_ERROR);
        }
        List<UpmRolePO> upmRolePOS = upmRoleMapper.selectByAuthKeyAndMainRoleName(upmRolePO.getName(), authKeyList);
        return Optional.ofNullable(upmRolePOS).map(converter::PO2Entity).orElse(Collections.EMPTY_LIST);
    }

}
