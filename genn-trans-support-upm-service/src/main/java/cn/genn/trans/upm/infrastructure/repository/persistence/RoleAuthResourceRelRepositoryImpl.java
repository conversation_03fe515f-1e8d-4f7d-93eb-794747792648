package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.trans.upm.domain.upm.repository.RoleAuthResourceRelRepository;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRoleAuthResourceRelPO;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Repository
public class RoleAuthResourceRelRepositoryImpl extends ServiceImpl<UpmRoleAuthResourceRelMapper, UpmRoleAuthResourceRelPO> implements RoleAuthResourceRelRepository {

    /**
     * @param roleResourceList
     * @return
     */
    @Override
    public Boolean saveOrUpdate(List<UpmRoleAuthResourceRelPO> roleResourceList, Long roleId) {
        baseMapper.deleteByRoleId(roleId);
        if (ObjUtil.isNotNull(roleResourceList)){
            baseMapper.saveBatch(roleResourceList);
        }
        return true;
    }
}
