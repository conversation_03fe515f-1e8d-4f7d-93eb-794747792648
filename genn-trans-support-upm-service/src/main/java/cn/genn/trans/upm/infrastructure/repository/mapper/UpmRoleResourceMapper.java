// package cn.genn.trans.upm.infrastructure.repository.mapper;
//
// import cn.genn.trans.upm.infrastructure.repository.po.UpmRoleResourcePO;
// import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//
// import java.util.List;
//
// /**
//  * <AUTHOR>
//  */
// public interface UpmRoleResourceMapper extends BaseMapper<UpmRoleResourcePO> {
//
//     /**
//      * 清除角色数据
//      */
//     void deleteByRoleId(Long roleId);
//
//     /**
//      * 批量写入
//      * @param list
//      * @return
//      */
//     void saveBatch(List<UpmRoleResourcePO> list);
// }
