// package cn.genn.trans.upm.infrastructure.repository.mapper;
//
// import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantSystemResourcePO;
// import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//
// import java.util.List;
//
// /**
//  * <AUTHOR>
//  */
// public interface UpmTenantSystemResourceMapper extends BaseMapper<UpmTenantSystemResourcePO> {
//
//     int saveBatch(List<UpmTenantSystemResourcePO> list);
// }
