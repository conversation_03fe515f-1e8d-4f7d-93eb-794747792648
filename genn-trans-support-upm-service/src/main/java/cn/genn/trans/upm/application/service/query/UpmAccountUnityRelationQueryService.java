package cn.genn.trans.upm.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.application.assembler.UpmAccountUnityRelationAssembler;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAccountUnityRelationMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountUnityRelationPO;
import cn.genn.trans.upm.interfaces.dto.UpmAccountUnityRelationDTO;
import cn.genn.trans.upm.interfaces.query.UpmAccountUnityRelationQuery;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmAccountUnityRelationQueryService {

    @Resource
    private UpmAccountUnityRelationMapper mapper;
    @Resource
    private UpmAccountUnityRelationAssembler assembler;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return UpmAccountUnityRelationDTO分页对象
     */
    public PageResultDTO<UpmAccountUnityRelationDTO> page(UpmAccountUnityRelationQuery query) {
        UpmAccountUnityRelationPO po = assembler.query2PO(query);
        return assembler.toPageResult(mapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new QueryWrapper<>(po)));
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return UpmAccountUnityRelationDTO
     */
    public UpmAccountUnityRelationDTO get(Long id) {
        return assembler.PO2DTO(mapper.selectById(id));
    }
}

