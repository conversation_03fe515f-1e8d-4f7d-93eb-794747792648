package cn.genn.trans.upm.infrastructure.config.captcha.service;

import cn.genn.trans.upm.infrastructure.config.captcha.AjCaptchaProperties;
import com.anji.captcha.service.CaptchaCacheService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 分布式部署的应用，建议实现CaptchaCacheService，比如用Redis，参考service/spring-boot代码示例。
 */
@Service("AjCaptchaCacheService")
public class CaptchaCacheServiceRedisImpl implements CaptchaCacheService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String type() {
        return AjCaptchaProperties.StorageType.redis.name();
    }

    @Override
    public void set(String key, String value, long expiresInSeconds) {
        stringRedisTemplate.opsForValue().set(key, value, expiresInSeconds, TimeUnit.SECONDS);
    }

    @Override
    public boolean exists(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    @Override
    public void delete(String key) {
        stringRedisTemplate.delete(key);
    }

    @Override
    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    @Override
    public Long increment(String key, long val) {
        return stringRedisTemplate.opsForValue().increment(key, val);
    }
}
