package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserThirdPO;
import cn.genn.trans.upm.interfaces.dto.UpmUserThirdDTO;
import cn.genn.trans.upm.interfaces.query.UpmUserThirdQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UpmUserThirdAssembler extends QueryAssembler<UpmUserThirdQuery, UpmUserThirdPO, UpmUserThirdDTO>{
}

