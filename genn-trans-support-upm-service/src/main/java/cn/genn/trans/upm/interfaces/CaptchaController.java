package cn.genn.trans.upm.interfaces;

import cn.genn.trans.upm.application.assembler.CaptchaAssembler;
import cn.genn.trans.upm.infrastructure.utils.CaptchaUtil;
import cn.genn.trans.upm.interfaces.dto.captcha.CaptchaDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 图形验证码
 *
 * <AUTHOR>
 * @date 2024/7/13
 */
@RestController
@RequestMapping({"/sso"})
@Api(value = "图形验证码", tags = {"图形验证码"})
public class CaptchaController {

    @Resource
    private CaptchaAssembler captchaAssembler;

    /**
     * 提供滑动拼图
     *
     * @param data
     * @return
     * @see com.anji.captcha.model.common.CaptchaTypeEnum
     */
    @PostMapping("/getCaptcha")
    public CaptchaDTO getCaptcha(@RequestBody CaptchaDTO data) {
        return captchaAssembler.VO2DTO(CaptchaUtil.getCaptcha(captchaAssembler.DTO2VO(data)));
    }

    /**
     * 验证码校验
     *
     * @param data
     * @return
     */
    @PostMapping("/checkCaptcha")
    public CaptchaDTO checkCaptcha(@RequestBody CaptchaDTO data) {
        return captchaAssembler.VO2DTO(CaptchaUtil.checkCaptcha(captchaAssembler.DTO2VO(data)));
    }
}
