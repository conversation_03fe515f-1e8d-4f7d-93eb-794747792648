package cn.genn.trans.upm.application.service.action;

import cn.genn.trans.upm.application.assembler.AuthGroupAssembler;
import cn.genn.trans.upm.application.assembler.UpmTenantAssembler;
import cn.genn.trans.upm.domain.upm.model.entity.UpmTenant;
import cn.genn.trans.upm.domain.upm.repository.AuthGroupRepository;
import cn.genn.trans.upm.domain.upm.service.*;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmAuthResourceRelMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmTenantSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.*;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UpmTenantDTO;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmTenantActionService {


    @Resource
    private TenantDomainService tenantDomainService;
    @Resource
    private UpmTenantAssembler tenantAssembler;
    @Resource
    private SystemDomainService systemDomainService;
    @Resource
    private ResourceDomainService resourceDomainService;
    @Resource
    private RoleDomainService roleDomainService;
    @Resource
    private UserDomainService userDomainService;
    @Resource
    private UpmSystemMapper systemMapper;
    @Resource
    private AuthGroupRepository authGroupRepository;
    @Resource
    private AuthGroupAssembler authGroupAssembler;
    @Resource
    private UpmAuthResourceRelMapper authResourceRelMapper;
    @Autowired
    private UpmTenantSystemMapper upmTenantSystemMapper;

    /**
     * 运营商添加租户
     *
     * @param command
     * @return
     */
    public UpmTenantDTO save(UpmTenantSaveCommand command) {
        // 添加租户
        UpmTenant upmTenant = tenantAssembler.upmTenantSaveCommand2UpmTenant(command);
        UpmTenantPO po = tenantDomainService.save(upmTenant);
        return tenantAssembler.PO2DTO(po);
    }

    /**
     * 运营商添加租户后置操作
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAfter(TenantSaveAfterCommand command) {
        Long tenantId = command.getTenantId();
        Long operatorId = command.getOperatorId();
        // 权限内所有系统
        List<Long> systemIdList = command.getSystemResourceList().stream().map(SystemResourceCommand::getSystemId).distinct().collect(Collectors.toList());

        List<UpmSystemPO> systemList = systemMapper.selectBatchIds(systemIdList);
        //运营商层级系统
        List<Long> operatorSystemIdList = systemList.stream().filter(system -> system.getType().equals(SystemTypeEnum.OPERATOR))
            .map(UpmSystemPO::getId).collect(Collectors.toList());
        //租户系统关联
        List<UpmTenantSystemPO> systemPOList = systemDomainService.tenantSystemRelation(systemIdList, tenantId);
        //运营层新增权限组
        List<UpmAuthGroupPO> upmAuthGroupPOS = this.arrangeAuthGroupOperator(operatorSystemIdList, operatorId,tenantId);
        authGroupRepository.saveBatch(upmAuthGroupPOS);
        //运营层权限组资源关联
        resourceDomainService.saveAuthResourceRel(systemPOList,operatorSystemIdList,command.getSystemResourceList(), tenantId, operatorId,AuthGroupEnum.OPERATOR);
        //运营层创建超级管理员角色
        List<UpmRolePO> upmRolePOList = roleDomainService.createSuperRole(tenantId, operatorSystemIdList,operatorId,AuthGroupEnum.OPERATOR);
        //运营层创建超级管理员账号
        // List<UpmRolePO> operatorRoleList = upmRolePOList.stream().filter(role -> operatorSystemIdList.contains(role.getSystemId())).collect(Collectors.toList());
        userDomainService.createSuperUser(operatorSystemIdList,upmRolePOList, command.getManagerAccount(),null,null);
        return true;
    }

    /**
     * 租户编辑
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean changeRegistration(TenantRegistrationChangeCommand command) {
        Long tenantId = command.getTenantId();
        Long operatorId = command.getOperatorId();
        // 编辑租户
        if(ObjUtil.isNotNull(command.getTenantChangeCommand())){
            UpmTenant upmTenant = tenantAssembler.upmTenantChangeCommand2UpmTenant(command.getTenantChangeCommand());
            tenantDomainService.change(upmTenant);
        }
        // 重置租户系统和资源
        if (CollUtil.isNotEmpty(command.getSystemResourceList())) {
            List<Long> systemIdList = command.getSystemResourceList().stream().map(SystemResourceCommand::getSystemId).distinct().collect(Collectors.toList());
            List<UpmSystemPO> systemList = systemMapper.selectBatchIds(systemIdList);
            List<Long> operatorSystemIdList = systemList.stream().filter(system -> system.getType().equals(SystemTypeEnum.OPERATOR))
                .map(UpmSystemPO::getId).collect(Collectors.toList());
            //运营层权限组资源关联
            List<SystemResourceCommand> operatorResourceList = command.getSystemResourceList().stream().filter(systemResource -> operatorSystemIdList.contains(systemResource.getSystemId())).distinct().collect(Collectors.toList());
            resourceDomainService.batchSystemReplaceAuthResource(operatorResourceList,tenantId,operatorId,AuthGroupEnum.OPERATOR);
            //ToDo:角色替换,用户替换,现在运营层只有一个系统,不存在替换;
        }

        return true;
    }

    public boolean changeStatus(UpmChangeStatusCommand command) {
        return tenantDomainService.updateStatus(command.getIdList(), command.getStatus());
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        // 租户关联系统,租户系统关联资源未删除

        // 删除角色和用户
        userDomainService.deleteByTenantId(id);
        roleDomainService.deleteByTenantId(id);
        return tenantDomainService.delete(id);
    }

    /**
     * 整理权限组数据
     *
     * @param systemIdList
     * @param operatorId
     * @return
     */
    private List<UpmAuthGroupPO> arrangeAuthGroupOperator(List<Long> systemIdList, Long operatorId,Long tenantId) {
        return systemIdList.stream().map(systemId -> new UpmAuthGroupPO()
            .setAuthKey(AuthKeyUtil.getOperatorKey(systemId, tenantId, operatorId))
            .setTenantId(tenantId)
            .setSystemId(systemId)
            .setType(AuthGroupEnum.OPERATOR)).collect(Collectors.toList());
    }

}

