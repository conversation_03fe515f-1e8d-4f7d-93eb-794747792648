package cn.genn.trans.upm.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.application.assembler.UpmUserAssembler;
import cn.genn.trans.upm.domain.upm.repository.AuthGroupRepository;
import cn.genn.trans.upm.domain.upm.service.ResourceDomainService;
import cn.genn.trans.upm.domain.upm.service.RoleDomainService;
import cn.genn.trans.upm.domain.upm.service.SystemDomainService;
import cn.genn.trans.upm.domain.upm.service.UserDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmSystemMapper;
import cn.genn.trans.upm.infrastructure.repository.po.*;
import cn.genn.trans.upm.interfaces.command.OriginCreateAdminCommand;
import cn.genn.trans.upm.interfaces.command.SystemResourceCommand;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 */
@Service
@Slf4j
public class UpmOriginActionService {

    @Resource
    private SystemDomainService systemDomainService;
    @Resource
    private ResourceDomainService resourceDomainService;
    @Resource
    private RoleDomainService roleDomainService;
    @Resource
    private UserDomainService userDomainService;
    @Resource
    private UpmSystemMapper systemMapper;
    @Resource
    private AuthGroupRepository authGroupRepository;

    @Resource
    private UpmUserAssembler upmUserAssembler;


    @Transactional(rollbackFor = Exception.class)
    public List<UpmUserDTO> createOriginAdministrator(OriginCreateAdminCommand command) {
        Long tenantId = command.getTenantId();
        if (CollectionUtils.isEmpty(command.getSystemResourceList())) {
            // 若无则直接获取当前租户关联的"指定系统"下所有系统id 的所有资源
            Map<Long, Set<Long>> systemResourceByAuthKey = systemDomainService.findResourceBySystemType(command.getType());
            // 补充资源ID列表
            List<SystemResourceCommand> systemResourceCommands = systemResourceByAuthKey.entrySet().stream().map(entry -> {
                SystemResourceCommand systemResourceCommand = new SystemResourceCommand();
                systemResourceCommand.setSystemId(entry.getKey());
                systemResourceCommand.setResourceIdList(new ArrayList<>(entry.getValue()));
                return systemResourceCommand;
            }).collect(Collectors.toList());
            command.setSystemResourceList(systemResourceCommands);
        }
        List<Long> systemIdList = Optional.ofNullable(command.getSystemResourceList())
            .orElse(new ArrayList<>(0))
            .stream().map(SystemResourceCommand::getSystemId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(systemIdList)) {
            throw new BusinessException(MessageCode.SYSTEM_REOURCE_NOT_EXIST);
        }
        List<UpmSystemPO> systemList = systemMapper.selectBatchIds(systemIdList);
        // 系统层级过滤
        systemIdList = Optional.ofNullable(systemList).orElse(new ArrayList<>(0)).stream()
            .filter(system -> system.getType().getCode().equals(command.getType().getCode()))
            .map(UpmSystemPO::getId).collect(Collectors.toList());

        // 租户系统关联
        List<UpmTenantSystemPO> systemPOList = systemDomainService.createTenantSystemRelation(systemIdList, tenantId);

        // 新增权限组
        List<UpmAuthGroupPO> upmAuthGroupPOS = systemIdList.stream().map(systemId -> new UpmAuthGroupPO()
                .setAuthKey(AuthKeyUtil.getAuthKey(command.getAuthGroup(), systemId, tenantId, command.getOriginId()))
                .setTenantId(tenantId)
                .setSystemId(systemId)
                .setType(command.getAuthGroup()))
            .collect(Collectors.toList());
        authGroupRepository.saveBatch(upmAuthGroupPOS);

        // 权限组资源关联
        resourceDomainService.saveCommonAuthResourceRel(systemPOList, systemIdList, command.getSystemResourceList(),
            tenantId, command.getOriginId(), command.getAuthGroup());
        // 创建超级管理员角色
        List<UpmRolePO> upmRolePOList = roleDomainService.createSuperRole(tenantId, systemIdList, command.getOriginId(), command.getAuthGroup());

        // 创建超级管理员账号
        Long systemId = systemIdList.get(0);
        userDomainService.checkAccountNameisUniqueInSystem(command.getUserName(), systemId);
        userDomainService.checkPhoneIsUniqueInSystem(command.getTelephone(), systemId);
        List<UpmUserPO> defaultSuperUser = userDomainService.createDefaultSuperUser(systemIdList, upmRolePOList, command.getUserName(),
                command.getPassword(), command.getTelephone(), command.getNickName());
        // 附加系统编码
        Map<Long, String> systemMap = systemList.stream().collect(Collectors.toMap(UpmSystemPO::getId, UpmSystemPO::getCode));

        List<UpmUserDTO> userDTOS = upmUserAssembler.PO2DTO(defaultSuperUser);
        userDTOS.forEach(user -> {
            user.setSystemCode(systemMap.get(user.getSystemId()));
        });

        return userDTOS;
    }

}

