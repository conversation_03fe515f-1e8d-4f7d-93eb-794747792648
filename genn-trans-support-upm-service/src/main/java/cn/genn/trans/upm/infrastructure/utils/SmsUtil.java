package cn.genn.trans.upm.infrastructure.utils;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.upm.infrastructure.constant.CacheConstants;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Date: 2024/6/11
 * @Author: ka<PERSON><PERSON><PERSON>
 */
@Slf4j
public class SmsUtil {

    public static final String BUSINESS_CODE_OF_LOGIN = "login";
    public static final String BUSINESS_CODE_OF_REGISTER = "register";
    public static final String BUSINESS_CODE_OF_BINDING = "binding";
    public static final String BUSINESS_CODE_OF_CHANGE_PASSWORD = "password";

    public static String generateCacheNotifyCodeKey(String businessCode, String telephone) {
        return CacheConstants.CACHE_LOGIN_SMS_CODE + businessCode + "_" + telephone;
    }

    /**
     * 上次发送验证码的时间
     *
     * @param businessCode
     * @param telephone
     * @return
     */
    public static String generateLastActiveSendTimeKey(String businessCode, String telephone) {
        return CacheConstants.CACHE_SEND_SMS_LAST_ACTIVE_TIME + businessCode + "_" + telephone;
    }

    /**
     * 登录验证码检测
     *
     * @param telephone        手机号
     * @param verificationCode 验证码
     * @param accountId        账号id
     */
    public static void checkLoginTelephone(String telephone, String verificationCode, Long accountId) {
        // 校验输入的短信验证码
        if (StrUtil.isNotBlank(telephone) && StrUtil.isNotBlank(verificationCode)) {
            String cacheRegisterSmsKey = generateCacheNotifyCodeKey(BUSINESS_CODE_OF_LOGIN, telephone);
            String code = RedisUtils.get(cacheRegisterSmsKey);
            log.info("缓存的短信验证码:{}", code);
            if (StrUtil.isBlank(code)) {
                throw new BusinessException(MessageCode.USER_SMS_CODE_EXPIRE);
            }
            if (!code.equals(verificationCode)) {
                long failSize = LoginFailUtil.recordLoginFail(String.valueOf(accountId));
                LoginFailUtil.smsLoginFailException(failSize, String.valueOf(accountId));
            }
        }
    }

    /**
     * 验证码检测
     *
     * @param businessCode     发送短信类型
     * @param telephone        手机号
     * @param verificationCode 验证码
     */
    public static void checkTelephone(String businessCode, String telephone, String verificationCode) {
        // 校验输入的短信验证码
        if (StrUtil.isNotBlank(telephone) && StrUtil.isNotBlank(verificationCode)) {
            String cacheRegisterSmsKey = generateCacheNotifyCodeKey(businessCode, telephone);
            String code = RedisUtils.get(cacheRegisterSmsKey);
            log.info("缓存的短信验证码:{}", code);
            if (StrUtil.isBlank(code)) {
                throw new BusinessException(MessageCode.USER_SMS_CODE_EXPIRE);
            }
            if (!code.equals(verificationCode)) {
                throw new BusinessException(MessageCode.USER_SMS_CODE_CHECK_FAIL);
            }
        }
    }


    /**
     * 判断距离上次发送验证码是否超过一分钟
     *
     * @param businessCode
     * @param telephone
     * @return
     */
    public static void isSendSmsFrequently(String businessCode, String telephone) {
        String lastActiveSendTimeKey = generateLastActiveSendTimeKey(businessCode, telephone);
        String lastActiveSendTime = RedisUtils.get(lastActiveSendTimeKey);
        if (lastActiveSendTime != null) {
            long currentTime = System.currentTimeMillis();
            long timeInterval = currentTime - Long.parseLong(lastActiveSendTime);
            log.info("距离上次发送验证码的时间间隔:{}", timeInterval);
            // 距离上次发送验证码时间间隔小于60秒
            if (timeInterval < 60000) {
                throw new BusinessException(MessageCode.USER_SEND_SMS_FAIL_TIME);
            }
        }
    }
}
