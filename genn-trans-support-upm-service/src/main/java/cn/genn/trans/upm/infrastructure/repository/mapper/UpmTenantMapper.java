package cn.genn.trans.upm.infrastructure.repository.mapper;

import cn.genn.trans.upm.infrastructure.repository.po.UpmTenantPO;
import cn.genn.trans.upm.interfaces.query.UpmTenantQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpmTenantMapper extends BaseMapper<UpmTenantPO> {

    Page<UpmTenantPO> selectByPage(Page<UpmTenantPO> page, @Param("query") UpmTenantQuery query);

    List<UpmTenantPO> selectByList(@Param("query") UpmTenantQuery query);

    List<UpmTenantPO> selectListBySystemId(Long systemId);

}
