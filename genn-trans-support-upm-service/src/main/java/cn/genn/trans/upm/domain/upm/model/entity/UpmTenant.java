package cn.genn.trans.upm.domain.upm.model.entity;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.TenantTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UpmTenant implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 类型（platform：平台方, op:运营方，bp:）
     */
    private TenantTypeEnum type;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 上级id
     */
    private Long pid;

    /**
     * 下级租户
     */
    private UpmTenant subTenant;

    /**
     * 关联系统
     */
    private List<UpmSystem> systemList;

    /**
     * 关联用户
     */
    private List<UpmUser> userList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 集群分组(default)
     */
    private String vpcGroup;

    private String domain;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private Long createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private Long updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;


}
