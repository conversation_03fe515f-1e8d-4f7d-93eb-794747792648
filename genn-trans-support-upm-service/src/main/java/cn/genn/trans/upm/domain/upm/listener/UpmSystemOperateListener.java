package cn.genn.trans.upm.domain.upm.listener;

import cn.genn.spring.boot.starter.event.spring.component.SpringEventAsyncListener;
import cn.genn.trans.upm.domain.upm.event.UpmSystemOperateEvent;

/**
 * <AUTHOR>
 */
public class UpmSystemOperateListener extends SpringEventAsyncListener<UpmSystemOperateEvent> {

    @Override
    protected void onMessage(UpmSystemOperateEvent event) {
        // 日志记录 通知租户？
    }
}
