// package cn.genn.trans.upm.infrastructure.repository.po;
//
// import com.baomidou.mybatisplus.annotation.*;
// import lombok.Data;
// import lombok.experimental.Accessors;
//
// import java.time.LocalDateTime;
//
//
// /**
//  * UpmRoleResourcePO对象
//  *
//  * <AUTHOR>
//  * @desc 角色资源关联表
//  */
// @Data
// @Accessors(chain = true)
// @TableName(value = "upm_role_resource", autoResultMap = true)
// public class UpmRoleResourcePO {
//
//     /**
//      * 主键自增
//      */
//     @TableId
//     private Long id;
//
//     /**
//      * 租户系统资源关联id
//      */
//     @TableField("tenant_resource_id")
//     private Long tenantResourceId;
//
//     /**
//      * 角色id
//      */
//     @TableField("role_id")
//     private Long roleId;
//
//     /**
//      * 创建时间
//      */
//     @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER)
//     private LocalDateTime createTime;
//
//     /**
//      * 创建用户ID
//      */
//     @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
//     private Long createUserId;
//
//     /**
//      * 创建用户名
//      */
//     @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
//     private String createUserName;
//
//
// }
//
