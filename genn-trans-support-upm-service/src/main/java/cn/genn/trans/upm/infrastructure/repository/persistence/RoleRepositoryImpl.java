package cn.genn.trans.upm.infrastructure.repository.persistence;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.infrastructure.converter.UpmRoleConverter;
import cn.genn.trans.upm.infrastructure.dto.RoleUserRelDTO;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleUserDTO;
import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Repository
public class RoleRepositoryImpl extends ServiceImpl<UpmRoleMapper, UpmRolePO> implements RoleRepository {

    @Resource
    private UpmRoleConverter converter;

    /**
     * id查询角色
     *
     * @param id
     */
    @Override
    public UpmRolePO selectById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public UpmRole findById(Long id) {
        return converter.PO2Entity(baseMapper.selectById(id));
    }

    /**
     * name重复校验
     *
     * @param name
     * @param authKey
     */
    @Override
    public List<UpmRolePO> selectByNameAndAuthKey(String name, String authKey) {
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getName, name)
            .eq(UpmRolePO::getAuthKey, authKey);
        return baseMapper.selectList(wrapper);
    }

    /**
     * code重复校验
     * @param code
     * @param authKey
     * @return
     */
    @Override
    public List<UpmRolePO> selectByCodeAndAuthKey(String code, String authKey) {
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getCode, code)
            .eq(UpmRolePO::getAuthKey, authKey);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<UpmRole> queryAllRoleAndResource() {
        return baseMapper.queryAllRoleAndResource();
    }

    @Override
    public List<RoleUserRelDTO> getAllUserAndRole() {
        return baseMapper.getAllUserAndRoleRel();
    }

    /**
     * 过滤系统角色
     *
     * @param idList
     */
    @Override
    public List<UpmRolePO> selectSystemRoleList(List<Long> idList) {
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .in(UpmRolePO::getId, idList)
            .eq(UpmRolePO::getType, RoleTypeEnum.SYSTEM);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 修改角色
     *
     * @param upmRole
     */
    @Override
    public Long update(UpmRole upmRole) {
        LambdaUpdateWrapper<UpmRolePO> wrapper = Wrappers.lambdaUpdate(UpmRolePO.class)
            .eq(UpmRolePO::getId, upmRole.getId())
            .set(StrUtil.isNotBlank(upmRole.getName()), UpmRolePO::getName, upmRole.getName())
            .set(UpmRolePO::getRemark, upmRole.getRemark())
            .set(ObjectUtil.isNotNull(upmRole.getStatus()), UpmRolePO::getStatus, upmRole.getStatus());
        return (long) baseMapper.update(wrapper);
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    @Override
    public Long batchDelete(List<Long> idList) {
        return (long) baseMapper.deleteBatchIds(idList);
    }

    /**
     * 批量修改状态
     *
     * @param idList
     * @param status
     */
    @Override
    public Long updateStatus(List<Long> idList, StatusEnum status) {

        LambdaUpdateWrapper<UpmRolePO> wrapper = Wrappers.lambdaUpdate(UpmRolePO.class)
            .in(UpmRolePO::getId, idList)
            .set(UpmRolePO::getStatus, status);
        return (long) baseMapper.update(wrapper);
    }

    /**
     * 用户ids查询启用关联角色
     *
     * @param userIdList
     * @return
     */
    @Override
    public List<UpmRoleUserDTO> queryByUserIds(List<Long> userIdList) {
        if (CollectionUtil.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.queryByUserIds(userIdList);
    }

    @Override
    public List<UpmRolePO> selectBatchIds(List<Long> roleIds) {
        if (CollectionUtil.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchIds(roleIds);
    }

    @Override
    public boolean deleteByTenantId(Long tenantId) {
        LambdaQueryWrapper<UpmRolePO> wrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getTenantId, tenantId);
        baseMapper.delete(wrapper);
        return true;
    }

    @Override
    public boolean deleteByTenantIdAndSystemIds(Long tenantId, List<Long> systemIds) {
        LambdaUpdateWrapper<UpmRolePO> wrapper = Wrappers.lambdaUpdate(UpmRolePO.class)
            .eq(UpmRolePO::getTenantId, tenantId)
            .in(UpmRolePO::getSystemId, systemIds);
        baseMapper.delete(wrapper);
        return true;
    }

    @Override
    public List<UpmRolePO> recoverTenantIdAndSystemIds(Long tenantId, List<Long> systemIds) {
        LambdaUpdateWrapper<UpmRolePO> wrapper = Wrappers.lambdaUpdate(UpmRolePO.class)
            .eq(UpmRolePO::getTenantId, tenantId)
            .in(UpmRolePO::getSystemId, systemIds)
            .set(UpmRolePO::getDeleted, DeletedEnum.NOT_DELETED.getCode());
        baseMapper.update(wrapper);
        LambdaQueryWrapper<UpmRolePO> queryWrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getTenantId, tenantId)
            .in(UpmRolePO::getSystemId, systemIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UpmRolePO> selectByTenantAndSystem(Long systemId, Long tenantId, RoleTypeEnum roleType) {
        LambdaQueryWrapper<UpmRolePO> queryWrapper = Wrappers.lambdaQuery(UpmRolePO.class)
            .eq(UpmRolePO::getTenantId, tenantId)
            .eq(UpmRolePO::getSystemId, systemId)
            .eq(ObjUtil.isNull(roleType),UpmRolePO::getType,roleType);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<UpmRole> saveBatchEntity(List<UpmRole> roleList) {
        List<UpmRolePO> upmRolePOS = converter.entity2PO(roleList);
        saveBatch(upmRolePOS);
        return converter.PO2Entity(upmRolePOS);
    }

    @Override
    public Boolean updateBatchEntity(List<UpmRole> roleList) {
        List<UpmRolePO> upmRolePOS = converter.entity2PO(roleList);
        return updateBatchById(upmRolePOS);
    }
}
