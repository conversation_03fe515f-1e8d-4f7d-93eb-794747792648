package cn.genn.trans.upm.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.application.assembler.UpmRoleAssembler;
import cn.genn.trans.upm.application.assembler.UpmRoleReturnAssembler;
import cn.genn.trans.upm.application.processor.RoleProcessor;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.repository.RoleRepository;
import cn.genn.trans.upm.domain.upm.service.ResourceDomainService;
import cn.genn.trans.upm.domain.upm.service.RoleDomainService;
import cn.genn.trans.upm.domain.upm.service.SystemDomainService;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmRoleMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeRoleTemplatePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAuthTypeTemplatePO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmRolePO;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.properties.SsoAuthProperties;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UpmRoleDTO;
import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmRoleActionService {

    @Resource
    private RoleDomainService roleDomainService;
    @Resource
    private UpmRoleAssembler roleAssembler;
    @Resource
    private UpmRoleReturnAssembler upmRoleReturnAssembler;
    @Resource
    private RoleRepository repository;
    @Resource
    private RoleProcessor roleProcessor;
    @Autowired
    private SsoAuthProperties ssoAuthProperties;
    @Resource
    private UpmRoleMapper roleMapper;
    @Resource
    private UpmSeverProperties upmSeverProperties;

    @Resource
    private SystemDomainService systemDomainService;
    @Resource
    private ResourceDomainService resourceDomainService;

    /**
     * 新增
     *
     * @return Long
     */
    public Boolean save(UpmRoleSaveCommand command) {
        roleProcessor.checkSave(command);
        UpmRole upmRole = roleAssembler.upmRoleSaveCommand2UpmRole(command);
        upmRole.setAuthKey(CurrentUserHolder.getAuthKey());
        upmRole.setType(RoleTypeEnum.TENANT);
        upmRole.setSystemId(CurrentUserHolder.getSystemId());
        upmRole.setTenantId(CurrentUserHolder.getTenantId());
        upmRole.setCode(Optional.ofNullable(command.getCode()).orElse(upmSeverProperties.getNormalRoleCode()));
        roleDomainService.save(upmRole);
        return true;
    }

    public Long saveReturnId(UpmRoleSaveCommand command) {
        roleProcessor.checkSave(command);
        UpmRole upmRole = roleAssembler.upmRoleSaveCommand2UpmRole(command);
        upmRole.setAuthKey(CurrentUserHolder.getAuthKey());
        upmRole.setType(RoleTypeEnum.TENANT);
        upmRole.setSystemId(CurrentUserHolder.getSystemId());
        upmRole.setTenantId(CurrentUserHolder.getTenantId());
        upmRole.setCode(Optional.ofNullable(command.getCode()).orElse(upmSeverProperties.getNormalRoleCode()));
        return roleDomainService.saveReturnRoleId(upmRole);
    }



    @Transactional(rollbackFor = Exception.class)
    public List<UpmRoleDTO> saveBySystemType(UpmRoleSaveBySystemTypeCommand command) {
        // 获取当前系统类型下所有的系统id
        List<UpmSystem> systems = systemDomainService.findBySystemType(command.getSystemType());
        if (CollectionUtil.isEmpty(systems)) {
            throw new BusinessException(MessageCode.SYSTEM_REOURCE_NOT_EXIST_CANT_CREATE_ROLE);
        }
        Long tenantId = CurrentUserHolder.getTenantId();

        roleProcessor.checkBeforeSaveBySystemType(command);
        List<UpmRole> upmRoles = systems.stream().map(system -> {
            String authKey = AuthKeyUtil.getAuthKey(command.getAuthGroup(), system.getId(), tenantId, command.getOriginId());
            UpmRole upmRole = roleAssembler.upmRoleSaveBySystem2UpmRole(command);
            upmRole.setAuthKey(authKey);
            upmRole.setType(RoleTypeEnum.TENANT);
            upmRole.setSystemId(system.getId());
            upmRole.setTenantId(tenantId);
            upmRole.setCode(Optional.ofNullable(command.getCode()).orElse(upmSeverProperties.getNormalRoleCode()));
            return upmRole;
        }).collect(Collectors.toList());
        List<UpmRole> savedUpmRoles = roleDomainService.saveBatchUpmRolesBySystemType(upmRoles);
        return upmRoleReturnAssembler.entity2DTO(savedUpmRoles);
    }

    /**
     * 修改
     *
     * @return Boolean
     */
    public Boolean change(UpmRoleUpdateCommand command) {
        roleProcessor.checkChange(command);
        roleDomainService.change(roleAssembler.upmRoleUpdateCommand2UpmRole(command));
        return true;
    }

    /**
     * 按照系统类型修改
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBySystemType(UpmRoleUpdateBySystemTypeCommand command) {
        roleProcessor.checkBeforeUpdateBySystemType(command);
        List<String> authKeyList = systemDomainService.getAuthKeysBySystemType(command.getSystemType(), command.getAuthGroup(), command.getOriginId());
        List<UpmRole> upmRoles = roleDomainService.selectByAuthKeyAndMainRoleId(authKeyList, command.getId());
        upmRoles.forEach(role -> {
            roleAssembler.updateBySystemTypeCmd(command, role);
        });
        roleDomainService.updateBatchUpmRolesBySystemType(upmRoles);
        return true;
    }

    /**
     * 启用停用角色
     *
     * @param command
     * @return
     */
    public Boolean changeStatus(UpmChangeStatusCommand command) {
        repository.updateStatus(command.getIdList(),command.getStatus());
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatusBySystemType(UpmChangeStatusBySystemTypeCommand command) {
        List<String> authKeyList = systemDomainService.getAuthKeysBySystemType(command.getSystemType(), command.getAuthGroup(), command.getOriginId());
        List<UpmRole> upmRoles = roleDomainService.selectByAuthKeyListAndMainRoleIds(authKeyList, command.getIdList());
        List<Long> roleIds = upmRoles.stream().map(UpmRole::getId).collect(Collectors.toList());
        repository.updateStatus(roleIds, command.getStatus());
        return true;
    }

    /**
     * 批量删除
     * @param idList
     * @return
     */
    public Boolean batchRemove(List<Long> idList){
        //系统角色禁止删除
        List<UpmRolePO> upmRolePOS = repository.selectSystemRoleList(idList);
        List<Long> systemIdList = upmRolePOS.stream().map(UpmRolePO::getId).collect(Collectors.toList());
        boolean systemRole = idList.stream().anyMatch(systemIdList::contains);
        if(systemRole){
            throw new BusinessException(MessageCode.ROLE_SYSTEM_NOT_DELETE_ERROR);
        }
        idList = idList.stream().filter(id -> !systemIdList.contains(id)).collect(Collectors.toList());
        roleDomainService.batchRemove(idList);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteBySystemType(UpmRoleDelBySystemTypeCommand command){
        List<String> authKeyList = systemDomainService.getAuthKeysBySystemType(command.getSystemType(), command.getAuthGroup(), command.getOriginId());
        List<UpmRole> upmRoles = roleDomainService.selectByAuthKeyListAndMainRoleIds(authKeyList, command.getIdList());
        // 系统角色禁止删除
        boolean existSystemRole = upmRoles.stream().anyMatch(role -> RoleTypeEnum.SYSTEM.equals(role.getType()));
        if(existSystemRole){
            throw new BusinessException(MessageCode.ROLE_SYSTEM_NOT_DELETE_ERROR);
        }
        List<Long> roleIds = upmRoles.stream().map(UpmRole::getId).collect(Collectors.toList());
        roleDomainService.batchRemove(roleIds);
        return true;
    }

    /**
     * 修改菜单权限
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeResource(UpmRoleResourceCommand command) {
        UpmRolePO upmRolePO = repository.selectById(command.getRoleId());
        if(ObjUtil.isNull(upmRolePO)){
            throw new BusinessException(MessageCode.ROLE_NOT_EXIST_ERROR);
        }
        UpmRole upmRole = new UpmRole()
            .setId(command.getRoleId())
            .setSystemId(upmRolePO.getSystemId())
            .setTenantId(upmRolePO.getTenantId())
            .setResourceIdList(command.getResourceIdList());
        return roleDomainService.changeResource(upmRole);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean changeResourceBySystemType(UpmRoleResourceChangeBySystemTypeCommand command) {
        List<String> authKeyList = systemDomainService.getAuthKeysBySystemType(command.getSystemType(), command.getAuthGroup(), command.getOriginId());
        List<UpmRole> upmRoles = roleDomainService.selectByAuthKeyAndMainRoleId(authKeyList, command.getMainRoleId());
        List<UpmResource> resourceList = resourceDomainService.selectBatchByIds(command.getResourceIdList());
        Map<Long, List<Long>> systemResourceMap = new HashMap<>(0);
        if (CollectionUtil.isNotEmpty(resourceList)) {
            systemResourceMap = resourceList.stream()
                .collect(Collectors.groupingBy(UpmResource::getSystemId, Collectors.mapping(UpmResource::getId, Collectors.toList())));
        }
        for (UpmRole upmRole : upmRoles) {
            upmRole.setResourceIdList(systemResourceMap.get(upmRole.getSystemId()));
            roleDomainService.changeResource(upmRole);
        }
        return Boolean.TRUE;
    }

    /**
     * 权限组添加角色
     */
    public void insertAuthRoleBatch(List<Long> companyIdList, List<Long> resourceJson,UpmAuthTypeRoleTemplatePO po, UpmAuthTypeTemplatePO templatePO){
        List<UpmRole> roleList = new ArrayList<>();
        for (Long companyId : companyIdList) {
            UpmRole upmRole = new UpmRole();
            upmRole.setName(po.getRoleName());
            upmRole.setCode(po.getRoleCode());
            upmRole.setSystemId(templatePO.getSystemId());
            upmRole.setRemark(po.getRemark());
            String authKey = AuthKeyUtil.getAuthKey(templatePO.getAuthType(),templatePO.getSystemId(),ssoAuthProperties.getPlatformTenantId(),companyId);
            upmRole.setAuthKey(authKey);
            upmRole.setType(RoleTypeEnum.TENANT);
            upmRole.setSystemId(templatePO.getSystemId());

            upmRole.setTenantId(ssoAuthProperties.getPlatformTenantId());
            upmRole.setCreateUserId(CurrentUserHolder.getUserId());
            upmRole.setCreateUserName(CurrentUserHolder.getUserName());
            upmRole.setUpdateUserId(CurrentUserHolder.getUserId());
            upmRole.setUpdateUserName(CurrentUserHolder.getUserName());
            roleList.add(upmRole);
        }
        List<UpmRolePO> rolePOList = roleDomainService.saveBatch(roleList);
        if(CollUtil.isNotEmpty(resourceJson)){
            for (UpmRolePO upmRolePO : rolePOList) {
                UpmRole upmRole = new UpmRole();
                upmRole.setId(upmRolePO.getId());
                upmRole.setAuthKey(upmRolePO.getAuthKey());
                upmRole.setResourceIdList(resourceJson);
                roleDomainService.fspChangeResource(upmRole);
            }
        }
    }

    public void updateAuthRoleBatch(UpmAuthTypeRoleTemplatePO oldPO,UpmAuthTypeRoleTemplatePO newPO, UpmAuthTypeTemplatePO templatePO){
        Long tenantId = ssoAuthProperties.getPlatformTenantId();

        String authKeyBefore = templatePO.getAuthType().getCode() + "-" + templatePO.getSystemId() + "-" + tenantId;
        //修改前的角色code=>所有要修改的角色Id
        List<UpmRolePO> rolePOList = roleMapper.selectByCodeAndAuthKeyBefore(oldPO.getRoleCode(),authKeyBefore);
        if(CollUtil.isEmpty(rolePOList)){
            return;
        }
        List<Long> roleIdList = rolePOList.stream().map(UpmRolePO::getId).distinct().collect(Collectors.toList());
        //如果code或name有变,则根据id修改
        if(!oldPO.getRoleCode().equals(newPO.getRoleCode()) || oldPO.getRoleName().equals(newPO.getRoleName())){
            roleMapper.updateBatch(roleIdList,newPO.getRoleCode(),newPO.getRoleName(),newPO.getRemark());
        }
        //如果资源json有变,则修改资源关联
        //todo: 如果初始化的角色允许在角色管理页面编辑菜单,这里就要改为只添加不删除
        boolean areEqual = oldPO.getResourceJson().stream().sorted()
            .collect(Collectors.toList()).equals(newPO.getResourceJson().stream().sorted().collect(Collectors.toList()));
        if(!areEqual){
            List<UpmRole> roleList = rolePOList.stream().map(rolePO ->
                new UpmRole().setId(rolePO.getId()).setAuthKey(rolePO.getAuthKey()).setResourceIdList(newPO.getResourceJson()))
                .collect(Collectors.toList());
            for (UpmRole upmRole : roleList) {
                roleDomainService.fspChangeResource(upmRole);
            }
        }

    }

    public void deleteAuthRoleBatch(List<Long> companyIdList,UpmAuthTypeRoleTemplatePO roleTemplatePO, UpmAuthTypeTemplatePO templatePO){
        List<String> authKeyList = companyIdList.stream().map(companyId ->
            AuthKeyUtil.getAuthKey(templatePO.getAuthType(),templatePO.getSystemId(),ssoAuthProperties.getPlatformTenantId(),companyId)).collect(Collectors.toList());
        roleDomainService.deleteAuthKeyAndCode(authKeyList,roleTemplatePO.getRoleCode());
    }



}

