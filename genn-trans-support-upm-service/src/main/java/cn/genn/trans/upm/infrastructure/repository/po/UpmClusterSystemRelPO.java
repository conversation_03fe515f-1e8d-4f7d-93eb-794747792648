package cn.genn.trans.upm.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * UpmClusterSystemRelPO对象
 *
 * <AUTHOR>
 * @desc 集群与系统关联表
 */
@Data
@Accessors(chain = true)
@TableName(value = "upm_cluster_system_rel", autoResultMap = true)
public class UpmClusterSystemRelPO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 集群分组（default；）
     */
    @TableField("vpc_group")
    private String vpcGroup;

    /**
     * 集群名称
     */
    @TableField("cluster_name")
    private String clusterName;

    /**
     * 系统id
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 系统code
     */
    @TableField("system_code")
    private String systemCode;

    /**
     * 集群域名
     */
    @TableField("domain")
    private String domain;

    /**
     * 内网域名
     */
    @TableField("internal_domain")
    private String internalDomain;

    /**
     * 删除标记（0：未删除，1已删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}

