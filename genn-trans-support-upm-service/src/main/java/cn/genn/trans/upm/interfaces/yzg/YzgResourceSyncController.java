package cn.genn.trans.upm.interfaces.yzg;

import cn.genn.trans.upm.application.assembler.UpmResourceDTOAssembler;
import cn.genn.trans.upm.application.service.action.UpmResourceActionService;
import cn.genn.trans.upm.domain.upm.model.entity.UpmResource;
import cn.genn.trans.upm.domain.upm.repository.ResourceRepository;
import cn.genn.trans.upm.interfaces.command.yzg.YzgPermissionSyncCmd;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资源管理
 * <AUTHOR>
 */
@Api(tags = "yzg资源同步")
@RestController
@RequestMapping("/yzgResource")
public class YzgResourceSyncController {

    @Resource
    private UpmResourceActionService actionService;

    @Resource
    private UpmResourceDTOAssembler resourceAssembler;

    @Resource
    private ResourceRepository resourceRepository;


    @PostMapping("/sync")
    @ApiOperation(value = "同步资源")
    public List<UpmResourceDTO> syncResourceByYzg(@RequestBody YzgPermissionSyncCmd syncCmd) {
        return resourceAssembler.entity2DTO(actionService.syncResourceByYzg(syncCmd));
    }

    @PostMapping("/queryList")
    @ApiOperation(value = "获取资源")
    public List<UpmResourceDTO> queryList(@RequestBody List<Long> systemIds) {
        List<UpmResource> resourceList = resourceRepository.findListBySystemId(systemIds);
        return resourceAssembler.entity2DTO(resourceList);
    }
}

