package cn.genn.trans.upm.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.trans.upm.infrastructure.repository.po.UpmClusterSystemRelPO;
import cn.genn.trans.upm.interfaces.dto.UpmClusterSystemRelDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ClusterSystemRelAssembler  extends QueryAssembler<Object, UpmClusterSystemRelPO, UpmClusterSystemRelDTO> {
}
