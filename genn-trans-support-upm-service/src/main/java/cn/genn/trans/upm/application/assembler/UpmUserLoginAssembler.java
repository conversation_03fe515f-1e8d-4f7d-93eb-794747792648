package cn.genn.trans.upm.application.assembler;

import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
import cn.genn.trans.upm.interfaces.dto.UserAccountBindUserDTO;
import cn.genn.trans.upm.interfaces.dto.UserAccountInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Date: 2024/4/16
 * @Author: kangjian
 */
@Mapper
public interface UpmUserLoginAssembler {

    UpmUserLoginAssembler INSTANCE = Mappers.getMapper(UpmUserLoginAssembler.class);

    LoginUserAccountDTO userAccountInfoDTO2LoginUserAccountDTO(UserAccountInfoDTO userAccountInfoDTO);

    @Mapping(source = "id", target = "userId")
    UserAccountBindUserDTO upmUser2UserAccountBindUserDTO(UpmUser upmUser);

    List<UserAccountBindUserDTO> upmUserList2UserAccountBindUserDTOList(List<UpmUser> upmUserList);

}
