package cn.genn.trans.upm.domain.upm.repository;

import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserRolePO;

import java.util.List;

public interface UserRoleRepository {

    /**
     * 关联角色
     */
    Boolean relatedRole(UpmUser upmUser, boolean clearSign);

    Boolean batchRelatedRoleBySystemType(List<UpmUser> userList, boolean clearSign);

    Boolean saveBatch(List<UpmUserRolePO> userRolePOList);
}
