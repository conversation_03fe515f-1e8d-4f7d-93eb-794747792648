package cn.genn.trans.base.utils;

import cn.hutool.core.codec.Base64;
import org.springframework.stereotype.Component;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 */
@Component
public class SignUtils {
    private static final String FLAG_RSA = "RSA";
    private static final String SIGNATURE_ALGORITHM = "MD5withRSA";

    /**
     * 通过预制公钥生成PublicKey
     *
     * @param publicKey
     * @return
     * @throws Exception
     */
    private static PublicKey getPublicKey(String publicKey) throws Exception {
        byte[] encoded =  Base64.decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encoded);
        KeyFactory factory = KeyFactory.getInstance(FLAG_RSA);
        return factory.generatePublic(keySpec);
    }
    /**
     * 验证签名
     *
     * @param data
     *            原文
     * @param mySign
     *            签名
     * @return
     * @throws Exception
     */
    public static boolean verify(byte[] data, byte[] mySign,String publicKey) throws Exception {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(getPublicKey(publicKey));
        signature.update(data);
        return signature.verify(mySign);
    }
}
