package cn.genn.trans.base.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "genn.upm")
public class UpmSeverProperties {

    @NestedConfigurationProperty
    private LoginProperties login = new LoginProperties();

    // 默认短信验证码
    private String defaultSmsVerificationCode;

    //验证码有效时间,单位分钟
    private Long smsEffectiveTime = 1L;

    //fsp未审核超管默认菜单
    private List<Long> fspDefaultSuperAdminResources = new ArrayList<>();

    //禁止编辑的菜单,防止自己禁用自己的权限
    private List<Long> disableEditResources = new ArrayList<>();

    @NestedConfigurationProperty
    private MengSmsProperties mengSms = new MengSmsProperties();

    /**
     * 默认司机系统code
     */
    private String miniDriveCode = "APPLET-DRIVE";

    private String driveDefaultRole = "driver_role";

    /**
     * 默认游客角色code
     */
    private String defaultTmsTourist = "tms_tourist";

    /**
     * 默认密码
     */
    private String defaultPassword = "GenN@2651#";

    /**
     * 小程序默认密码
     */
    private String miniPassword = "Dht123!!";

    /**
     * 公众号账号默认密码
     */
    private String officialPassword = "GenN@2651#";

    /**
     * 超管默认角色code
     */
    private String superRoleCode = "SUPER_MANAGER";

    /**
     * 超管默认角色名称
     */
    private String superRoleName = "超级管理员";

    /**
     * 默认普通角色code
     */
    private String normalRoleCode = "0";

    /**
     * 超级管理员默认名称
     */
    private String defaultSuperNick = "超级管理员";


    @Data
    public static class LoginProperties {

        /**
         * 登录失败次数限制,默认5
         */
        private long failNum = 5L;
        /**
         * 登录失败记录时间(单位:分钟)
         */
        private long failEffectiveTime = 5L;
        /**
         * 冻结时间（单位：分钟）
         */
        private long freezeTime = 30L;

        /**
         * 小程序单点登录(默认true开启),生产禁止false
         */
        private boolean miniSingleLogin = true;

        /**
         * 移动端单点登录(默认true开启),生产禁止false
         */
        private boolean appSingleLogin = true;

        /**
         * 开启非单点登录配置(默认false关闭)
         */
        private boolean enableNonSingleSignOnConfig = false;
        /**
         * 开启非单点登录系统code列表 配合上面开关使用
         */
        private List<String> nonSingleSignOnSystemCodeList = new ArrayList<>();

    }

    @Data
    public static class MengSmsProperties {

        private String url;

        private String svcKey;
    }
}
