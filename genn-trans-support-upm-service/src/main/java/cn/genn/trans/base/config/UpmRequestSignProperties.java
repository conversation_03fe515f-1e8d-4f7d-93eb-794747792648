package cn.genn.trans.base.config;

import cn.genn.web.spring.component.sign.RequestSignProperties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "genn.upm.sign", ignoreInvalidFields = true)
public class UpmRequestSignProperties extends RequestSignProperties {


    private boolean enabled = false;



}
