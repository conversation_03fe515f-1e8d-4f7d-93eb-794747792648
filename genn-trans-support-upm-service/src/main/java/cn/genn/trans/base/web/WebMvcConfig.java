package cn.genn.trans.base.web;

import cn.genn.trans.base.config.UpmRequestSignProperties;
import cn.genn.trans.upm.interfaces.base.web.filter.SsoAuthFilter;
import cn.genn.trans.upm.interfaces.base.web.filter.SsoLoginFilter;
import cn.genn.web.spring.component.sign.RequestSignInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig extends WebMvcConfigurerAdapter {
    @Resource
    private UpmRequestSignProperties requestSignProperties;
    @Resource
    @Lazy
    private SsoLoginFilter ssoLoginFilter;
    @Resource
    @Lazy
    private SsoAuthFilter ssoAuthFilter;

    /**
     * 拦截白名单
     */
    private static final String[] IGNORE_PATHS = {"/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**", "/doc.html", "/favicon.ico", "/error"};


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(ssoLoginFilter).addPathPatterns("/**").excludePathPatterns(IGNORE_PATHS);
        registry.addInterceptor(ssoAuthFilter).addPathPatterns("/**").excludePathPatterns(IGNORE_PATHS);
        registry.addInterceptor(new RequestSignInterceptor(requestSignProperties)).addPathPatterns("/**");
    }

}
