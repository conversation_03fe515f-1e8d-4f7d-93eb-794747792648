package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpmUserRolePageQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "账号")
    private String username;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "员工名称")
    private String nick;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "员工角色id")
    private Long roleId;

    @ApiModelProperty(value = "修改时间段-前")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTimeBefore;

    @ApiModelProperty(value = "修改时间段-后")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTimeAfter;
}
