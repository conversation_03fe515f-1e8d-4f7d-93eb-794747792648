package cn.genn.trans.upm.interfaces.api;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.interfaces.command.fsp.ReviewAfterCommand;
import cn.genn.trans.upm.interfaces.command.fsp.SaveAfterCommand;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.dto.fsp.SuperUserDTO;
import cn.genn.trans.upm.interfaces.query.fsp.SuperUserPageQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 保理平台组织相关feign
 *
 * <AUTHOR>
 * @date 2024/7/28
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IFspAuthService", path = "/api/upm/fsp", url = "")
public interface IFspAuthService {

    @PostMapping("/page")
    @ApiOperation("查询超管用户")
    PageResultDTO<SuperUserDTO> page(@RequestBody SuperUserPageQuery query);

    @PostMapping("/saveAfter")
    @ApiOperation("新增组织后续处理")
    UpmUserDTO saveAfter(@RequestBody @Validated SaveAfterCommand command);

    @PostMapping("/reviewAfter")
    @ApiOperation("审核组织后续")
    Boolean reviewAfter(@RequestBody @Validated ReviewAfterCommand command);

    @PostMapping("/delete")
    @ApiOperation("删除")
    Boolean delete(@RequestBody List<Long> userIdList);
}
