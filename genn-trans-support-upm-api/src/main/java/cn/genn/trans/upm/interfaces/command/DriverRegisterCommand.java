package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DriverRegisterCommand {

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String telephone;

    @ApiModelProperty(value = "昵称")
    @NotBlank(message = "昵称不能为空")
    private String nick;

    @ApiModelProperty(value = "平台", required = true)
    private String systemCode;

}
