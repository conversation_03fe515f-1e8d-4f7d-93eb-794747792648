package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.command.mini.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/1
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IMiniNotifyService", path = "/api/upm/mini/notify")
public interface IMiniNotifyService {

    @PostMapping("/chargebackReview")
    @ApiOperation("退单审核通知")
    Boolean chargebackReview(@Validated @RequestBody MiniNotifyCommand<ChargeBackReviewCommand> command);

    @PostMapping("/dispatchOrder")
    @ApiOperation("派单通知")
    Boolean dispatchOrder(@Validated @RequestBody MiniNotifyCommand<DispatchOrderCommand> command);

    @PostMapping("/vehicleAccess")
    @ApiOperation("车辆进场通知")
    Boolean vehicleAccess(@Validated @RequestBody MiniNotifyCommand<VehicleAccessCommand> command);

    @PostMapping("/expenseReview")
    @ApiOperation("费用审核通知")
    Boolean expenseReview(@Validated @RequestBody MiniNotifyCommand<ExpenseReviewCommand> command);
}
