package cn.genn.trans.upm.interfaces.utils;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
public class AuthKeyUtil {

    private static final String DELIMITER = "-";

    public static String getAuthKey(AuthGroupEnum authGroup,Long systemId, Long tenantId, Long originId) {
        return authGroup.getCode() + DELIMITER + systemId + DELIMITER + tenantId + DELIMITER + originId;
    }

    public static String getPlatformKey(Long systemId, Long tenantId) {
        return AuthGroupEnum.PLATFORM.getCode() + DELIMITER + systemId + DELIMITER + tenantId;
    }

    public static String getCereroKey(Long systemId, Long tenantId) {
        if (tenantId == 1L) {
            return getPlatformKey(systemId, tenantId);
        }
        return AuthGroupEnum.OPERATOR.getCode() + DELIMITER + systemId + DELIMITER + tenantId;
    }

    public static String getOperatorKey(Long systemId, Long tenantId, Long operatorId) {
        return AuthGroupEnum.OPERATOR.getCode() + DELIMITER + systemId + DELIMITER + tenantId + DELIMITER + operatorId;
    }

    public static String getCarrierKey(Long systemId, Long tenantId, Long carrierId) {
        return AuthGroupEnum.CARRIER.getCode() + DELIMITER + systemId + DELIMITER + tenantId + DELIMITER + carrierId;
    }

    public static String getClientKey(Long systemId, Long tenantId, Long clientId) {
        return AuthGroupEnum.CLIENT.getCode() + DELIMITER + systemId + DELIMITER + tenantId + DELIMITER + clientId;
    }

    public static AuthGroupEnum getAuthGroupEnum(String authKey){
        if(StrUtil.isBlank(authKey) || !authKey.contains(DELIMITER)){
            return null;
        }
        String code = Arrays.asList(authKey.split(DELIMITER)).get(0);
        return AuthGroupEnum.of(code);
    }

    public static Long getSystemId(String authKey){
        if(StrUtil.isBlank(authKey) || !authKey.contains(DELIMITER)){
            return null;
        }
        List<String> list = Arrays.asList(authKey.split(DELIMITER));
        if(list.size()<=1){
            return null;
        }
        return Long.parseLong(list.get(1));
    }

    public static Long getTenantId(String authKey){
        if(StrUtil.isBlank(authKey) || !authKey.contains(DELIMITER)){
            return null;
        }
        List<String> list = Arrays.asList(authKey.split(DELIMITER));
        if(list.size()<=2){
            return null;
        }
        return Long.parseLong(list.get(2));
    }

    public static Long getOriginId(String authKey){
        if(StrUtil.isBlank(authKey) || !authKey.contains(DELIMITER)){
            return null;
        }
        List<String> list = Arrays.asList(authKey.split(DELIMITER));
        if(list.size()<=3){
            return null;
        }
        return Long.parseLong(list.get(3));
    }
}
