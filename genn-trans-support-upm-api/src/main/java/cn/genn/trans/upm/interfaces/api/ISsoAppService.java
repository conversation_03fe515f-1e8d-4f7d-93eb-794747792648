package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.dto.mini.MiniLoginUserDTO;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppLoginQuery;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppSmsLoginQuery;
import cn.genn.trans.upm.interfaces.query.app.SsoAccountAppUsernameQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "ISsoAppService", path = "/api/upm/sso/app", url = "${genn.upm.auth.upmServerUrl:}")
public interface ISsoAppService {

    @PostMapping("/oneClickLogin")
    @ApiOperation("移动端本机一键登录")
    MiniLoginUserDTO oneClickLogin(@Validated @RequestBody SsoAccountAppLoginQuery query);

    @PostMapping("/smsLogin")
    @ApiOperation("移动端短信验证码登录")
    MiniLoginUserDTO smsLogin(@Validated @RequestBody SsoAccountAppSmsLoginQuery query);

    @PostMapping("/accountLogin")
    @ApiOperation("移动端账号密码登录")
    MiniLoginUserDTO accountLogin(@Validated @RequestBody SsoAccountAppUsernameQuery query);
}
