package cn.genn.trans.upm.interfaces.query.feishu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@Accessors(chain = true)
public class SsoAccountFeishuSmsLoginQuery {
    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String telephone;
    @ApiModelProperty(value = "短信验证码", required = true)
    @NotBlank(message = "短信验证码不能为空")
    private String smsVerificationCode;
    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String systemCode;
    @ApiModelProperty(value = "小程序appid", required = true)
    @NotBlank(message = "小程序appid不能为空")
    private String appid;
    @ApiModelProperty(value = "临时登录code", required = true)
    private String loginCode;
}
