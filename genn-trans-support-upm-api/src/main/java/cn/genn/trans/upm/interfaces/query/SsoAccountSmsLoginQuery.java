package cn.genn.trans.upm.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @Date: 2024/4/22
 * @Author: kang<PERSON>an
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SsoAccountSmsLoginQuery {
    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String telephone;
    @ApiModelProperty(value = "短信验证码", required = true)
    @NotBlank(message = "短信验证码不能为空")
    private String smsVerificationCode;
    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String systemCode;
    @ApiModelProperty(value = "图片验证码")
    private String verificationCode;
    @ApiModelProperty(value = "跳过验证码校验标识")
    private boolean skipVerificationFlag;

}
