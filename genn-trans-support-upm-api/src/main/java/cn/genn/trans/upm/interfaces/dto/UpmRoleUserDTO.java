package cn.genn.trans.upm.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpmRoleUserDTO extends UpmRoleDTO {

    @ApiModelProperty(value = "系统id")
    private Long userId;
}
