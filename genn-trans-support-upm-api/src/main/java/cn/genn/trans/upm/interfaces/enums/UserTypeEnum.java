package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
@Getter
@AllArgsConstructor
public enum UserTypeEnum {

    SYSTEM("system", "系统用户"),
    TENANT("tenant", "租户用户"),
        ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, UserTypeEnum> VALUES = new HashMap<>();
    static {
        for (final UserTypeEnum item : UserTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static UserTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
