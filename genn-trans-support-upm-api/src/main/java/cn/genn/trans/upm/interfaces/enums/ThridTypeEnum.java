package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户关联三方类型
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Getter
@AllArgsConstructor
public enum ThridTypeEnum {

    WX("wx", "微信"),
    OFFICIAL_ACCOUNT("official_account", "公众号"),
    APP("app", "移动端"),
    FS("fs", "飞书"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, LoginTypeEnum> VALUES = new HashMap<>();
    static {
        for (final LoginTypeEnum item : LoginTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static LoginTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
