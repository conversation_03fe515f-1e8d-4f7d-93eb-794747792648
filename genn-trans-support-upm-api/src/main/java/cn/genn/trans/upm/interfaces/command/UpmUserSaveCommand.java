package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class UpmUserSaveCommand {

    @ApiModelProperty(value = "账号")
    @NotBlank(message = "账号不能为空")
    @Size(max=32,message="名称长度不能大于32")
    private String username;

    @ApiModelProperty(value = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "昵称")
    private String nick;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "邮箱")
    @Pattern(regexp="^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message="邮箱格式错误")
    private String email;

    @ApiModelProperty(value = "关联角色id")
    private List<Long> roleIdList;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "销售名称")
    private String saleName;

    @ApiModelProperty(value = "账号有效期")
    private LocalDateTime effectiveTime;
}
