package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UpmUserResetPasswordBySystemTypeCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "系统id不能为空")
    private Long id;

    @ApiModelProperty(value = "新密码")
    @NotBlank(message = "密码不能为空")
    private String newPassword;

    @ApiModelProperty(value = "系统类型")
    @NotNull(message = "系统类型不能为空")
    private SystemTypeEnum systemType;

    @ApiModelProperty(value = "权限组")
    @NotNull(message = "权限组不能为空")
    private AuthGroupEnum authGroup;

    @ApiModelProperty(value = "源id")
    @NotNull(message = "源id不能为空")
    private Long originId;
}
