package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 注册参数
 *
 * <AUTHOR>
 * @date 2024/5/29
 */
@Data
public class UserRegisTerCommand {

    @ApiModelProperty(value = "系统id")
    @NotNull(message = "系统id不能为空")
    private Long systemId;

    @ApiModelProperty(value = "账号")
    @NotBlank(message = "账号不能为空")
    @Size(max=32,message="名称长度不能大于32")
    private String username;

    @ApiModelProperty(value = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value = "确认密码")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    @ApiModelProperty(value = "昵称")
    private String nick;

    @ApiModelProperty(value = "手机号")
    private String telephone;

    @ApiModelProperty(value = "验证码")
    private String captcha;


}
