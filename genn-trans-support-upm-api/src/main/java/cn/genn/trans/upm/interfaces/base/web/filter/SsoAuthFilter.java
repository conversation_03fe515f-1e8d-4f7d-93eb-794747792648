package cn.genn.trans.upm.interfaces.base.web.filter;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.upm.interfaces.api.ISsoAuthClient;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.exception.AuthException;
import cn.genn.trans.upm.interfaces.base.web.exception.MessageCode;
import cn.genn.trans.upm.interfaces.base.web.properties.SsoAuthProperties;
import cn.genn.trans.upm.interfaces.query.UpmCheckUriPermissionQuery;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * SsoAuthenticationFilter类用于处理单点登录（SSO）授权过滤。
 * 该类的主要作用是检查请求是否来自经过授权的用户，
 * 如果未授权，则阻止访问。
 *
 * @Date: 2024/4/24
 * @Author: kangjian
 */
@Component
@Slf4j
public class SsoAuthFilter implements HandlerInterceptor, Ordered {

    public static final String TOKEN = "token";
    /**
     * 魔法登录 header头信息模拟登录
     */
    public static final String MAGIC_TOKEN = "magic-token";
    private final PathMatcher pathMatcher = (PathMatcher) new AntPathMatcher();

    private SsoAuthProperties ssoAuthProperties;

    @Value(value = "${server.servlet.context-path}")
    private String contextPath;

    public SsoAuthFilter(SsoAuthProperties ssoAuthProperties) {
        this.ssoAuthProperties = ssoAuthProperties;
    }

    /**
     * @param request  current HTTP request
     * @param response current HTTP response
     * @param handler  chosen handler to execute, for type and/or instance evaluation
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!ssoAuthProperties.getPermission().isEnable()) {
            return true;
        }
        String token = request.getHeader(TOKEN);
        String magicToken = request.getHeader(MAGIC_TOKEN);
        String requestUri = request.getRequestURI();

        try {
            // 无需拦截的url
            if (filterIgnoreAccess(requestUri)) {
                log.debug("SsoAuthFilter ignore uri auth requestUri={}", requestUri);
                return true;
            }
            // 魔法登录 从magicToken中提取数据构造登录态
            boolean magicLogin = StrUtil.isNotBlank(magicToken);

            if (!magicLogin && StrUtil.isBlank(token)) {
                throw new AuthException(MessageCode.TOKEN_GET_ERROR);
            }

            log.info("SsoAuthFilter auth requestUri={} token={} magic-token={}", requestUri, token, magicToken);
            SsoUserAuthInfoDTO userAuthInfoDTO = CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.get();
            log.info("SsoAuthFilter userAuthInfoDTO from CurrentUserHolder dto={}", JsonUtils.toJson(userAuthInfoDTO));
            // 校验用户资源权限
            if (checkUserPermission(userAuthInfoDTO, requestUri)) {
                log.info("SsoAuthFilter auth success");
                return true;
            }
            log.info("SsoAuthFilter auth fail");
            String error = String.format("request uri：%s，userId：%d，token：%s  鉴权不通过", requestUri, userAuthInfoDTO.getUserId(), token);
            throw new AuthException(MessageCode.TOKEN_GET_ERROR.getCode(), error);
        } catch (AuthException ex) {
            log.warn("SsoAuthFilter error ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("SsoAuthFilter error ", ex);
            throw new AuthException(MessageCode.AUTH_FAIL.getCode(), "鉴权过滤器异常: " + ex.getMessage());
        }
    }

    // 调用upm服务进行casbin规则的校验
    private boolean checkUserPermission(SsoUserAuthInfoDTO userAuthInfoDTO, String requestUri) {
        if (userAuthInfoDTO.isSuperAdmin()) {
            log.info("super admin don not need check permission");
            return true;
        }
        UpmCheckUriPermissionQuery query = UpmCheckUriPermissionQuery.builder().userId(userAuthInfoDTO.getUserId()).uri(requestUri).build();
        boolean result = SpringUtil.getBean(ISsoAuthClient.class).checkUriPermission(query);
        log.info("checkUserPermission requestUri={} userId={} check_result={}", requestUri, userAuthInfoDTO.getUserId(), result);
        return result;
    }

    private boolean filterIgnoreAccess(String requestUri) {
        // for (String ignoreUrl : ssoAuthProperties.getPermission().getExcludePatterns()) {
        //     if (pathMatcher.match(ignoreUrl, requestUri)) {
        //         return true;
        //     }
        // }
        String requestUriSub = "";
        if (requestUri.startsWith(contextPath)) {
            requestUriSub = requestUri.substring(contextPath.length());
        }
        for (String ignoreUrl : ssoAuthProperties.getDefaultUris()) {
            if (pathMatcher.match(ignoreUrl, requestUri) || pathMatcher.match(ignoreUrl, requestUriSub)) {
                return true;
            }
        }
        for (String ignoreUrl : ssoAuthProperties.getPermission().getExcludePatterns()) {
            if (pathMatcher.match(ignoreUrl, requestUri) || pathMatcher.match(ignoreUrl, requestUriSub)) {
                return true;
            }
        }

        return false;
    }


    @Override
    public int getOrder() {
        return -99;
    }
}
