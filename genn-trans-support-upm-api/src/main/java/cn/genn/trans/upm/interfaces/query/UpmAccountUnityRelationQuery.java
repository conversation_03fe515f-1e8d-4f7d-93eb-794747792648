package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * UpmAccountUnityRelation查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpmAccountUnityRelationQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiParam(value = "主键")
    private Long id;

    @ApiParam(value = "统一登录账号id")
    private Long unityAccountId;

    @ApiParam(value = "默认账号id")
    private Long defaultAccountId;

    @ApiParam(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiParam(value = "创建用户ID")
    private Long createUserId;

    @ApiParam(value = "创建用户名称")
    private String createUserName;


}

