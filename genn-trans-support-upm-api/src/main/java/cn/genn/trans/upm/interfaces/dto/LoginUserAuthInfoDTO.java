package cn.genn.trans.upm.interfaces.dto;

import cn.genn.trans.upm.interfaces.dto.feishu.FsDepartmentDTO;
import cn.genn.trans.pms.interfaces.enums.OperatorTypeEnum;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.ClientTypeEnum;
import cn.genn.trans.upm.interfaces.enums.PasswrodStatusEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * token中缓存的相应用户所有权限信息
 *
 * @Date: 2024/4/16
 * @Author: kangjian
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LoginUserAuthInfoDTO implements Serializable {
    private String token;
    /**
     * 系统类型  平台 运营商 承运商
     */
    private SystemTypeEnum systemType;

    private Long accountId;

    private String username;

    private String telephone;

    private String email;

    private Long userId;

    private PasswrodStatusEnum passwordStatus;

    private LocalDateTime pdUpdateTime;

    private ClientTypeEnum clientType = ClientTypeEnum.WEB;

    /**
     * AES加密所需要的密钥
     */
    private String secretKey;
    private String ticket;

    private String userDeviceIdentify;

    /**
     * 租户信息
     */
    private Long tenantId;
    private String tenantName;
    private String tenantCode;
    private String vpcGroup;

    /**
     * 系统信息
     */
    private Long systemId;
    private String systemName;
    private String systemCode;

    /**
     * 运营商相关信息
     */
    private Long operatorId;
    private String operatorName;
    private String operatorCode;

    /**
     * 承运商相关信息
     */
    private Long carrierId;
    private String carrierName;
    private String carrierCode;
    /**
     * 当前承运商和运营商的合作关系
     */
    private Long operatorCarrierRelId;
    /**
     * 司机相关信息
     */
    private Long driverId;
    private String driverName;
    /**
     * 当前司机和承运商的合作关系
     */
    private Long driverCarrierRelId;

    /**
     * 角色信息
     */
    private List<String> roles;

    /**
     * 权限信息
     */
    private String authKey;
    private AuthGroupEnum authGroup;
    private Set<String> permissions;

    /**
     * 系统角色资源清单
     */
    private List<UpmResourceTreeDTO> resourceTree;

    /**
     * 小程序信息
     */
    private String sessionKey;
    private String unionId;
    private String openId;
    private String appId;

    /**
     * 用户个人信息
     */

    private String nick;
    private String avatar;
    private String remark;

    /**
     * 用户飞书信息
     */
    private String fsAppId;
    private String fsOpenId;
    private String fsAvatarUrl;
    private List<FsDepartmentDTO> fsDepartments;

    /**
     * 保理
     */
    private Long companyId;

    /**
     * 站点信息
     */
    private Long stationId;
    private String stationName;
    private Long memberId;
    private String memberNo;

    private OperatorTypeEnum operatorType;
}
