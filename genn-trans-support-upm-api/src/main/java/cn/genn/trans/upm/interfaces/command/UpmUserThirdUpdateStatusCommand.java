package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.ThridTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


@Data
public class UpmUserThirdUpdateStatusCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "第三方系统id")
    @NotNull(message = "第三方系统id不能为空")
    private String appId;

    @ApiModelProperty(value = "第三方系统类型")
    @NotNull(message = "第三方系统类型不能为空")
    private ThridTypeEnum type;

    @ApiModelProperty(value = "关闭用户id")
    private List<Long> closeUserId;

    @ApiModelProperty(value = "新建用户id")
    private Long newUserId;

    @ApiModelProperty(value = "新建用户openId")
    private String newOpenId;


}

