package cn.genn.trans.upm.interfaces.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 账号信息
 * @Date: 2024/4/16
 * @Author: kang<PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class UserAccountInfoDTO {

    private Long accountId;

    private String username;
    private String password;

    private String telephone;
    private String salt;
}
