package cn.genn.trans.upm.interfaces.dto;

import cn.genn.trans.upm.interfaces.enums.TenantTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账号绑定用户
 * @Date: 2024/4/16
 * @Author: kang<PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserAccountBindUserDTO {

    private Long userId;
    private Long accountId;

    private Long tenantId;
    private String tenantName;
    private String tenantCode;
    private TenantTypeEnum tenantType;

    private Long systemId;
    private String systemName;
    private String systemCode;
}
