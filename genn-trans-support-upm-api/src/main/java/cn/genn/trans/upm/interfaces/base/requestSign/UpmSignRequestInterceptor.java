package cn.genn.trans.upm.interfaces.base.requestSign;

import cn.genn.security.constant.SignAlgorithm;
import cn.genn.security.sign.SignatureAlgorithm;
import cn.genn.security.sign.SignatureAlgorithmImpl;
import cn.hutool.core.util.IdUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;


/**
 * <AUTHOR>
 */
//@Component
@Slf4j
public class UpmSignRequestInterceptor implements RequestInterceptor {

    private final UpmRequestSignGenerator signGenerator;

    public UpmSignRequestInterceptor(UpmRequestSignGenerator signGenerator) {
        this.signGenerator = signGenerator;
    }

    @Override
    public void apply(RequestTemplate template) {
        long timestamp = System.currentTimeMillis();
        String nonce = IdUtil.fastSimpleUUID();

//        generateAuthHeader();

        // 生成签名
//        String signature = signGenerator.generateSignature(nonce, timestamp);

        // 将签名和必要的身份标识（如apiKey）添加到请求Header中
//        template.header("X-Signature", signature);
    }

    public static String generateAuthHeader(String appId, String appSecret,
                                            HttpMethod httpMethod,
                                            String requestPath) {
        SignatureAlgorithm gennSignatureAlgorithm = new SignatureAlgorithmImpl(SignAlgorithm.HmacSHA256);
        String sign = gennSignatureAlgorithm.makeSign(() -> {
            List<String> sourceList = new ArrayList<>();
            return String.join("\n", sourceList);
        }, appSecret);
        return "GENN-HUB" + " " + appId + ":" + sign;
    }

    public static void handleRequest(RequestTemplate template) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String name = headerNames.nextElement();
                    String values = null;
                    if ("username".equalsIgnoreCase(name)) {
                        values = request.getHeader(name);
                    }
                    if (StringUtils.isNotBlank(values)) {
                        template.header(name, values);
                    }
                }
            }
        }
    }


}
