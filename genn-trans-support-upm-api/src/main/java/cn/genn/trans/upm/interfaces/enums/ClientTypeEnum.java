package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ClientTypeEnum {

    WEB("web","网页"),
    MINI("mini","微信小程序"),
    APP("app","移动端"),
    OFFICIAL_ACCOUNT("official_account","公众号"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, ClientTypeEnum> VALUES = new HashMap<>();

    static {
        for (final ClientTypeEnum item : ClientTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ClientTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
