package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.command.CheckVerificationCodeCommand;
import cn.genn.trans.upm.interfaces.command.UpmSendSmsCommand;
import cn.genn.trans.upm.interfaces.command.feishu.UpmFSUserCommand;
import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.UserTokenDTO;
import cn.genn.trans.upm.interfaces.dto.captcha.CaptchaDTO;
import cn.genn.trans.upm.interfaces.query.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * sso资源接口
 *
 * @Date: 2024/4/18
 * @Author: kangjian
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "ISsoAuthClient", path = "/api/upm/sso", url = "${genn.upm.auth.upmServerUrl:}")
public interface ISsoAuthClient {
    @PostMapping("/account/login")
    @ApiOperation("账号登录")
    LoginUserAccountDTO auth(@RequestBody SsoAccountLoginQuery query,@RequestHeader(value = "vpcGroup",required = false) String vpcGroup);

    @PostMapping("/account/smsLogin")
    @ApiOperation("账号登录-短信验证码登录")
    LoginUserAccountDTO smsLogin(@RequestBody SsoAccountSmsLoginQuery query,@RequestHeader(value = "vpcGroup",required = false) String vpcGroup);

    @PostMapping("/account/sendSms")
    @ApiOperation("发送短信验证码")
    Boolean sendSms(@RequestBody UpmSendSmsCommand command);

    @PostMapping("/system/login")
    @ApiOperation("子系统用户登录token")
    UserTokenDTO systemLogin(@RequestBody SsoUserLoginQuery query);

    @PostMapping("/system/userInfo")
    @ApiOperation("子系统根据token获取用户信息")
    LoginUserAuthInfoDTO systemUserInfo(@RequestBody SsoUserTokenQuery query);

    @PostMapping("/system/tokenUserAuthInfo")
    @ApiOperation("根据token获取token缓存数据")
    SsoUserAuthInfoDTO tokenUserAuthInfo(@RequestBody SsoUserTokenQuery query);

    @PostMapping("/account/logout")
    @ApiOperation("登出")
    Boolean logout(@RequestBody SsoUserTokenQuery query);

    @PostMapping("/system/refreshToken")
    @ApiOperation("子系统token续约")
    Boolean refreshToken(@RequestBody SsoUserTokenQuery query);

    @PostMapping("/user/checkUriPermission")
    @ApiOperation(value = "校验用户是否拥有uri的操作权限")
    Boolean checkUriPermission(@RequestBody UpmCheckUriPermissionQuery query);

    @PostMapping("/user/generateToken")
    @ApiOperation("根据用户id生成token")
    String generateToken(@RequestParam(value = "userId") Long userId);

    @PostMapping("/getCaptcha")
    CaptchaDTO getCaptcha(@RequestBody CaptchaDTO data);

    @PostMapping("/checkCaptcha")
    CaptchaDTO checkCaptcha(@RequestBody CaptchaDTO data);

    @PostMapping("/checkVerificationCode")
    void checkVerificationCode(CheckVerificationCodeCommand command);

    @PostMapping("/feishu/agentLogin")
    LoginUserAccountDTO getLoginUserAccount(@RequestBody UpmFSUserCommand command);

    @PostMapping("/feishu/agentLoginWithoutRegister")
    @ApiOperation(value = "飞书三方登录",notes = "只能登录系统绑定过飞书账号，有可能飞书手机号与系统绑定手机号不相同,不注册账号")
    LoginUserAccountDTO getFsLoginUserAccount(@Validated @RequestBody UpmFSUserCommand command);

}
