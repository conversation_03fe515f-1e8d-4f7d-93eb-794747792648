package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 一个业务系统管理多个下级系统的权限
 */
@Data
@Accessors(chain = true)
public class UpmUserSaveBySystemTypeCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "账号")
    @NotBlank(message = "账号不能为空")
    @Size(max=32,message="名称长度不能大于32")
    private String username;

    @ApiModelProperty(value = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "昵称")
    private String nick;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "邮箱")
    @Pattern (regexp="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message="邮箱格式错误")
    private String email;

    @ApiModelProperty(value = "关联主角色id列表")
    private List<Long> mainRoleIds;




    @ApiModelProperty(value = "系统类型")
    @NotNull(message = "系统类型不能为空")
    private SystemTypeEnum systemType;

    @ApiModelProperty(value = "权限组")
    @NotNull(message = "权限组不能为空")
    private AuthGroupEnum authGroup;

    @ApiModelProperty(value = "源id")
    @NotNull(message = "源id不能为空")
    private Long originId;
}
