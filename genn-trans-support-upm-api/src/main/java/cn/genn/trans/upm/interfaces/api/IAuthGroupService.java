package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.command.AuthBuildChangeCommand;
import cn.genn.trans.upm.interfaces.command.AuthBuildCommand;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IAuthGroupService", path = "/api/upm/upmAuth", url = "")
public interface IAuthGroupService {

    @PostMapping("/builderAuth")
    @ApiOperation("新增承运商的后续操作")
    List<Long> builderAuth(@RequestBody @Validated AuthBuildCommand command);

    @PostMapping("/changeAuth")
    @ApiOperation("编辑承运商的后续操作")
    Boolean changeAuth(@RequestBody @Validated AuthBuildChangeCommand command);

}
