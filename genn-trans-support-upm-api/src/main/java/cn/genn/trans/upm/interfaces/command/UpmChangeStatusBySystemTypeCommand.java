package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class UpmChangeStatusBySystemTypeCommand implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotEmpty(message = "id列表不能为空")
    private List<Long> idList;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    @NotNull(message = "状态类型不能为空")
    private StatusEnum status;


    @ApiModelProperty(value = "系统类型")
    @NotNull(message = "系统类型不能为空")
    private SystemTypeEnum systemType;

    @ApiModelProperty(value = "权限组")
    @NotNull(message = "权限组不能为空")
    private AuthGroupEnum authGroup;

    @ApiModelProperty(value = "源id")
    @NotNull(message = "源id不能为空")
    private Long originId;
}
