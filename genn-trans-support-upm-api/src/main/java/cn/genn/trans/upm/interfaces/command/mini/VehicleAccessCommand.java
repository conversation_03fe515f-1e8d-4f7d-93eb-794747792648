package cn.genn.trans.upm.interfaces.command.mini;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class VehicleAccessCommand extends NotifyCommand{

    @ApiModelProperty("订单号")
    @NotBlank(message = "订单号不能为空")
    @Size(max=32,message="订单号长度不能大于32")
    private String orderNo;

    @ApiModelProperty("车牌号码")
    @NotBlank(message = "车牌号码不能为空")
    @Pattern(regexp="^[\\u4e00-\\u9fa5][a-zA-Z0-9]{0,6}[\\u4e00-\\u9fa5]$", message="车牌号码格式错误")
    private String plateNumber;

    @ApiModelProperty("时间")
    @NotNull(message = "时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime enterTime;

    @ApiModelProperty("截止时间")
    @NotNull(message = "截止时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty("备注")
    @NotBlank(message = "备注不能为空")
    // @Size(max=20,message="拒绝理由长度不能大于20")
    private String remark;
}
