package cn.genn.trans.upm.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class UserRoleQuery  implements Serializable {

    @ApiModelProperty(value = "角色编码")
    @NotBlank(message = "角色编码不能为空")
    private String roleCode;

    @ApiModelProperty(value = "权限组")
    @NotBlank(message = "权限组不能为空")
    private String authKey;
}
