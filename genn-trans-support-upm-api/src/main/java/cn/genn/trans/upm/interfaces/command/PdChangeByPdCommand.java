package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
@Accessors(chain = true)
public class PdChangeByPdCommand {

    @ApiModelProperty(value = "原始密码")
    @NotBlank(message = "原始密码不能为空")
    private String oldPassword;

    @ApiModelProperty(value = "密码")
    @NotBlank(message = "新密码不能为空")
    private String password;

    @ApiModelProperty(value = "确认密码")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

}
