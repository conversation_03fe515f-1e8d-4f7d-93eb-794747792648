package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 密码校验规则
 */
@Getter
@AllArgsConstructor
public enum PasswordCheckEnum {

    /**
     * 简单（仅数字，8-12位）:
     */
    EASY("easy","^[0-9]{8,12}$"),
    /**
     * 普通（数字和字母，8-12位）
     */
    NORMAL("normal","^[A-Za-z0-9]{8,12}$"),
    /**
     * 强化（数字、大小写字母，8-12位）
     */
    ENHANCED("enhanced","^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)[A-Za-z\\d]{8,12}$"),
    /**
     * 等保三级(默认)
     * 1. 必须包含8-12位字符；
     * 2. 需至少包含大写字母、小写字母、数字和特殊符号组合中的任意3种；
     * 3. 不能包含连续或相同的字符。
     */
    STRONG("strong","^(?=(.*[A-Z]){1})(?=(.*[a-z]){1})(?=(.*\\d){1})(?=(.*[@$!%*#?&]){1}).{8,12}$"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String regular;


    private static final Map<String, PasswordCheckEnum> VALUES = new HashMap<>();

    static {
        for (final PasswordCheckEnum item : PasswordCheckEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static PasswordCheckEnum of(String code) {
        return VALUES.get(code);
    }

}
