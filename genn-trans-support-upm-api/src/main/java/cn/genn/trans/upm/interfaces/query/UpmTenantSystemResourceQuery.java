package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageSortQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * UpmTenantSystemResource查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpmTenantSystemResourceQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键自增")
    private Long id;

    @ApiModelProperty(value = "租户系统关联表id")
    private Long tenantSystemId;

    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "逻辑删除（0：未删除；1：删除）")
    private DeletedEnum delete;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户ID")
    private Long createUserId;

    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private Long updateUserId;

    @ApiModelProperty(value = "更新用户名")
    private String updateUserName;


}

