package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class UpmUserUpdateBySystemTypeCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @NotNull(message = "用户id不能为空")
    private Long id;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "昵称")
    @Size(max = 32, message = "昵称最大长度不能超过32")
    private String nick;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "关联角色id")
    private List<Long> roleIdList;

    /**
     * 大部分是true;false用于未构建完成的超管用户
     */
    @ApiModelProperty(value = "更新是否清除原有角色")
    private Boolean clearSign;



    @ApiModelProperty(value = "系统类型")
    @NotNull(message = "系统类型不能为空")
    private SystemTypeEnum systemType;

    @ApiModelProperty(value = "权限组")
    @NotNull(message = "权限组不能为空")
    private AuthGroupEnum authGroup;

    @ApiModelProperty(value = "源id")
    @NotNull(message = "源id不能为空")
    private Long originId;
}
