package cn.genn.trans.upm.interfaces.query.mini;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 描述
 * @date 2024/6/20
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SsoAccountMiniWXLoginQuery {

    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String systemCode;

    @ApiModelProperty(value = "小程序appid", required = true)
    @NotBlank(message = "小程序appid不能为空")
    private String appid;

    @ApiModelProperty(value = "临时登录code", required = true)
    @NotBlank(message = "临时登录code不能为空")
    private String loginCode;

    @ApiModelProperty(value = "获取手机号code", required = true)
    @NotBlank(message = "telephoneCode不能为空")
    private String telephoneCode;
}
