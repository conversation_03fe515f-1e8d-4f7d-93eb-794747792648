package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.TenantTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * UpmTenant查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpmTenantQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    @Size(max = 32,message = "名称长度不能大于32")
    private String name;

    @ApiModelProperty(value = "编码")
    @Size(max = 32,message = "编码长度不能大于32")
    private String code;

    @ApiModelProperty(value = "系统id")
    @NotNull(message = "系统id不能为空")
    private Long systemId;

    @ApiModelProperty(value = "类型（platform：平台方, op:运营方，bp:）")
    private TenantTypeEnum type;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

}

