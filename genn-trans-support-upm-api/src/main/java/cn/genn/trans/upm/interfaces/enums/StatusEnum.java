package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum StatusEnum {

    ENABLE(1, "启用"),
    DISABLE(2, "停用");


    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    StatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, StatusEnum> VALUES = new HashMap<>();
    static {
        for (final StatusEnum item : StatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static StatusEnum of(int code) {
        return VALUES.get(code);
    }
}
