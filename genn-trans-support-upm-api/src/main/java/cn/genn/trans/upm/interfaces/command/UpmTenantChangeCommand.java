package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/11
 */
@Data
public class UpmTenantChangeCommand {

    @ApiModelProperty(value = "主键")
    @NotNull(message = "租户id不能为空")
    private Long id;

    @ApiModelProperty(value = "名称")
    @Size(max = 32, message = "租户名称最大长度不能超过32")
    private String name;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "备注")
    @Size(max = 256, message = "备注最大长度不能超过256")
    private String remark;
}
