package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/15
 */
@Data
@Accessors(chain = true)
public class AuthBuildChangeCommand {

    @ApiModelProperty(value = "开通系统和资源列表")
    @NotEmpty(message = "开通系统和资源列表不能为空")
    private List<SystemResourceCommand> systemResourceList;

    @ApiModelProperty("承运商id")
    @NotNull(message = "承运商id不能为空")
    private Long carrierId;

    @ApiModelProperty(value = "租户id")
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

}
