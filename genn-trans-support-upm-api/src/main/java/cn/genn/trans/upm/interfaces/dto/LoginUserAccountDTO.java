package cn.genn.trans.upm.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户账号登录后的账号信息
 *
 * @Date: 2024/4/16
 * @Author: ka<PERSON><PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "LoginUserAccountDTO", description = "用户账号登录后的账号信息")
public class LoginUserAccountDTO {
    @ApiModelProperty("账号id")

    private Long accountId;

    @ApiModelProperty("账号名称")
    private String username;

    @ApiModelProperty("手机号")
    private String telephone;

    /**
     * 为账号登录设备分配的唯一标识
     */
    @ApiModelProperty("设备标识")
    private String userDeviceIdentify;

    /**
     * 登录后的ticket码
     */
    @ApiModelProperty("ticket")
    private String ticket;

    /**
     * 账号绑定的所有用户供登录账号后选择
     */
    @ApiModelProperty("账号绑定的所有用户供登录账号后选择")
    private List<UserAccountBindUserDTO> bindUserList;
}
