package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import cn.genn.trans.upm.interfaces.enums.TenantTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/11
 */
@Data
public class UpmTenantSaveCommand {

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "租户名称不能为空")
    @Size(max = 32, message = "租户名称最大长度不能超过32")
    private String name;

    @ApiModelProperty(value = "租户编码")
    private String code;

    @ApiModelProperty(value = "类型（platform：平台方, op:运营方，bp:）")
    private TenantTypeEnum type;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "上级id")
    private Long pid;

    @ApiModelProperty(value = "备注")
    @Size(max = 256, message = "备注最大长度不能超过256")
    private String remark;

    @ApiModelProperty(value = "集群group")
    private String vpcGroup;
}
