package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/24
 */
@Getter
@AllArgsConstructor
public enum SystemTypeEnum {

    PLATFORM("platform","平台方"),
    OPERATOR("operator","运营商"),
    CARRIER("carrier","承运商"),
    DRIVER("driver","司机端"),
    STATION("station", "站端"),
    FLEET("fleet", "车队")
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, SystemTypeEnum> VALUES = new HashMap<>();

    static {
        for (final SystemTypeEnum item : SystemTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static SystemTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
