package cn.genn.trans.upm.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Date: 2024/4/22
 * @Author: ka<PERSON><PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SsoUserLoginQuery {
    @ApiModelProperty(value = "平台端")
    @NotBlank(message = "systemCode不能为空")
    private String systemCode;
    @ApiModelProperty(value = "ticket码")
    @NotBlank(message = "ticket码不能为空")
    private String ticket;
    @ApiModelProperty(value = "预定token")
    private String token;
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "userId不能为空")
    private Long userId;
    @ApiModelProperty(value = "用户设备标识")
    @NotBlank(message = "用户设备标识不能为空")
    private String userDeviceIdentify;

    /**
     * 扩展支持承运商登录和司机登录
     * 运营商端OPS 承运商端TMS 超管端PMS 司机端
     */
    /**
     * 运营商相关信息
     */
    private Long operatorId;
    private String operatorCode;
    private String operatorName;

    /**
     * 承运商相关信息
     */
    private Long carrierId;
    private String carrierName;
    /**
     * 当前承运商和运营商的合作关系
     */
    private Long operatorCarrierRelId;
    /**
     * 司机相关信息
     */
    private Long driverId;
    private String driverName;
    /**
     * 当前司机和承运商的合作关系
     */
    private Long driverCarrierRelId;

    /**
     * 站点信息
     */
    private Long stationId;

    private String stationName;

    private Long memberId;
    private String memberNo;

}
