package cn.genn.trans.upm.interfaces.dto;

import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusBooleanEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * UpmResourceTreeDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpmResourceTreeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "前端path")
    private String path;

    @ApiModelProperty(value = "首字母大写，一定要与vue文件的name对应起来，用于noKeepAlive缓存控制（该项特别重要）")
    private String name;

    @ApiModelProperty(value = "路由重定向")
    private String redirect;

    @ApiModelProperty(value = "前端组件路径")
    private String component;

    @ApiModelProperty(value = "meta属性")
    private MetaDTO meta;

    @ApiModelProperty(value = "子菜单")
    private List<UpmResourceTreeDTO> children;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MetaDTO implements Serializable {
        @ApiModelProperty(value = "主键")
        private Long id;

        @ApiModelProperty(value = "资源系统id")
        private Long systemId;

        @ApiModelProperty(value = "链接地址")
        private String url;

//        @ApiModelProperty(value = "资源编号")
//        private String code;

        @ApiModelProperty(value = "资源类型（1: 菜单，2：按钮）")
        private ResourceTypeEnum type;

        @ApiModelProperty(value = "排序")
        private Integer resourceSort;

        @ApiModelProperty(value = "上级菜单")
        private Long pid;

        @ApiModelProperty(value = "图标")
        private String icon;

        @ApiModelProperty(value = "菜单、面包屑、多标签页显示的名称")
        private String title;

        @ApiModelProperty(value = "状态（1：启用；2：停用）")
        private StatusEnum status;

        @ApiModelProperty(value = "菜单激活（指定激活菜单的path）")
        private String activePath;

        @ApiModelProperty(value = "按钮权限标识")
        private List<String> auths;

        @ApiModelProperty(value = "需要内嵌的iframe链接地址")
        private String frameSrc;

        @ApiModelProperty(value = "是否在菜单中显示（默认值：true）")
        private StatusBooleanEnum showLink;

        @ApiModelProperty(value = "是否显示父级菜单（默认值：true）")
        private StatusBooleanEnum showParent;

        @ApiModelProperty(value = "当前路由是否不缓存（默认值：false）")
        private StatusBooleanEnum keepAlive;

        @ApiModelProperty(value = "当前路由是否固定标签页（默认值：false）")
        private StatusBooleanEnum fixedTag;

        @ApiModelProperty(value = "当前菜单名称或自定义信息禁止添加到标签页（默认值：false）")
        private StatusBooleanEnum hiddenTag;

        @ApiModelProperty(value = "备注")
        private String remark;
    }

}

