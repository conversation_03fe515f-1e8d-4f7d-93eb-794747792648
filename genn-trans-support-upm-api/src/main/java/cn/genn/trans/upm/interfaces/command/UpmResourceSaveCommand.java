package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.DefaultSelectEnum;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusBooleanEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * UpmResource操作对象
 *
 * <AUTHOR>
 */
@Data
public class UpmResourceSaveCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "资源系统id", required = true)
    @NotNull(message = "系统id不能为空")
    private Long systemId;

    @ApiModelProperty(value = "资源类型（1: 菜单，2：按钮）", required = true)
    @NotNull(message = "资源类型不能为空")
    private ResourceTypeEnum type;

    @ApiModelProperty(value = "菜单、面包屑、多标签页显示的名称", required = true)
    @NotBlank(message = "资源title不能为空")
    private String title;

    @ApiModelProperty(value = "首字母大写，一定要与vue文件的name对应起来，用于noKeepAlive缓存控制（该项特别重要）")
    private String name;

    @ApiModelProperty(value = "排序")
    private Integer resourceSort;

    @ApiModelProperty(value = "链接地址")
    private String url;

    @ApiModelProperty(value = "前端path")
    private String path;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "上级菜单")
    private Long pid;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "按钮权限标识")
    private String auths;

    /*** 前端动态路由配置参数 start */
    @ApiModelProperty(value = "前端组件路径")
    private String component;

    @ApiModelProperty(value = "路由重定向")
    private String redirect;

    @ApiModelProperty(value = "选中状态")
    private DefaultSelectEnum defaultSelect;

    @ApiModelProperty(value = "菜单激活（指定激活菜单的path）")
    private String activePath;

    @ApiModelProperty(value = "需要内嵌的iframe链接地址")
    private String frameSrc;

    @ApiModelProperty(value = "是否在菜单中显示（默认值：true）")
    private StatusBooleanEnum showLink;

    @ApiModelProperty(value = "是否显示父级菜单（默认值：true）")
    private StatusBooleanEnum showParent;

    @ApiModelProperty(value = "当前路由是否不缓存（默认值：false）")
    private StatusBooleanEnum keepAlive;

    @ApiModelProperty(value = "当前路由是否固定标签页（默认值：false）")
    private StatusBooleanEnum fixedTag;

    @ApiModelProperty(value = "当前菜单名称或自定义信息禁止添加到标签页（默认值：false）")
    private StatusBooleanEnum hiddenTag;
    /*** 前端动态路由配置参数 end */

    @ApiModelProperty(value = "备注")
    private String remark;

}

