package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
@Data
@Accessors(chain = true)
public class AuthBuildCommand {

    @ApiModelProperty(value = "开通系统和资源列表")
    @NotEmpty(message = "开通系统和资源列表不能为空")
    private List<SystemResourceCommand> systemResourceList;

    @ApiModelProperty("管理员账号id")
    @NotNull(message = "管理员账号id不能为空")
    private Long superAccountId;

    @ApiModelProperty("承运商id")
    @NotNull(message = "承运商id不能为空")
    private Long carrierId;
}
