package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.mini.MiniLoginUserDTO;
import cn.genn.trans.upm.interfaces.query.SsoUserTokenQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniSmsLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoAccountMiniWXLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoUserTokenMiniQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "ISsoMiniService", path = "/api/upm/sso/mini", url = "${genn.upm.auth.upmServerUrl:}")
public interface ISsoMiniService {

    @PostMapping("/wxLogin")
    @ApiOperation("小程序微信授权登录")
    MiniLoginUserDTO miniWxLogin(@Validated @RequestBody SsoAccountMiniWXLoginQuery query);

    @PostMapping("/smsLogin")
    @ApiOperation("小程序短信验证码登录")
    MiniLoginUserDTO miniSmsLogin(@Validated @RequestBody SsoAccountMiniSmsLoginQuery query);

    @PostMapping("/accountLogin")
    @ApiOperation("小程序账号登录")
    MiniLoginUserDTO accountLogin(@Validated @RequestBody SsoAccountMiniLoginQuery query);

    @PostMapping("/userInfo")
    @ApiOperation("子系统根据token获取用户信息")
    LoginUserAuthInfoDTO miniUserInfo(@Validated @RequestBody SsoUserTokenMiniQuery query);

    @PostMapping("/logout")
    @ApiOperation("登出")
    Boolean logout(@Validated @RequestBody SsoUserTokenQuery query);

    @PostMapping("/refreshToken")
    @ApiOperation("子系统token续约")
    Boolean refreshToken(@Validated @RequestBody SsoUserTokenQuery query);

    @PostMapping("/logoff")
    @ApiOperation(value = "注销账号")
    Boolean logoff(@RequestParam("accountId") Long accountId);
}
