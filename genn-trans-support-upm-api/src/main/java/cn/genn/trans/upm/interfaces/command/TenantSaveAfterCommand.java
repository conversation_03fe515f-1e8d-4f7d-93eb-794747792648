package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Data
@Accessors(chain = true)
public class TenantSaveAfterCommand {

    @ApiModelProperty(value = "租户id")
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    @ApiModelProperty(value = "运营商id")
    @NotNull(message = "运营商id不能为空")
    private Long operatorId;

    @ApiModelProperty(value = "开通系统和资源列表")
    @NotEmpty(message = "开通系统和资源列表不能为空")
    private List<SystemResourceCommand> systemResourceList;

    @ApiModelProperty(value = "开通超管账号")
    @NotBlank(message = "超管账号不能为空")
    private String managerAccount;



}
