package cn.genn.trans.upm.interfaces.dto;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * UpmAuthTypeTemplateDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpmAuthTypeTemplateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "权限组类型：pl平台,op运营,ca承运,cl客户")
    private AuthGroupEnum authType;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "资源树json")
    private List<Long> resourceJson;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


}

