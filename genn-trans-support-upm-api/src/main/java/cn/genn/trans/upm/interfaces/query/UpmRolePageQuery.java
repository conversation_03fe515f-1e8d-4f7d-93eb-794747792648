package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/6
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpmRolePageQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "名称")
    @Size(max = 32, message = "名称长度不能大于32")
    private String name;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "角色ID")
    private Long id;

    @ApiModelProperty(value = "角色编号")
    private String code;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改时间段-前")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTimeBefore;

    @ApiModelProperty(value = "修改时间段-后")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTimeAfter;

    @ApiModelProperty(value = "用户ID列表")
    private List<Long> roleIds;
}
