package cn.genn.trans.upm.interfaces.query;

import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.genn.trans.upm.interfaces.enums.ThridTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Accessors(chain = true)
public class UserThirdSystemQuery {

    @ApiModelProperty("三方类型")
    @NotNull(message = "三方类型不能为空")
    private ThridTypeEnum thirdType;

    @ApiModelProperty("systemType")
    @NotNull(message = "systemType不能为空")
    private SystemTypeEnum systemType;

    @ApiModelProperty("userId列表")
    @NotEmpty(message = "用户id列表不能为空")
    private List<Long> userIdList;
}
