package cn.genn.trans.upm.interfaces.query.fsp;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 超管用户查询
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SuperUserPageQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "昵称")
    private String nick;

    @ApiModelProperty(value = "手机号")
    private String telephone;

    @ApiModelProperty(value = "状态")
    private StatusEnum status;

    private Long systemId;
}
