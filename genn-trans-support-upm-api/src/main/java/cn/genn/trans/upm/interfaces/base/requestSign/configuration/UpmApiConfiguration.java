package cn.genn.trans.upm.interfaces.base.requestSign.configuration;

import cn.genn.trans.upm.interfaces.base.requestSign.UpmRequestSignGenerator;
import cn.genn.trans.upm.interfaces.base.requestSign.UpmSignRequestInterceptor;
import feign.RequestInterceptor;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class UpmApiConfiguration {
    @Resource
    private UpmRequestSignGenerator signatureGenerator;

//    @Bean
    public RequestInterceptor upmSignatureRequestInterceptor() {
        return new UpmSignRequestInterceptor(signatureGenerator);
    }
}
