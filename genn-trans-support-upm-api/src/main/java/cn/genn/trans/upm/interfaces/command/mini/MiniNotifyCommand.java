package cn.genn.trans.upm.interfaces.command.mini;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 小程序通知
 *
 * <AUTHOR>
 * @date 2024/7/1
 */
@Data
@Accessors(chain = true)
public class MiniNotifyCommand<T extends NotifyCommand> {

    @ApiModelProperty("userId列表")
    @NotEmpty(message = "用户id列表不能为空")
    private List<Long> userIdList;

    @ApiModelProperty("通知内容")
    @NotNull(message = "通知内容不能为空")
    private T notifyCommand;

    @ApiModelProperty("通知跳转参数")
    private Map<String,Object> linkCommand;
}
