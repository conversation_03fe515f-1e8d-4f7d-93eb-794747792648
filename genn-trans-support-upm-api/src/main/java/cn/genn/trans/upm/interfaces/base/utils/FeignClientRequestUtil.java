package cn.genn.trans.upm.interfaces.base.utils;

import cn.genn.trans.upm.interfaces.query.SsoUserTokenQuery;

/**
 * @Date: 2024/4/28
 * @Author: kangjian
 */
public class FeignClientRequestUtil {

    public static String getToken(SsoUserTokenQuery query) {
        /*String json = JSONUtil.toJsonStr(query);
        String result2 = HttpRequest.post(query)
            .body(json)
            .execute().body();
        return null;*/
        return null;
    }

}
