package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.dto.ClusterDTO;
import cn.genn.trans.upm.interfaces.dto.UpmClusterSystemRelDTO;
import cn.genn.trans.upm.interfaces.dto.ClusterSystemDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/15
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IClusterService", path = "/api/upm/cluster")
public interface IClusterService {

    @GetMapping("/allClusterList")
    @ApiOperation("获取集群列表")
    List<ClusterDTO> queryAllClusterList();


    @GetMapping("/queryByTenantIdAndSystemId")
    @ApiOperation("租户id和系统id查指定集群")
    ClusterSystemDTO queryByTenantIdAndSystemId(@RequestParam("tenantId") Long tenantId, @RequestParam("systemId") Long systemId);

    @GetMapping("/getCluster")
    @ApiOperation("查询单个集群")
    UpmClusterSystemRelDTO getCluster(@RequestParam("vpcGroup") String vpcGroup, @RequestParam("systemId") Long systemId);
}
