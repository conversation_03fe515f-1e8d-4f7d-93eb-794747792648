package cn.genn.trans.upm.interfaces.command.official;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 微信公众号授权登录, 临时方案，相关授权在补能 wechat 项目完成。这里仅做 upm 登录注册
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsoAccountOfficialWXTempLoginCommand {

    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String systemCode;

    @ApiModelProperty(value = "租户id", required = true)
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    @ApiModelProperty(value = "公众号appId", required = true)
    @NotBlank(message = "公众号appId不能为空")
    private String appId;

    @ApiModelProperty(value = "openId", required = true)
    @NotBlank(message = "openId")
    private String openId;

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "telephone不能为空")
    private String telephone;
}
