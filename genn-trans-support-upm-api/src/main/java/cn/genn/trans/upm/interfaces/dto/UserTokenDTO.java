package cn.genn.trans.upm.interfaces.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户token信息
 * @Date: 2024/4/16
 * @Author: kang<PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class UserTokenDTO {

    private String token;
    private Long userId;

    public Long timeout;

    private Long activeTimeout;

    private String secretKey;
    private String vpcGroup;
}
