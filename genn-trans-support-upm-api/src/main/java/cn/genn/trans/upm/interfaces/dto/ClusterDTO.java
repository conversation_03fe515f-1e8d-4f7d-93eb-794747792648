package cn.genn.trans.upm.interfaces.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 租户集群数据
 *
 * <AUTHOR>
 * @date 2024/7/15
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ClusterDTO {

    private String vpcGroup;

    private String clusterName;

    private String domain;

    private String internalDomain;
}
