package cn.genn.trans.upm.interfaces.query;

import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024/5/28
 */
@Data
public class SystemQuery {

    @ApiModelProperty("id列表")
    private List<Long> idList;

    @ApiModelProperty("排查id列表")
    private List<Long> noIdList;

    @ApiModelProperty("指定系统类型(platform:平台方,operator:运营商,carrier:承运商)")
    private SystemTypeEnum systemType;

    @ApiModelProperty("排除系统类型(platform:平台方,operator:运营商,carrier:承运商)")
    private SystemTypeEnum noSystemType;



}
