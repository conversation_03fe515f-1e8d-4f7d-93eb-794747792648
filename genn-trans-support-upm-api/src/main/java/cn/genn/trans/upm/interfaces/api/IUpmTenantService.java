package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.command.TenantRegistrationChangeCommand;
import cn.genn.trans.upm.interfaces.command.TenantSaveAfterCommand;
import cn.genn.trans.upm.interfaces.command.UpmChangeStatusCommand;
import cn.genn.trans.upm.interfaces.command.UpmTenantSaveCommand;
import cn.genn.trans.upm.interfaces.dto.UpmTenantDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IUpmTenantService", path = "/api/upm/upmTenant")
public interface IUpmTenantService {

    @PostMapping("/listAll")
    @ApiOperation(value = "查询所有租户列表")
    List<UpmTenantDTO> listAll();

    @GetMapping("/list")
    @ApiOperation(value = "查询租户列表")
    List<UpmTenantDTO> list();

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    UpmTenantDTO get(@ApiParam(name = "id", required = true) @RequestParam Long id);

    @PostMapping("/save")
    @ApiOperation(value = "运营商添加租户")
    UpmTenantDTO save(@ApiParam(value = "操作类") @RequestBody @Validated UpmTenantSaveCommand command);

    @PostMapping("/saveAfter")
    @ApiOperation(value = "运营商添加租户后置操作")
    Boolean saveAfter(@ApiParam(value = "操作类") @RequestBody @Validated TenantSaveAfterCommand command);

    @PostMapping("/registration/change")
    @ApiOperation(value = "编辑租户信息：编辑租户，关联系统，关联资源")
    boolean changeRegistration(@RequestBody TenantRegistrationChangeCommand command);

    @PostMapping("/change/status")
    @ApiOperation(value = "批量启用禁用")
    boolean changeStatus(@RequestBody UpmChangeStatusCommand command);

    @PostMapping("delete")
    @ApiOperation(value = "删除租户")
    boolean delete(@RequestParam("id") Long id);


}
