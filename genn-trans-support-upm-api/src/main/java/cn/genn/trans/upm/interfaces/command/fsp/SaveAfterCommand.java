package cn.genn.trans.upm.interfaces.command.fsp;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 保理添加组织后续处理
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Data
@Slf4j
public class SaveAfterCommand {


    @ApiModelProperty(value = "组织类型")
    @NotNull(message = "组织类型不能为空")
    private AuthGroupEnum authGroup;

    @ApiModelProperty(value = "组织id")
    @NotNull(message = "组织id不能为空")
    private Long companyId;

    @ApiModelProperty(value = "超管账号")
    @NotBlank(message = "账号不能为空")
    private String username;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String telephone;

    @ApiModelProperty(value = "用户")
    @NotBlank(message = "用户不能为空")
    private String nick;
}
