package cn.genn.trans.upm.interfaces.command.mini;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 退单审核通知
 *
 * <AUTHOR>
 * @date 2024/7/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ChargeBackReviewCommand extends NotifyCommand{

    @ApiModelProperty("退单结果")
    @NotBlank(message = "退单结果不能为空")
    @Pattern(regexp="^[\\u4e00-\\u9fa5]{1,5}$", message="退单结果格式错误,只能包含5个以内汉字")
    private String result;

    @ApiModelProperty("温馨提示")
    @NotBlank(message = "温馨提示不能为空")
    // @Size(max=20,message="温馨提示长度不能大于20")
    private String prompt;

    @ApiModelProperty("拒绝理由")
    @NotBlank(message = "拒绝理由不能为空")
    // @Size(max=20,message="拒绝理由长度不能大于20")
    private String reason;

}
