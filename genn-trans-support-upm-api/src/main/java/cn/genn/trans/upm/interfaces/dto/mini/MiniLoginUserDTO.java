package cn.genn.trans.upm.interfaces.dto.mini;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@Data
@Accessors(chain = true)
public class MiniLoginUserDTO {

    private String token;

    private String secretKey;

    private Long userId;

    @ApiModelProperty("设备标识")
    private String userDeviceIdentify;

    private String telephone;

    @ApiModelProperty("首次登录标记,true:首次登录")
    private boolean firstLogin = false;
}
