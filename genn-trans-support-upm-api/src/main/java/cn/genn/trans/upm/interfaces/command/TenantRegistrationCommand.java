package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 租户入驻
 *
 * <AUTHOR>
 * @date 2024/5/11
 */
@Data
public class TenantRegistrationCommand {

    @ApiModelProperty(value = "租户信息")
    @NotNull(message = "租户信息不能为空")
    private UpmTenantSaveCommand tenantSaveCommand;

    @ApiModelProperty(value = "开通系统和资源列表")
    @NotEmpty(message = "开通系统和资源列表不能为空")
    private List<SystemResourceCommand> systemResourceList;

    @ApiModelProperty(value = "开通超管账号")
    @NotNull(message = "超管账号不能为空")
    private String managerAccount;

}
