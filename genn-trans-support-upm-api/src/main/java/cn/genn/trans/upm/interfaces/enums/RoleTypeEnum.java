package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum RoleTypeEnum {

    SYSTEM("system", "系统角色"),
    TENANT("tenant", "租户角色"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, RoleTypeEnum> VALUES = new HashMap<>();
    static {
        for (final RoleTypeEnum item : RoleTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RoleTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
