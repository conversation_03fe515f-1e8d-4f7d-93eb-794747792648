package cn.genn.trans.upm.interfaces.api;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.dto.UpmRoleDTO;
import cn.genn.trans.upm.interfaces.query.UpmRolePageQuery;
import cn.genn.trans.upm.interfaces.query.UpmRoleQuery;
import cn.genn.trans.upm.interfaces.query.UpmRoleResourceBySystemTypeQuery;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IUpmRoleService", path = "/api/upm/upmRole")
public interface IUpmRoleService {

    @PostMapping("/page")
    @ApiOperation(value = "分页查询角色")
    PageResultDTO<UpmRoleDTO> page(@RequestBody UpmRolePageQuery query);

    @PostMapping("/conditionList")
    @ApiOperation(value = "条件查询角色列表")
    List<UpmRoleDTO> conditionList(@RequestBody UpmRoleQuery query);

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    UpmRoleDTO get(@RequestParam Long id);

    @PostMapping("/save")
    @ApiOperation(value = "添加")
    Boolean save(@RequestBody UpmRoleSaveCommand command);

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    Boolean change(@RequestBody UpmRoleUpdateCommand command);

    @PostMapping("/change/status")
    @ApiOperation(value = "启用停用角色")
    Boolean changeStatus(@RequestBody UpmChangeStatusCommand command);

    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除角色")
    Boolean batchRemove(@RequestBody List<Long> idList);

    @PostMapping("/query/resource")
    @ApiOperation(value = "查询角色id资源")
    List<UpmResourceDTO> queryResourceTreeById(@RequestParam Long id);

    @PostMapping("/update/resource")
    @ApiOperation(value = "修改角色下菜单权限")
    Boolean changeResource(@RequestBody UpmRoleResourceCommand command);

    @PostMapping("/batch/search")
    @ApiOperation(value = "依照id列表批量获取角色信息")
    List<UpmRoleDTO> bachSearch(@ApiParam(value = "依照id列表批量获取角色信息") @RequestBody List<Long> idList);


    @PostMapping("/saveBySystemType")
    @ApiOperation(value = "依照系统类型新增角色")
    List<UpmRoleDTO> saveBySystemType(@ApiParam(value = "依照系统类型新增角色") @RequestBody @Validated UpmRoleSaveBySystemTypeCommand command);

    @PostMapping("/updateBySystemType")
    @ApiOperation(value = "依照系统类型修改角色")
    Boolean updateBySystemType(@ApiParam(value = "依照系统类型修改角色") @RequestBody @Validated UpmRoleUpdateBySystemTypeCommand command);

    @PostMapping("/change/status/bySystemType")
    @ApiOperation(value = "依照系统类型批量启用禁用角色")
    Boolean changeStatusBySystemType(@ApiParam(value = "依照系统类型批量启用禁用角色") @RequestBody @Validated UpmChangeStatusBySystemTypeCommand command);

    @PostMapping("/batch/delete/bySystemType")
    @ApiOperation(value = "依照系统类型批量删除角色")
    Boolean batchDeleteBySystemType(@ApiParam(value = "依照系统类型批量删除角色") @RequestBody @Validated UpmRoleDelBySystemTypeCommand command);

    @PostMapping("/query/resource/bySystemType")
    @ApiOperation(value = "依照系统类型查询主角色id资源")
    List<UpmResourceDTO> queryResourceBySystemType(@ApiParam(value = "依照系统类型批量删除角色") @RequestBody @Validated UpmRoleResourceBySystemTypeQuery query);

    @PostMapping("/change/resource/bySystemType")
    @ApiOperation(value = "依照系统类型修改角色下菜单权限")
    Boolean changeResourceBySystemType(@ApiParam(value = "角色id") @RequestBody @Validated UpmRoleResourceChangeBySystemTypeCommand command);

    @PostMapping("/saveReturnId")
    @ApiOperation(value = "添加")
    Long saveReturnId(@ApiParam(value = "角色") @RequestBody @Validated UpmRoleSaveCommand command);
}
