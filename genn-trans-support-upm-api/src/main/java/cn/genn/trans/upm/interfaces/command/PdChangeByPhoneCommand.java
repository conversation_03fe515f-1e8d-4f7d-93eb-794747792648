package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
@Accessors(chain = true)
public class PdChangeByPhoneCommand {

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String telephone;
    @ApiModelProperty(value = "短信验证码", required = true)
    @NotBlank(message = "短信验证码不能为空")
    private String smsVerificationCode;

    @ApiModelProperty(value = "密码")
    @NotBlank(message = "新密码不能为空")
    private String password;

    @ApiModelProperty(value = "确认密码")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    @ApiModelProperty(value = "系统code")
    @NotBlank(message = "系统code不能为空")
    private String systemCode;

}
