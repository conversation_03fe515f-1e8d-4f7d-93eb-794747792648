package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class UpmRoleUpdateCommand {

    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "系统id不能为空")
    private Long id;

    @ApiModelProperty(value = "名称")
    @Size(max = 32, message = "名称最大长度不能超过32")
    private String name;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "角色code")
    @Size(max = 32, message = "编码最大长度不能超过32")
    private String code;

    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "描述最大长度不能超过255")
    private String remark;
}
