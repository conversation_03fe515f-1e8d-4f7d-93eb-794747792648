package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.command.feishu.UpmFSUserCommand;
import cn.genn.trans.upm.interfaces.dto.LoginUserAccountDTO;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.feishu.FeishuLoginUserDTO;
import cn.genn.trans.upm.interfaces.query.SsoUserTokenQuery;
import cn.genn.trans.upm.interfaces.query.feishu.SsoAccountFeishuLoginQuery;
import cn.genn.trans.upm.interfaces.query.feishu.SsoAccountFeishuSmsLoginQuery;
import cn.genn.trans.upm.interfaces.query.mini.SsoUserTokenMiniQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "ISsoFeishuService", path = "/api/upm/sso/feishu", url = "${genn.upm.auth.upmServerUrl:}")
public interface ISsoFeishuService {
    @PostMapping("/smsLogin")
    @ApiOperation("小程序短信验证码登录")
    FeishuLoginUserDTO smsLogin(@Validated @RequestBody SsoAccountFeishuSmsLoginQuery query);

    @PostMapping("/accountLogin")
    @ApiOperation("小程序账号登录")
    FeishuLoginUserDTO accountLogin(@Validated @RequestBody SsoAccountFeishuLoginQuery query);

    @PostMapping("/userInfo")
    @ApiOperation("子系统根据token获取用户信息")
    LoginUserAuthInfoDTO feishuUserInfo(@Validated @RequestBody SsoUserTokenMiniQuery query);

    @PostMapping("/logout")
    @ApiOperation("登出")
    Boolean logout(@Validated @RequestBody SsoUserTokenQuery query);

    @PostMapping("/refreshToken")
    @ApiOperation("子系统token续约")
    Boolean refreshToken(@Validated @RequestBody SsoUserTokenQuery query);

    @PostMapping("/agentLogin")
    @ApiOperation("智能体-飞书三方登录")
    LoginUserAccountDTO getLoginUserAccount(@Validated @RequestBody UpmFSUserCommand command);
}
