package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.base.requestSign.UpmSignRequestInterceptor;
import cn.genn.trans.upm.interfaces.command.UpmChangeStatusCommand;
import cn.genn.trans.upm.interfaces.command.UpmResourceOperateCommand;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import cn.genn.trans.upm.interfaces.query.UpmResourceBySystemTypeQuery;
import cn.genn.trans.upm.interfaces.query.UpmResourceQuery;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * 资源接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "upmResourceService", path = "/api/upm/upmResource", url = "${genn.upm.auth.upmServerUrl:}", configuration = UpmSignRequestInterceptor.class)
public interface IUpmResourceService {

    @PostMapping("/queryListByAuthKey")
    @ApiOperation(value = "查询用户权限组范围内的所有资源")
    List<UpmResourceDTO> queryListByAuthKey();

    /**
     * 系统，租户，角色列表，资源类型查询资源列表,分三类查询
     * type: 默认查所有类型资源,类型包括:菜单,按钮等
     * systemId: 查询系统下所有资源
     * roleIdList:查询角色列表下的资源
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "系统，角色列表，资源类型查询资源列表")
    List<UpmResourceDTO> queryList(@RequestBody UpmResourceQuery query);

    @PostMapping("/queryNoChild")
    @ApiOperation(value = "查询没有下级的资源")
    List<Long> queryNoChild(@RequestBody List<Long> idList);

    // @PostMapping("/treeList")
    // @ApiOperation(value = "查询列表-树")
    // List<UpmResourceTreeDTO> treeList(@RequestBody UpmResourceTreeQuery query);

    @PostMapping("/get")
    @ApiOperation(value = "id查询单个资源")
    UpmResourceDTO get(@RequestParam(value = "id") Long id);

    @PostMapping("/save")
    @ApiOperation(value = "新增资源")
    Long save(@RequestBody UpmResourceOperateCommand command);

    @PostMapping("/update")
    @ApiOperation(value = "更新资源")
    Boolean update(@RequestBody UpmResourceOperateCommand command);

    @PostMapping("/change/status")
    @ApiOperation(value = "批量启用停用")
    Boolean changeStatus(@RequestBody UpmChangeStatusCommand command);

    @PostMapping("/remove")
    @ApiOperation(value = "删除资源")
    Boolean remove(@RequestBody List<Long> resourceIdList);

    @PostMapping("/queryListBySystemType")
    @ApiOperation(value = "依照系统类型 查询用户权限组范围内的所有资源")
    List<UpmResourceDTO> queryListBySystemType(@ApiParam(value = "查询类") @Validated @RequestBody UpmResourceBySystemTypeQuery query);


}
