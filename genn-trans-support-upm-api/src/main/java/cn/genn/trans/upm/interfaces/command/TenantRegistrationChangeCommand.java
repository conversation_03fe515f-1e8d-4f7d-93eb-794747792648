package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/14
 */
@Data
public class TenantRegistrationChangeCommand {

    @ApiModelProperty(value = "租户信息")
    private UpmTenantChangeCommand tenantChangeCommand;

    @ApiModelProperty(value = "开通系统和资源列表")
    private List<SystemResourceCommand> systemResourceList;

    @ApiModelProperty(value = "运营商id")
    @NotNull(message = "运营商id不能为空")
    private Long operatorId;

    @ApiModelProperty(value = "租户id")
    @NotNull(message = "租户id不能为空")
    private Long tenantId;
}
