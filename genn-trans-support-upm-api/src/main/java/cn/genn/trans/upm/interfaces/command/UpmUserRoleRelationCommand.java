package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UpmUserRoleRelationCommand {

    @ApiModelProperty(value = "用户id")
    @NotNull(message="用户id不能为空")
    private Long userId;

    @ApiModelProperty(value = "角色id")
    private List<Long> roleIdList;

}
