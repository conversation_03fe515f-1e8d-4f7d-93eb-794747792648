package cn.genn.trans.upm.interfaces.dto.mini;

import cn.genn.trans.upm.interfaces.enums.AccountTypeEnum;
import cn.genn.trans.upm.interfaces.enums.LoginTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import lombok.Data;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Data
public class UserWxInfoDTO {

    /**
     * 账号id
     */
    private Long id;

    /**
     * 账号
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐
     */
    private String salt;

    /**
     * 状态（1：启用；2：停用）
     */
    private StatusEnum status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 登录类型（phone:手机，normal:普通账号）
     */
    private LoginTypeEnum loginType;

    /**
     * 类型（system:系统账号，default:默认账号；unity:统一登录账号）
     */
    private AccountTypeEnum type;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 微信union_id
     */
    private String unionId;

    /**
     * 微信open_id
     */
    private String openId;

    private String appId;

    private String authKey;

    private Long systemId;

    private Long tenantId;
}
