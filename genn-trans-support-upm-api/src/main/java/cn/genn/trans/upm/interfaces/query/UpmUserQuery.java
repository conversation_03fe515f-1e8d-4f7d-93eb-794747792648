package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * UpmUser查询对象
 *
 * <AUTHOR>
 */
@Data
public class UpmUserQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "权限组key")
    private String authKey;

    /**
     * 模糊查询
     */
    @ApiModelProperty(value = "账号")
    private String username;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "昵称")
    private String nick;

    @ApiModelProperty(value = "userIdList")
    private List<Long> userIdList;

    @ApiModelProperty(value = "删除标记:0未删除,1已删除")
    private DeletedEnum deleted;


}

