package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.base.requestSign.UpmSignRequestInterceptor;
import cn.genn.trans.upm.interfaces.command.yzg.YzgPermissionSyncCmd;
import cn.genn.trans.upm.interfaces.dto.UpmResourceDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * 资源接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IYzgResourceService", path = "/api/upm/yzgResource", url = "${genn.upm.auth.upmServerUrl:}", configuration = UpmSignRequestInterceptor.class)
public interface IYzgResourceService {

    @PostMapping("/sync")
    @ApiOperation(value = "删除资源")
    List<UpmResourceDTO> syncResourceByYzg(@RequestBody YzgPermissionSyncCmd syncCmd);

    @PostMapping("/queryList")
    @ApiOperation(value = "获取资源")
    List<UpmResourceDTO> queryList(@RequestBody List<Long> systemIds);
}
