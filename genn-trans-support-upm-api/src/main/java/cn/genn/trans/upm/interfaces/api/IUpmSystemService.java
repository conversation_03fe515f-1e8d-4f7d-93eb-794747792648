package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.base.requestSign.UpmSignRequestInterceptor;
import cn.genn.trans.upm.interfaces.dto.UpmSystemDTO;
import cn.genn.trans.upm.interfaces.query.SystemQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * 系统接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "upmSystemService", path = "/api/upm/upmSystem", url = "${genn.upm.auth.upmServerUrl:}", configuration = UpmSignRequestInterceptor.class)
public interface IUpmSystemService {

    @PostMapping("/get")
    @ApiOperation(value = "id查询单个系统")
    UpmSystemDTO get(@RequestParam(value = "id") Long id);

    @PostMapping("/getByCode")
    @ApiOperation(value = "根据systemCode查询")
    UpmSystemDTO getByCode(@RequestParam String systemCode);

    @PostMapping("/list")
    @ApiOperation(value = "查询系统列表")
    List<UpmSystemDTO> list(@RequestBody List<Long> idList);

    @GetMapping("/listAll")
    @ApiOperation(value = "查询所有系统列表")
    List<UpmSystemDTO> listAll();

    @PostMapping("/queryList")
    @ApiOperation(value = "条件查询系统列表")
    List<UpmSystemDTO> queryList(@RequestBody SystemQuery query);
}
