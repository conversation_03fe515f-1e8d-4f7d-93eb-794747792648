package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

    SYSTEM("system","系统账号"),
    DEFAULT("default","默认账号"),
    UNITY("unity","统一账号"),
        ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, AccountTypeEnum> VALUES = new HashMap<>();

    static {
        for (final AccountTypeEnum item : AccountTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AccountTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
