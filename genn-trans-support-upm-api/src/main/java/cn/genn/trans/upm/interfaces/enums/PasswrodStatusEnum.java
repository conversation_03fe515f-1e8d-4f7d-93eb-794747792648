package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/6
 */
@Getter
public enum PasswrodStatusEnum {
    DEFAULT(0, "初始密码"),
    NORMAL(1, "已修改"),

    ;

    @EnumValue
    @JsonValue
    private final int type;

    private final String description;

    PasswrodStatusEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    private static final Map<Integer, PasswrodStatusEnum> VALUES = new HashMap<>();
    static {
        for (final PasswrodStatusEnum item : PasswrodStatusEnum.values()) {
            VALUES.put(item.getType(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static PasswrodStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
