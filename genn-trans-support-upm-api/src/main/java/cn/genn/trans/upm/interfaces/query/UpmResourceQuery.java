package cn.genn.trans.upm.interfaces.query;

import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * UpmResource查询对象
 *
 * <AUTHOR>
 */
@Data
public class UpmResourceQuery {

    @ApiModelProperty(value = "系统id")
    @NotNull(message = "系统id不能为空")
    private Long systemId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    @ApiModelProperty(value = "角色ids")
    private List<Long> roleIdList;

    @ApiModelProperty(value = "资源类型（1: 菜单，2：按钮）")
    private ResourceTypeEnum type;

}

