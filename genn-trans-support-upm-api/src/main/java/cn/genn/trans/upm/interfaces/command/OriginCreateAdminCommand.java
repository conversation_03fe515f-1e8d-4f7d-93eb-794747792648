package cn.genn.trans.upm.interfaces.command;

import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 业务子系统新增管理员
 */
@Data
@Accessors(chain = true)
public class OriginCreateAdminCommand {

    @ApiModelProperty(value = "租户id")
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    @ApiModelProperty(value = "源id")
    @NotNull(message = "源id 不能为空")
    private Long originId;

    @ApiModelProperty(value = "开通系统和资源列表-无则默认当前系统级别下的系统的所有资源")
    private List<SystemResourceCommand> systemResourceList;

    @ApiModelProperty(value = "系统级别")
    @NotNull(message = "系统级别 不能为空")
    private SystemTypeEnum type;

    @ApiModelProperty(value = "权限组类型")
    @NotNull(message = "权限组类型 不能为空")
    private AuthGroupEnum authGroup;



    /**
     * 默认管理员信息
     */
    @ApiModelProperty(value = "用户名")
    @NotBlank(message = "管理员用户名 不能为空")
    private String userName;

    @ApiModelProperty(value = "管理员默认密码-无则使用upm默认")
    private String password;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "管理员手机号 不能为空")
    private String telephone;

    @ApiModelProperty(value = "管理员昵称")
    private String nickName;



}
