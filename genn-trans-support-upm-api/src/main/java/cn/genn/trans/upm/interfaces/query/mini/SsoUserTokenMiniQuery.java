package cn.genn.trans.upm.interfaces.query.mini;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SsoUserTokenMiniQuery {

    @ApiModelProperty(value = "token")
    @NotBlank(message = "token不能为空")
    private String token;
    @ApiModelProperty(value = "用户设备标识")
    private String userDeviceIdentify;
}
