package cn.genn.trans.upm.interfaces.command.fsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * fsp初始化角色数据
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InitRoleSaveCommand {

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    @Size(max = 32, message = "名称最大长度不能超过32")
    private String name;

    @ApiModelProperty(value = "角色code")
    @NotBlank(message = "编码不能为空")
    @Size(max = 32, message = "编码最大长度不能超过32")
    private String code;

    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "描述最大长度不能超过255")
    private String remark;

    @ApiModelProperty(value = "资源id")
    private List<Long> resourceIdList;
}
