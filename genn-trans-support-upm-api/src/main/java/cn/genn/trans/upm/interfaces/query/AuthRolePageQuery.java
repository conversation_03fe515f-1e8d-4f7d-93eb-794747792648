package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class AuthRolePageQuery extends PageSortQuery implements Serializable {

    @ApiParam(value = "权限组模板id")
    @NotNull(message = "权限组模板id不能为空")
    private Long templateId;
}
