package cn.genn.trans.upm.interfaces.command.fsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 审核组织后续处理
 *
 * <AUTHOR>
 * @date 2024/7/27
 */
@Data
@Slf4j
public class ReviewAfterCommand {

    @ApiModelProperty(value = "userId")
    @NotNull(message = "userId不能为空")
    private Long userId;

    // @ApiModelProperty(value = "初始化角色数据")
    // @Valid
    // private List<InitRoleSaveCommand> roleList;
}
