package cn.genn.trans.upm.interfaces.command.mini;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ExpenseReviewCommand extends NotifyCommand {

    @ApiModelProperty("订单号")
    @NotBlank(message = "订单号不能为空")
    @Size(max=32,message="订单号长度不能大于32")
    private String orderNo;

    @ApiModelProperty("费用项,例如:超时费")
    @NotBlank(message = "费用项不能为空")
    @Size(max=20,message="费用项长度不能大于20")
    private String expenseItem;

    @ApiModelProperty("金额")
    @NotBlank(message = "金额不能为空")
    @Pattern(regexp="^[￥$€£]?\\d{1,10}(\\.\\d{1,3})?元?$", message="金额格式错误")
    private String expense;

    @ApiModelProperty("状态")
    @NotBlank(message = "状态不能为空")
    @Pattern(regexp="^[\\u4e00-\\u9fa5]{1,5}$", message="状态格式错误,只能包含5个以内汉字")
    private String statusName;
}
