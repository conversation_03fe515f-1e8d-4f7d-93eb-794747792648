package cn.genn.trans.upm.interfaces.query;

import cn.genn.trans.upm.interfaces.enums.ThridTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户id获取三方关联信息(wx,app等)
 */
@Data
@Accessors(chain = true)
public class UserThirdQuery {

    @ApiModelProperty("三方类型")
    @NotNull(message = "三方类型不能为空")
    private ThridTypeEnum thirdType;

    /**
     * 移动端个推对应cid字段
     */
    @ApiModelProperty("appId")
    @NotBlank(message = "appId不能为空")
    private String appId;

    @ApiModelProperty("openId")
    private String openId;

    @ApiModelProperty("userId列表")
    @NotEmpty(message = "用户id列表不能为空")
    private List<Long> userIdList;



}
