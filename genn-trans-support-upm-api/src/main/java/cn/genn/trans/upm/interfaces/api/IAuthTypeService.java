package cn.genn.trans.upm.interfaces.api;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.interfaces.command.AuthTypeChangeCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleDeleteCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleSaveCommand;
import cn.genn.trans.upm.interfaces.command.UpmAuthRoleUpdateCommand;
import cn.genn.trans.upm.interfaces.dto.UpmAuthTypeRoleTemplateDTO;
import cn.genn.trans.upm.interfaces.dto.UpmAuthTypeTemplateDTO;
import cn.genn.trans.upm.interfaces.query.AuthRolePageQuery;
import cn.genn.trans.upm.interfaces.query.AuthTypeQuery;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 权限组类型模板
 *
 * <AUTHOR>
 * @date 2024/7/28
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IAuthTypeService", path = "/api/upm/authType", url = "")
public interface IAuthTypeService {

    @PostMapping("/query")
    @ApiOperation("查询当前系统权限组类型")
    List<UpmAuthTypeTemplateDTO> query(@RequestBody AuthTypeQuery query);

    @PostMapping("/changeResource")
    @ApiOperation("维护组织菜单模板")
    Boolean changeResource(@RequestBody @Validated AuthTypeChangeCommand command);

    @PostMapping("/roleTemplatePage")
    @ApiOperation(value = "分页查询角色")
    PageResultDTO<UpmAuthTypeRoleTemplateDTO> roleTemplatePage(@ApiParam(value = "查询类") @RequestBody @Validated AuthRolePageQuery query);

    @PostMapping("/roleTemplateSave")
    @ApiOperation(value = "添加角色模板")
    Boolean roleTemplateSave(@ApiParam(value = "添加角色模板") @RequestBody @Validated UpmAuthRoleSaveCommand command);

    @PostMapping("/roleTemplateUpdate")
    @ApiOperation(value = "编辑角色模板")
    Boolean roleTemplateUpdate(@ApiParam(value = "编辑角色模板") @RequestBody @Validated UpmAuthRoleUpdateCommand command);

    @PostMapping("/roleTemplateDeletes")
    @ApiOperation(value = "删除角色模板")
    Boolean roleTemplateDeletes(@ApiParam(value = "删除角色模板") @RequestBody @Validated UpmAuthRoleDeleteCommand command);
}
