package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/5/28
 */
@Getter
@AllArgsConstructor
public enum DefaultSelectEnum {

    FALSE(false, "否"),
    TRUE(true, "是");


    @EnumValue
    @JsonValue
    private final boolean code;

    private final String description;

    private static final Map<Boolean, DefaultSelectEnum> VALUES = new HashMap<>();
    static {
        for (final DefaultSelectEnum item : DefaultSelectEnum.values()) {
            VALUES.put(item.code, item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static DefaultSelectEnum of(boolean code) {
        return VALUES.get(code);
    }
}
