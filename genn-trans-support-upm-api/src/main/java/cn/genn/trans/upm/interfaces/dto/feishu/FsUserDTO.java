package cn.genn.trans.upm.interfaces.dto.feishu;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FsUserDTO {

    @ApiModelProperty(value = "当前启用飞书appId")
    private String fsAppId;

    private String fsOpenId;

    @ApiModelProperty(value = "飞书头像链接")
    private String fsAvatarUrl;

    @ApiModelProperty(value = "飞书部门信息,子部门在前")
    private List<FsDepartmentDTO> fsDepartments;

    @ApiModelProperty(value = "名称")
    private String fsName;

    @ApiModelProperty(value = "手机号")
    private String fsTelephone;
}
