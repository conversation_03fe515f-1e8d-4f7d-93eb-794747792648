package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UpmTenantDTO;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IUpmOriginService", path = "/api/upm/upmOrigin")
public interface IUpmOriginService {

    @PostMapping("/createOriginAdmin")
    @ApiOperation(value = "业务子系统添加管理员（包含创建多系统、角色、账号、用户、资源关联）")
    List<UpmUserDTO> createOriginAdministrator(@ApiParam(value = "操作类") @RequestBody @Validated OriginCreateAdminCommand command);
}
