package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.upm.interfaces.enums.AccountTypeEnum;
import cn.genn.trans.upm.interfaces.enums.LoginTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * UpmAccount查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpmAccountQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "账号")
    private String username;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "盐")
    private String salt;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "登录类型（phone:手机，normal:普通账号）")
    private LoginTypeEnum loginType;

    @ApiModelProperty(value = "类型（system:系统账号，default:默认账号；unity:统一登录账号）")
    private AccountTypeEnum type;

    @ApiModelProperty(value = "手机号")
    private String telephone;

    @ApiModelProperty(value = "逻辑删除")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户ID")
    private Long createUserId;

    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private Long updateUserId;

    @ApiModelProperty(value = "更新用户名")
    private String updateUserName;


}

