package cn.genn.trans.upm.interfaces.base.web.filter;

import cn.genn.core.context.BaseRequestContext;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.upm.interfaces.api.ISsoAuthClient;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.exception.AuthException;
import cn.genn.trans.upm.interfaces.base.web.exception.MessageCode;
import cn.genn.trans.upm.interfaces.base.web.properties.SsoAuthProperties;
import cn.genn.trans.upm.interfaces.query.SsoUserTokenQuery;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 登录过滤器 没有登录态token的请求被拦截
 *
 * @Date: 2024/4/24
 * @Author: kangjian
 */
@Component
@Slf4j
public class SsoLoginFilter implements HandlerInterceptor, Ordered {

    public static final String TOKEN = "token";
    /**
     * 魔法登录 header头信息模拟登录
     */
    public static final String MAGIC_TOKEN = "magic-token";

    private final PathMatcher pathMatcher = (PathMatcher) new AntPathMatcher();

    private SsoAuthProperties ssoAuthProperties;

    @Value(value = "${server.servlet.context-path}")
    private String contextPath;

    public SsoLoginFilter(SsoAuthProperties ssoAuthProperties) {
        this.ssoAuthProperties = ssoAuthProperties;
    }

    /**
     * @param request  current HTTP request
     * @param response current HTTP response
     * @param handler  chosen handler to execute, for type and/or instance evaluation
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.remove();
        if (!ssoAuthProperties.getSession().isEnable()) {
            return true;
        }
        String token = request.getHeader(TOKEN);
        String magicToken = request.getHeader(MAGIC_TOKEN);
        String requestUri = request.getRequestURI();

        try {
            // 无需拦截的url
            if (filterIgnoreAccess(requestUri)) {
                log.debug("SsoLoginFilter ignore uri auth requestUri={}", requestUri);
                return true;
            }
            // 魔法登录 从magicToken中提取数据构造登录态
            boolean magicLogin = StrUtil.isNotBlank(magicToken);

            if (!magicLogin && StrUtil.isBlank(token)) {
                throw new AuthException(MessageCode.AUTH_FAIL);
            }

            log.info("SsoLoginFilter auth requestUri={} token={} magic-token={}", requestUri, token, magicToken);

            SsoUserAuthInfoDTO userAuthInfoDTO = null;

            if (magicLogin) {
                magicToken = URLDecoder.decode(magicToken, CharsetUtil.CHARSET_UTF_8);
                log.info("SsoLoginFilter decode magicToken={}", magicToken);
                userAuthInfoDTO = JsonUtils.parse(magicToken, SsoUserAuthInfoDTO.class);
            } else {
                SsoUserTokenQuery query = SsoUserTokenQuery.builder()
                    .token(token)
                    .build();

                userAuthInfoDTO = SpringUtil.getBean(ISsoAuthClient.class).tokenUserAuthInfo(query);
                log.info("SsoAuthFilter userAuthInfoDTO from token dto={}", JsonUtils.toJson(userAuthInfoDTO));
                if (Objects.isNull(userAuthInfoDTO)) {
                    throw new AuthException(MessageCode.TOKEN_INVALID);
                }
            }

            //            if (userAuthInfoDTO.isFrozenStatus()) {
            //                throw new AuthException(MessageCode.TOKEN_FREEZE);
            //            }

            CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.set(userAuthInfoDTO);
            // core-上下文
            BaseRequestContext baseRequestContext = BaseRequestContext.get();
            if(baseRequestContext == null){
                baseRequestContext = new BaseRequestContext();
            }
            baseRequestContext.setUserId(userAuthInfoDTO.getUserId());
            baseRequestContext.setUserName(userAuthInfoDTO.getUsername());
            baseRequestContext.setTenantId(userAuthInfoDTO.getTenantId());
            BaseRequestContext.set(baseRequestContext);
            return true;
        } catch (AuthException ex) {
            log.warn("SsoLoginFilter error ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("SsoLoginFilter error ", ex);
            throw new AuthException(MessageCode.LOGIN_ERROR.getCode(), "登录过滤器异常: " + ex.getMessage());
        }
    }

    private boolean filterIgnoreAccess(String requestUri) {
        // for (String ignoreUrl : ssoAuthProperties.getPermission().getExcludePatterns()) {
        //     if (pathMatcher.match(ignoreUrl, requestUri)) {
        //         return true;
        //     }
        // }
        String requestUriSub = "";
        if (requestUri.startsWith(contextPath)) {
            requestUriSub = requestUri.substring(contextPath.length());
        }
        for (String ignoreUrl : ssoAuthProperties.getDefaultUris()) {
            if (pathMatcher.match(ignoreUrl, requestUri) || pathMatcher.match(ignoreUrl, requestUriSub)) {
                return true;
            }
        }
        for (String ignoreUrl : ssoAuthProperties.getSession().getExcludePatterns()) {
            if (pathMatcher.match(ignoreUrl, requestUri) || pathMatcher.match(ignoreUrl, requestUriSub)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.remove();
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

    @Override
    public int getOrder() {
        return -100;
    }
}
