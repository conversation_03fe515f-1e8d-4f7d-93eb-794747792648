package cn.genn.trans.upm.interfaces.query.mini;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 描述
 * @date 2024/6/20
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SsoAccountMiniLoginQuery {

    @ApiModelProperty(value = "账号", required = true)
    @NotBlank(message = "账号不能为空")
    private String username;
    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "密码不能为空")
    private String password;
    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String systemCode;
    @ApiModelProperty(value = "小程序appid", required = true)
    @NotBlank(message = "小程序appid不能为空")
    private String appid;
    @ApiModelProperty(value = "临时登录code", required = true)
    @NotBlank(message = "临时登录code不能为空")
    private String loginCode;
}
