package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UpmAuthRoleSaveCommand {

    @ApiParam(value = "权限组模板id")
    @NotNull(message = "权限组模板id不能为空")
    private Long templateId;

    @ApiParam(value = "角色code")
    @NotBlank(message = "角色code不能为空")
    private String roleCode;

    @ApiParam(value = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    @ApiParam(value = "角色绑定资源")
    private List<Long> resourceJson;

    @ApiModelProperty(value = "描述")
    private String remark;

    /**
     * 已通过审核的组织id
     */
    @ApiModelProperty(value = "组织id列表")
    private List<Long> companyIdList;
}
