package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Getter
@AllArgsConstructor
public enum LoginTypeEnum {

    PHONE("phone", "手机"),
    NORMAL("normal", "普通"),
        ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, LoginTypeEnum> VALUES = new HashMap<>();
    static {
        for (final LoginTypeEnum item : LoginTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static LoginTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
