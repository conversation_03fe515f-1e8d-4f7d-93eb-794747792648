package cn.genn.trans.upm.interfaces.query.app;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class SsoAccountAppLoginQuery {

    @ApiModelProperty(value = "平台", required = true)
    @NotBlank(message = "平台不能为空")
    private String systemCode;

    @ApiModelProperty(value = "梦网获取手机号query", required = true)
    @NotNull(message = "手机号query不能为空")
    @Valid
    private MengSmsQuery query;

    @ApiModelProperty(value = "个推clientId", required = true)
    @NotBlank(message = "cid不能为空")
    private String cid;

    @ApiModelProperty(value = "appId", required = true)
    @NotBlank(message = "appId不能为空")
    private String appId;

}
