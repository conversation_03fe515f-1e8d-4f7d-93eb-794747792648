package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
@Accessors(chain = true)
public class UserInfoChangeCommand {

    @ApiModelProperty("昵称")
    @Size(max = 32, message = "昵称最大长度不能超过32")
    private String nick;

    @ApiModelProperty("个人简介")
    @Size(max = 1024, message = "个人简介最大长度不能超过32")
    private String remark;

    @ApiModelProperty("邮箱")
    @Pattern(regexp="^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message="邮箱格式错误")
    private String email;

    @ApiModelProperty("头像")
    private String avatar;
}
