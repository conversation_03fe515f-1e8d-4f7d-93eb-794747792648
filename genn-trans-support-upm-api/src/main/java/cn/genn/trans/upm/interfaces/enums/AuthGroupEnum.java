package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Getter
@AllArgsConstructor
public enum AuthGroupEnum {
    PLATFORM("pl", "平台"),
    OPERATOR("op", "运营"),
    CARRIER("ca", "承运"),
    CLIENT("cl", "客户"),
    STATION("st", "站端"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, AuthGroupEnum> VALUES = new HashMap<>();

    static {
        for (final AuthGroupEnum item : AuthGroupEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AuthGroupEnum of(String code) {
        return VALUES.get(code);
    }


}
