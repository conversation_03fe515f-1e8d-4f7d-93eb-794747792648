package cn.genn.trans.upm.interfaces.dto;

import cn.genn.core.model.enums.DeletedEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UpmClusterSystemRelDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "集群分组（default；）")
    private String vpcGroup;

    @ApiModelProperty(value = "集群名称")
    private String clusterName;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "系统code")
    private String systemCode;

    @ApiModelProperty(value = "集群域名")
    private String domain;

    @ApiModelProperty(value = "内网域名")
    private String internalDomain;

    @ApiModelProperty(value = "删除标记（0：未删除，1已删除）")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
