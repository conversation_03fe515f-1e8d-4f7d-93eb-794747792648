package cn.genn.trans.upm.interfaces.command.yzg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class YzgPermissionSyncCmd implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long systemId;

    private List<YzgPermissionItem> permissions;

    @Data
    public static class YzgPermissionItem implements Serializable {
        private static final long serialVersionUID = 1L;
        private Integer id;
        private Integer groupId;
        private String groupName;
        private String name;
        private String enName;
        private String url;
        private Integer type;
        private Short sort;
    }
}
