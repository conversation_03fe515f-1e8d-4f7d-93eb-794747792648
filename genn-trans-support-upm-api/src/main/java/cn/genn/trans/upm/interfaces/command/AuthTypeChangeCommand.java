package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 编辑权限组类型数据
 *
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
@Accessors(chain = true)
public class AuthTypeChangeCommand {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "资源id列表")
    @NotEmpty(message = "资源树列表不能为空")
    private List<Long> resourceJson;

    /**
     * 已通过审核的组织id
     */
    @ApiModelProperty(value = "组织id列表")
    private List<Long> companyIdList;
}
