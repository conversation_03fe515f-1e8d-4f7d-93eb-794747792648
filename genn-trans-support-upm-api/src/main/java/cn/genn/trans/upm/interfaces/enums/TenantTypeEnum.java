package cn.genn.trans.upm.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TenantTypeEnum{

    PLATFORM("platform", "平台方"),
    BP("bp", "供应商"),
    OP("op", "运营方"),
        ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    private static final Map<String, TenantTypeEnum> VALUES = new HashMap<>();
    static {
        for (final TenantTypeEnum item : TenantTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TenantTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
