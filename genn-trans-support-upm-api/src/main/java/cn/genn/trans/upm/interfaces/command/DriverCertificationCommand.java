package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DriverCertificationCommand  implements Serializable {

    @ApiModelProperty(value = "userid")
    @NotNull(message = "userid不能为空")
    private Long userId;
}
