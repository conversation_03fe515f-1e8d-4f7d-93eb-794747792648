package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UpmAuthRoleDeleteCommand {

    @ApiModelProperty(value = "角色模板id")
    @NotNull(message = "角色模板id不能为空")
    private Long id;

    /**
     * 已通过审核的组织id
     */
    @ApiModelProperty(value = "组织id列表")
    private List<Long> companyIdList;
}
