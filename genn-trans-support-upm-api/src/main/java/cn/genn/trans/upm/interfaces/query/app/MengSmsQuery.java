package cn.genn.trans.upm.interfaces.query.app;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class MengSmsQuery {

    @NotBlank(message = "msgid不能为空")
    private String msgid;

    @NotBlank(message = "timestamp不能为空")
    private String timestamp;

    @NotNull(message = "appid不能为空")
    private Long appid;

    @NotBlank(message = "checksum不能为空")
    private String checksum;

    @NotBlank(message = "payload不能为空")
    private String payload;
}
