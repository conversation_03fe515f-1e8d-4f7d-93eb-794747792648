package cn.genn.trans.upm.interfaces.command.mini;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 派单通知
 *
 * <AUTHOR>
 * @date 2024/7/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DispatchOrderCommand extends NotifyCommand{

    @ApiModelProperty("订单号")
    @NotBlank(message = "订单号不能为空")
    @Size(max=32,message="订单号长度不能大于32")
    private String orderNo;

    @ApiModelProperty("派单时间")
    @NotNull(message = "派单时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime dispatchTime;

    @ApiModelProperty("取货地址")
    @NotBlank(message = "取货地址不能为空")
    // @Size(max=20,message="取货地址长度不能大于20")
    private String pickupAddress;

    @ApiModelProperty("备注信息")
    @NotBlank(message = "备注信息不能为空")
    // @Size(max=20,message="备注信息长度不能大于20")
    private String remark;
}
