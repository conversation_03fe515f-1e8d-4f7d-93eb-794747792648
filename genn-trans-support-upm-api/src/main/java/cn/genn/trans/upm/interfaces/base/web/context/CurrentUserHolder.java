package cn.genn.trans.upm.interfaces.base.web.context;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.exception.MessageCode;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * 线程上下文存储用户信息 SsoAuthenticationFilter赋值
 *
 * <AUTHOR>
 */
@Slf4j
public class CurrentUserHolder {
    public static final ThreadLocal<SsoUserAuthInfoDTO> CURRENT_USER_THREAD_LOCAL = new ThreadLocal<>();

    public static SsoUserAuthInfoDTO getCurrentUser() {
        log.info("threadinfo :{}", JsonUtils.toJson(CURRENT_USER_THREAD_LOCAL.get()));
        return CURRENT_USER_THREAD_LOCAL.get();
    }

    public static boolean hasRole(String roleCode) {
        SsoUserAuthInfoDTO currentUser = CURRENT_USER_THREAD_LOCAL.get();
        return (currentUser != null && CollectionUtil.isNotEmpty(currentUser.getRoles()) && currentUser.getRoles().contains(roleCode));
    }

    public static String getToken() {
        String token = CURRENT_USER_THREAD_LOCAL.get().getToken();
        if (ObjUtil.isNull(token)) {
            throw new BusinessException(MessageCode.TOKEN_GET_ERROR);
        }
        return token;
    }

    public static Long getSystemId() {
        Long systemId = CURRENT_USER_THREAD_LOCAL.get().getSystemId();
        if (ObjUtil.isNull(systemId)) {
            throw new BusinessException(MessageCode.SYSTEM_ID_ERROR);
        }
        return systemId;
    }

    public static Long getTenantId() {
        Long tenantId = CURRENT_USER_THREAD_LOCAL.get().getTenantId();
        if (ObjUtil.isNull(tenantId)) {
            throw new BusinessException(MessageCode.TENANT_ID_ERROR);
        }
        return tenantId;
    }

    public static Long getUserId() {
        Long userId = CURRENT_USER_THREAD_LOCAL.get().getUserId();
        if (ObjUtil.isNull(userId)) {
            throw new BusinessException(MessageCode.USER_ID_ERROR);
        }
        return userId;
    }

    public static Long getAccountId() {
        Long accountId = CURRENT_USER_THREAD_LOCAL.get().getAccountId();
        if (ObjUtil.isNull(accountId)) {
            throw new BusinessException(MessageCode.ACCOUNT_ID_ERROR);
        }
        return accountId;
    }

    public static String getUserName() {
        String username = CURRENT_USER_THREAD_LOCAL.get().getUsername();
        if (StrUtil.isBlank(username)) {
            throw new BusinessException(MessageCode.USERNAME_ERROR);
        }
        return username;
    }

    public static String getVpcGroup() {
        String vpcGroup = CURRENT_USER_THREAD_LOCAL.get().getVpcGroup();
        if (StrUtil.isBlank(vpcGroup)) {
            throw new BusinessException(MessageCode.VPC_GROUP_ERROR);
        }
        return vpcGroup;
    }

    public static Long getOperatorId() {
        Long operatorId = CURRENT_USER_THREAD_LOCAL.get().getOperatorId();
        if (ObjUtil.isNull(operatorId)) {
            throw new BusinessException(MessageCode.OPERATOR_ID_ERROR);
        }
        return operatorId;
    }

    public static Long getCarrierId() {
        Long carrierId = CURRENT_USER_THREAD_LOCAL.get().getCarrierId();
        if (ObjUtil.isNull(carrierId)) {
            throw new BusinessException(MessageCode.CARRIER_ID_ERROR);
        }
        return carrierId;
    }

    /**
     * 保理组织id
     *
     * @return
     */
    public static Long getCompanyId() {
        Long companyId = CURRENT_USER_THREAD_LOCAL.get().getCompanyId();
        if (ObjUtil.isNull(companyId)) {
            throw new BusinessException(MessageCode.COMPANY_ID_ERROR);
        }
        return companyId;
    }

    public static AuthGroupEnum getAuthGroup() {
        AuthGroupEnum authGroup = CURRENT_USER_THREAD_LOCAL.get().getAuthGroup();
        if (ObjUtil.isNull(authGroup)) {
            throw new BusinessException(MessageCode.AUTH_GROUP_ERROR);
        }
        return authGroup;
    }

    public static String getAuthKey() {
        String authKey = CURRENT_USER_THREAD_LOCAL.get().getAuthKey();
        if (ObjUtil.isNull(authKey)) {
            throw new BusinessException(MessageCode.AUTH_KEY_ERROR);
        }
        return authKey;
    }

    public static Long getDriverId() {
        Long driverId = CURRENT_USER_THREAD_LOCAL.get().getDriverId();
        if (ObjUtil.isNull(driverId)) {
            throw new BusinessException(MessageCode.DRIVER_ID_ERROR);
        }
        return driverId;
    }

    public static String getDriverName() {
        String driverName = CURRENT_USER_THREAD_LOCAL.get().getDriverName();
        if (ObjUtil.isNull(driverName)) {
            throw new BusinessException(MessageCode.DRIVER_NAME_ERROR);
        }
        return driverName;
    }

    public static String getNick() {
        String nick = CURRENT_USER_THREAD_LOCAL.get().getNick();
        if (ObjUtil.isNull(nick)) {
            throw new BusinessException(MessageCode.NICK_ERROR);
        }
        return nick;
    }

    public static Long getStationId() {
        Long stationId = CURRENT_USER_THREAD_LOCAL.get().getStationId();
        if (ObjUtil.isNull(stationId)) {
            throw new BusinessException(MessageCode.NICK_ERROR);
        }
        return stationId;
    }

    public static SystemTypeEnum getSystemType() {
        SystemTypeEnum systemType = CURRENT_USER_THREAD_LOCAL.get().getSystemType();
        if (ObjUtil.isNull(systemType)) {
            throw new BusinessException(MessageCode.SYSTEM_TYPE_ERROR);
        }
        return systemType;
    }

    public static String getSystemCode() {
        String systemCode = CURRENT_USER_THREAD_LOCAL.get().getSystemCode();
        if (ObjUtil.isNull(systemCode)) {
            throw new BusinessException(MessageCode.SYSTEM_CODE_ERROR);
        }
        return systemCode;
    }

    public static Long getMemberId() {
        Long memberId = CURRENT_USER_THREAD_LOCAL.get().getMemberId();
        if (memberId == null) {
            throw new BusinessException(MessageCode.MEMBER_ERROR);
        }
        return memberId;
    }
    public static String getMemberNo() {
        String memberNo = CURRENT_USER_THREAD_LOCAL.get().getMemberNo();
        if (memberNo == null) {
            throw new BusinessException(MessageCode.MEMBER_ERROR);
        }
        return memberNo;
    }
    public static Set<String> getRoles() {
        Set<String> roles = CURRENT_USER_THREAD_LOCAL.get().getRoles();
        if (CollUtil.isEmpty(roles)) {
            throw new BusinessException(MessageCode.ROLES_ERROR);
        }
        return roles;
    }

    public static String getFSOpenId() {
        String openId = CURRENT_USER_THREAD_LOCAL.get().getFsOpenId();
        if (StringUtils.isBlank(openId)) {
            throw new BusinessException(MessageCode.ROLES_ERROR);
        }
        return openId;
    }

}
