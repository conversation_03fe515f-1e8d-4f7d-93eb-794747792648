package cn.genn.trans.upm.interfaces.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Date: 2024/4/23
 * @Author: kang<PERSON>an
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class LoginUserMenusResourceDetailDTO {

    private String path;
    private String name;
    private MetaDTO meta;

    private List<LoginUserMenusResourceDetailDTO> children;

    @Data
    @NoArgsConstructor
    private class MetaDTO {
        private String title;
        private String icon;
        private Integer resourceSort;
        private List<String> roles;
        private List<String> auths;
    }

    /**
     * {
     *   path: "/mock/permission",
     *   meta: {
     *     title: "menus.purePermission",
     *     icon: "ep:lollipop",
     *     resource_sort: 10
     *   },
     *   children: [
     *     {
     *       path: "/permission/page/index",
     *       name: "PermissionPage",
     *       meta: {
     *         title: "menus.purePermissionPage",
     *         roles: ["admin", "common"]
     *       }
     *     },
     *     {
     *       path: "/permission/button/index",
     *       name: "PermissionButton",
     *       meta: {
     *         title: "menus.purePermissionButton",
     *         roles: ["admin", "common"],
     *         auths: [
     *           "permission:btn:add",
     *           "permission:btn:edit",
     *           "permission:btn:delete"
     *         ]
     *       }
     *     }
     *   ]
     * }
     */
}
