package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.command.official.SsoAccountOfficialWXTempLoginCommand;
import cn.genn.trans.upm.interfaces.dto.official.UserOfficialInfoDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "ISsoOfficialService", path = "/api/upm/sso/official", url = "${genn.upm.auth.upmServerUrl:}")
public interface ISsoOfficialService {

    @PostMapping("/login")
    @ApiOperation("微信公众号授权登录")
    UserOfficialInfoDTO officialWxLogin(@Validated @RequestBody SsoAccountOfficialWXTempLoginCommand command);
}
