package cn.genn.trans.upm.interfaces.query;

import cn.genn.trans.upm.interfaces.enums.RoleTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;

/**
 * UpmRole查询对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UpmRoleQuery{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    @Size(max=32,message="名称长度不能大于32")
    private String name;


    @ApiModelProperty(value = "类型(system:系统角色，tenant:租户角色)")
    private RoleTypeEnum type;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;


}

