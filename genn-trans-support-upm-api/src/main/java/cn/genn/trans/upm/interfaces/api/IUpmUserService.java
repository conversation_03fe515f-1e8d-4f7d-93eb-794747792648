package cn.genn.trans.upm.interfaces.api;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.trans.upm.interfaces.base.requestSign.UpmSignRequestInterceptor;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.dto.UpmUserThirdDTO;
import cn.genn.trans.upm.interfaces.query.*;
import cn.genn.trans.upm.interfaces.query.mini.UpmUserInfoQuery;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IUpmUserService", path = "/api/upm/upmUser", url = "${genn.upm.auth.upmServerUrl:}", configuration = UpmSignRequestInterceptor.class)
public interface IUpmUserService {

    @PostMapping("/page")
    @ApiOperation(value = "分页查询列表")
    PageResultDTO<UpmUserDTO> page(@RequestBody UpmUserPageQuery query);

    @PostMapping("/pageUserRole")
    @ApiOperation(value = "分页查询列表")
    PageResultDTO<UpmUserDTO> pageUserRole(@RequestBody @Validated UpmUserRolePageQuery query);

    @PostMapping("/conditionList")
    @ApiOperation(value = "条件查询用户列表")
    List<UpmUserDTO> conditionList(@RequestBody UpmUserQuery query);

    @PostMapping("/queryByRoleCode")
    @ApiOperation(value = "角色code查询用户")
    List<UpmUserDTO> queryByRoleCode(@RequestBody @Validated UserRoleQuery query);

    @PostMapping("/get")
    @ApiOperation(value = "根据id查询")
    UpmUserDTO get(@RequestParam Long id);

    @GetMapping("/check/username")
    @ApiOperation(value = "根据username查询,true表示存在")
    Boolean checkUserName(@RequestParam("username") String username,@RequestParam("systemId")Long systemId);

    @GetMapping("/check/telephone")
    @ApiOperation(value = "手机号判断账号是否存在,true表示存在")
    Boolean checkTelephone(@RequestParam("telephone") String telephone,@RequestParam("systemId")Long systemId);

    @PostMapping("/save")
    @ApiOperation(value = "添加新用户")
    UpmUserDTO save(@RequestBody UpmUserSaveCommand command);

    @PostMapping("/update")
    @ApiOperation(value = "修改用户")
    Boolean change(@RequestBody UpmUserUpdateCommand command);

    @PostMapping("/change/status")
    @ApiOperation(value = "启用停用用户")
    Boolean changeStatus(@RequestBody UpmChangeStatusCommand command);

    @PostMapping("/reset/password")
    @ApiOperation(value = "重置密码")
    Boolean resetPassword(@RequestBody UpmUserResetPasswordCommand command);

    @PostMapping("/related/role")
    @ApiOperation(value = "关联角色")
    Boolean relatedRole(@RequestBody UpmUserRoleRelationCommand command);

    @PostMapping("/batch/delete")
    @ApiOperation(value = "批量删除用户")
    Boolean batchRemove(@RequestBody List<Long> idList);

    @PostMapping("/userRegister")
    @ApiOperation(value = "各系统游客注册")
    Boolean userRegister(@RequestBody UserRegisTerCommand command);

    @PostMapping("/changeUserInfo")
    @ApiOperation(value = "更新用户信息")
    Boolean changeUserInfo(@RequestBody @Validated UserInfoChangeCommand command);

    @PostMapping("/bindPhone")
    @ApiOperation(value = "用户绑定手机")
    Boolean bindPhone(@RequestBody @Validated UserBindPhoneCommand command);

    @PostMapping("/changeBindPhone")
    @ApiOperation(value = "用户更换绑定手机")
    Boolean changeBindPhone(@RequestBody @Validated UserChangeBindPhoneCommand command);

    @PostMapping("/changePdByPhone")
    @ApiOperation(value = "密保手机修改密码")
    Boolean changePdByPhone(@RequestBody @Validated PdChangeByPhoneCommand command);

    @PostMapping("/queryUserByUsernamesAndAuthKey")
    @ApiOperation(value = "usernameList和authKey查询用户")
    List<UpmUserDTO> queryUserByUsernamesAndAuthKey(@ApiParam(value = "查询类") @RequestBody @Validated UpmUserInfoQuery query);

    @GetMapping("/searchName")
    @ApiOperation(value = "search检索username和nick查询用户列表")
    List<UpmUserDTO> searchName(@RequestParam("search") String search,@RequestParam("authKey") String authKey);

    @PostMapping("/getThirdByUserIds")
    @ApiOperation("userIds获取三方用户信息")
    List<UpmUserThirdDTO> getThirdByUserIds(@Validated @RequestBody UserThirdQuery query);

    @PostMapping("/getThirdByUserIdsAndSystemType")
    @ApiOperation("userIds和systemType获取三方用户信息")
    List<UpmUserThirdDTO> getThirdByUserIdsAndSystemType(@Validated @RequestBody UserThirdSystemQuery query);

    @PostMapping("/saveBySystemType")
    @ApiOperation(value = "依照系统类型添加新用户")
    List<UpmUserDTO> saveBySystemType(@ApiParam(value = "用户信息") @RequestBody @Validated UpmUserSaveBySystemTypeCommand command);

    @PostMapping("/updateBySystemType")
    @ApiOperation(value = "依照系统类型修改用户")
    Boolean updateBySystemType(@ApiParam(value = "用户信息") @RequestBody @Validated UpmUserUpdateBySystemTypeCommand command);

    @PostMapping("/change/status/bySystemType")
    @ApiOperation(value = "依照系统类型启用停用用户")
    Boolean changeStatusBySystemType(@ApiParam(value = "启用禁用用户") @RequestBody @Validated UpmUserChangeStatusBySystemTypeCommand command);

    @PostMapping("/reset/password/bySystemType")
    @ApiOperation(value = "依照系统类型重置密码")
    Boolean resetPasswordBySystemType(@ApiParam(value = "重置密码") @RequestBody @Validated UpmUserResetPasswordBySystemTypeCommand command);

    @PostMapping("/delete/bySystemType")
    @ApiOperation(value = "依照系统类型删除用户")
    Boolean deleteBySystemType(@ApiParam(value = "批量删除用户") @RequestBody UpmUserDelBySystemTypeCommand command);

}
