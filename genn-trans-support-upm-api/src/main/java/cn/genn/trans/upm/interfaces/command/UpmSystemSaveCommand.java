package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;


/**
 * UpmSystem操作对象
 *
 * <AUTHOR>
 */
@Data
public class UpmSystemSaveCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编号", required = true)
    @NotBlank(message = "系统编号不能为空")
    @Size(max = 32, message = "系统编号最大长度不能超过32")
    private String code;

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "系统名称不能为空")
    @Size(max = 32, message = "系统名称最大长度不能超过32")
    private String name;

    @ApiModelProperty(value = "备注")
    @Size(max = 256, message = "备注最大长度不能超过256")
    private String remark;

    @ApiModelProperty(value = "系统样式")
    private String pattern;

}

