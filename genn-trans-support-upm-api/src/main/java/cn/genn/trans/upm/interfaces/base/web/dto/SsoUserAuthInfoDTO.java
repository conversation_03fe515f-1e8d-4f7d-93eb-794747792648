package cn.genn.trans.upm.interfaces.base.web.dto;

import cn.genn.trans.pms.interfaces.enums.OperatorTypeEnum;
import cn.genn.trans.upm.interfaces.dto.feishu.FsDepartmentDTO;
import cn.genn.trans.upm.interfaces.enums.AuthGroupEnum;
import cn.genn.trans.upm.interfaces.enums.PasswrodStatusEnum;
import cn.genn.trans.upm.interfaces.enums.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * token中缓存的相应用户所有权限信息
 *
 * @Date: 2024/4/16
 * @Author: kangjian
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SsoUserAuthInfoDTO implements Serializable {

    private String token;

    /**
     * token是否冻结
     */
    private boolean frozenStatus;

    /**
     * 是否平台超级管理员
     */
    private boolean superAdmin;

    /**
     * 系统类型  平台 运营商 承运商
     */
    private SystemTypeEnum systemType;

    private Long accountId;

    private String username;

    private String telephone;

    private String email;

    private Long userId;

    private PasswrodStatusEnum passwordStatus;

    private LocalDateTime pdUpdateTime;

    /**
     * AES加密所需要的密钥
     */
    private String secretKey;
    private String ticket;

    private String userDeviceIdentify;

    /**
     * 租户信息
     */
    private Long tenantId;
    private String tenantName;
    private String tenantCode;
    private String vpcGroup;

    /**
     * 系统信息
     */
    private Long systemId;
    private String systemName;
    private String systemCode;

    /**
     * 运营商相关信息
     */
    private Long operatorId;
    private String operatorName;
    private String operatorCode;
    private OperatorTypeEnum operatorType;

    /**
     * 承运商相关信息
     */
    private Long carrierId;
    private String carrierName;
    private String carrierCode;
    /**
     * 当前承运商和运营商的合作关系
     */
    private Long operatorCarrierRelId;
    /**
     * 司机相关信息
     */
    private Long driverId;
    private String driverName;
    /**
     * 当前司机和承运商的合作关系
     */
    private Long driverCarrierRelId;

    /**
     * 角色信息
     */
    private Set<String> roles;

    /**
     * 权限信息
     */
    private String authKey;
    private AuthGroupEnum authGroup;
    private Set<String> permissions;

    /**
     * 扩展信息 方便服务使用方可以设置自己的值传递
     */
    private Map<String, Object> extendMap;

    /**
     * 用户个人信息
     */

    private String nick;
    private String avatar;
    private String remark;

    /**
     * 用户飞书信息
     */
    private String fsAppId;
    private String fsOpenId;
    private String fsAvatarUrl;
    private List<FsDepartmentDTO> fsDepartments;

    /**
     * 保理
     */
    private Long companyId;

    /**
     * 站点信息
     */
    private Long stationId;
    private String stationName;

    /**
     * 补能相关信息
     */
    private String hqId;
    private String openId;
    private String wxAppId;
    private Long memberId;
    private String memberNo;
}
