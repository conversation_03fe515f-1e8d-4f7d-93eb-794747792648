package cn.genn.trans.upm.interfaces.base.requestSign;

import cn.genn.security.constant.SignAlgorithm;
import cn.genn.security.sign.SignatureAlgorithm;
import cn.genn.security.sign.SignatureAlgorithmImpl;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class UpmRequestSignGenerator {

//    private final String secretKey;
//
//    public UpmRequestSignGenerator(String secretKey) {
//        this.secretKey = secretKey;
//    }

    public String generateSignature(String timestamp) {
        // 实现具体的签名算法，可能涉及参数排序、拼接、加密等步骤
        // 使用secretKey和其他必要数据生成签名字符串
        // ...

        SignatureAlgorithm signatureAlgorithm = new SignatureAlgorithmImpl(SignAlgorithm.HmacSHA256);


        return "";
    }
}
