package cn.genn.trans.upm.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Date: 2024/4/22
 * @Author: ka<PERSON><PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpmCheckUriPermissionQuery {
    @ApiModelProperty(value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @ApiModelProperty(value = "资源uri", required = true)
    @NotBlank(message = "资源uri不能为空")
    private String uri;
}
