package cn.genn.trans.upm.interfaces.base.web.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * 业务错误码定义
 * 从201-899
 *
 * <AUTHOR>
 */
public enum MessageCode implements MessageCodeWrap {
    AUTH_FAIL("401", "鉴权失败"),
    TOKEN_GET_ERROR("402", "未获取到token"),
    LOGIN_ERROR("403", "登录态异常"),
    TOKEN_INVALID("404", "token已失效,未获取到用户信息,请重新进行登录"),
    TOKEN_FREEZE("405", "token长时间不活跃已被冻结,需手动进行续签"),
    SYSTEM_ID_ERROR("406","未获取到系统id"),
    TENANT_ID_ERROR("407","未获取到租户id"),
    USERNAME_ERROR("408","未获取到用户名称"),
    USER_ID_ERROR("409","未获取到用户id"),
    VPC_GROUP_ERROR("410","未获取到vpcGroup"),
    CARRIER_ID_ERROR("411","未获取到承运商id"),
    OPERATOR_ID_ERROR("412","未获取到运营商id"),
    AUTH_KEY_ERROR("413","未获取到权限组"),
    NICK_ERROR("414","未获取到昵称"),
    DRIVER_ID_ERROR("415","未获取到司机id"),
    DRIVER_NAME_ERROR("416","未获取到司机名称"),
    ACCOUNT_ID_ERROR("417","未获取到司机名称"),
    COMPANY_ID_ERROR("418","未获取到组织id"),
    AUTH_GROUP_ERROR("419","未获取到组织类型"),
    STATION_ID_ERROR("420","未获取到站点id"),
    SYSTEM_TYPE_ERROR("421","未获取到系统类型"),
    SYSTEM_CODE_ERROR("422", "未获取到系统编码"),
    ROLES_ERROR("423","未获取到角色"),
    MEMBER_ERROR("424","未获取到会员编号"),
    USER_EXPIRED("425","账号已过有效期"),
    ;

    private final String code;
    private final String description;

    MessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getBizCode() {
        return DEFAULT_BIZ_CODE;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
