package cn.genn.trans.upm.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @Date: 2024/4/22
 * @Author: kang<PERSON><PERSON>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SsoUserTokenQuery {
    @ApiModelProperty(value = "token")
    @NotBlank(message = "token不能为空")
    private String token;
    @ApiModelProperty(value = "用户设备标识")
    private String userDeviceIdentify;
}
