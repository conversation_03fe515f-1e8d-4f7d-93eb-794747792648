package cn.genn.trans.upm.interfaces.api;

import cn.genn.trans.upm.interfaces.command.DriverCertificationCommand;
import cn.genn.trans.upm.interfaces.command.DriverRegisterCommand;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2024/6/21
 */
@Component
@FeignClient(name = "genn-trans-support-upm", contextId = "IMiniUserService", path = "/api/upm/mini/user", url = "${genn.upm.auth.upmServerUrl:}")
public interface IMiniUserService {

    @PostMapping("/driverRegister")
    @ApiOperation("司机upm注册")
    UserWxInfoDTO driverRegister(@RequestBody @Validated DriverRegisterCommand command);

    @PostMapping("/driverAuth")
    @ApiOperation("司机认证")
    Boolean driver(@RequestBody @Validated DriverCertificationCommand command);
}
