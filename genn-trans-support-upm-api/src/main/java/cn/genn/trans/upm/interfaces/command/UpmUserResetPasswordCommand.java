package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpmUserResetPasswordCommand {

    @ApiModelProperty(value = "主键")
    @NotNull(message = "系统id不能为空")
    private Long id;

    @ApiModelProperty(value = "新密码")
    @NotBlank(message = "密码不能为空")
    private String newPassword;
}
