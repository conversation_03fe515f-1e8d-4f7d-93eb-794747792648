package cn.genn.trans.upm.interfaces.base.web.enums;

import cn.genn.core.exception.MessageCodeWrap;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum AuthMessageCode implements MessageCodeWrap {
    SUCCESS("000000200", "请求成功"),
    PARAM_EMPTY_FAIL("000000701", "参数不正确"),
    UNKNOWN_ERROR_CODE("00999", "未知错误类型");
    private String code;
    private String desc;

    AuthMessageCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }
}
