package cn.genn.trans.upm.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.trans.upm.interfaces.enums.ResourceTypeEnum;
import cn.genn.trans.upm.interfaces.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 资源树查询对象
 *
 * <AUTHOR>
 * @date 2024/5/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpmResourceTreeQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "资源系统id")
    private Long systemId;

    @ApiModelProperty(value = "链接地址")
    private String url;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "资源类型（1: 菜单，2：按钮）")
    private ResourceTypeEnum type;

    @ApiModelProperty(value = "首字母大写，一定要与vue文件的name对应起来，用于noKeepAlive缓存控制（该项特别重要）")
    private String name;

    @ApiModelProperty(value = "菜单、面包屑、多标签页显示的名称")
    private String title;

    @ApiModelProperty(value = "前端path")
    private String path;

    @ApiModelProperty(value = "状态（1：启用；2：停用）")
    private StatusEnum status;
}
