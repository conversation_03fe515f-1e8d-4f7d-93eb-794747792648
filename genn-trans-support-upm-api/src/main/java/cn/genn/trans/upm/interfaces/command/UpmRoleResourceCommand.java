package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UpmRoleResourceCommand {

    @ApiModelProperty(value = "角色id")
    @NotNull(message = "roleId不能为空")
    private Long roleId;

    @ApiModelProperty(value = "资源id")
    private List<Long> resourceIdList;
}
