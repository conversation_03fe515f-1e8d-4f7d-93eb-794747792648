package cn.genn.trans.upm.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UpmAuthRoleUpdateCommand {

    @ApiModelProperty(value = "角色模板主键id")
    @NotNull(message = "角色模板id不能为空")
    private Long id;

    @ApiParam(value = "角色code")
    private String roleCode;

    @ApiParam(value = "角色名称")
    private String roleName;

    @ApiParam(value = "角色绑定资源")
    private List<Long> resourceJson;

    @ApiModelProperty(value = "描述")
    private String remark;

}
