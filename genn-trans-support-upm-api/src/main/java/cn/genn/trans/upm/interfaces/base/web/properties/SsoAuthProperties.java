package cn.genn.trans.upm.interfaces.base.web.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * sso-auth配置
 *
 * <AUTHOR>
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "genn.upm.auth", ignoreInvalidFields = true)
@Data
public class SsoAuthProperties {

    //权限校验默认白名单路径
    private List<String> defaultUris = new ArrayList<>(Arrays.asList("/sso/**", "/health/**", "/white/**"));

    private SsoAuthConfig session = new SsoAuthConfig();

    private SsoAuthConfig permission = new SsoAuthConfig();

    private String upmServerUrl;

    /**
     * 平台租户id
     */
    private Long platformTenantId = 1L;

    private SystemConfig system = new SystemConfig();

    @Data
    public static class SsoAuthConfig {
        private boolean enable = true;
        private String patterns = "/**";
        private List<String> containPatterns = new ArrayList<>();
        private List<String> excludePatterns = new ArrayList<>();
    }

    @Data
    public static class SystemConfig {
        /**
         * 超管系统id
         */
        private Long platformSystemId = 1L;

        /**
         * 运营商系统id
         */
        private Long operatorSystemId = 2L;

        /**
         * 承运商系统id
         */
        private Long carrierSystemId = 3L;

        /**
         * 开放平台系统id
         */
        private Long hubSystemId = 4L;

        /**
         * 客户系统id
         */
        private Long customerSystemId = 5L;

        /**
         * 默认司机系统id
         */
        private Long driveSystemId = 6L;

        /**
         * 保理系统
         */
        private Long fspSystemId = 7L;

    }

    public Long getPlatformSystemId() {
        return system.platformSystemId;
    }

    public Long getOperatorSystemId() {
        return system.operatorSystemId;
    }

    public Long getCarrierSystemId() {
        return system.carrierSystemId;
    }

    public Long getHubSystemId() {
        return system.hubSystemId;
    }

    public Long getCustomerSystemId() {
        return system.customerSystemId;
    }

    public Long getDriveSystemId() {
        return system.driveSystemId;
    }

    public Long getFspSystemId() {
        return system.fspSystemId;
    }


}
