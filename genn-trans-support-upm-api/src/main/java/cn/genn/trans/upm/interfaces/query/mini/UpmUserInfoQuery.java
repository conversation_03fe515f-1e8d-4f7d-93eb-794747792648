package cn.genn.trans.upm.interfaces.query.mini;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class UpmUserInfoQuery {

    @ApiModelProperty("用户名列表")
    @NotEmpty(message = "用户名列表不能为空")
    private List<String> usernameList;

    @ApiModelProperty("权限组key")
    @NotBlank(message = "权限组key不能为空")
    private String authKey;
}
