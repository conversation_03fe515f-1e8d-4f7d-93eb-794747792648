FROM genn-repository-cn-beijing.cr.volces.com/genn-ops/base/openjdk8:v1
USER root

WORKDIR /appdata

ENV JVM_ARGS="-server \
            -Xms128m \
            -Xmx256m \
            -XX:+UseG1GC \
            -XX:MaxGCPauseMillis=200 \
            -XX:+UseContainerSupport \
            -XX:+HeapDumpOnOutOfMemoryError \
            -XX:HeapDumpPath=./heap-dump.hprof \
            -XX:+PrintGCDetails \
            -XX:+PrintGCDateStamps \
            -Xloggc:./gc.log \
            -XX:+UseGCLogFileRotation \
            -XX:NumberOfGCLogFiles=10 \
            -XX:GCLogFileSize=100M \
            -Djava.awt.headless=true \
            -XX:+ExitOnOutOfMemoryError \
            -Dspring.cloud.nacos.config.server-addr=http://nacos.uat.genn.cn:8848 \
            -Dspring.cloud.nacos.config.namespace=uat \
            -Dserver.port=8080"
ENV SKY_AGENT="-javaagent:/appdata/skywalking-agent/skywalking-agent.jar -DSW_AGENT_NAME=uat-genn-trans-support-upm -DSW_AGENT_COLLECTOR_BACKEND_SERVICES=skywalking.genn.cn:11800"

COPY genn-trans-support-upm-service/target/genn-trans-support-upm.jar ./

CMD java $SKY_AGENT $JVM_ARGS -jar genn-trans-support-upm.jar
